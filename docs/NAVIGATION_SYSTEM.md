# 导航菜单系统文档

## 概述

本项目采用模块化的导航菜单系统，支持多级菜单、图标显示、状态管理、响应式布局和拖拽交互。基于 **shadcn/ui Sidebar** 组件构建，提供了灵活的配置和优秀的用户体验。

## 系统架构

```
src/navigation/sidebar/
└── sidebar-items.ts           # 导航配置文件

src/app/(main)/demo/_components/sidebar/
├── nav-main.tsx              # 主导航组件
├── nav-user.tsx              # 用户信息组件
├── nav-documents.tsx         # 文档导航组件
├── nav-secondary.tsx         # 次级导航组件
└── app-sidebar.tsx           # 侧边栏容器组件
```

## 核心配置 (`sidebar-items.ts`)

### 数据结构定义

```typescript
/**
 * 导航子项接口
 */
export interface NavSubItem {
  title: string;           // 显示标题
  url: string;            // 跳转链接
  icon?: LucideIcon;      // 图标组件（可选）
  comingSoon?: boolean;   // 即将推出标识
  newTab?: boolean;       // 是否新标签页打开
  isNew?: boolean;        // 新功能标识
}

/**
 * 导航主项接口
 */
export interface NavMainItem {
  title: string;           // 显示标题
  url: string;            // 跳转链接
  icon?: LucideIcon;      // 图标组件（可选）
  subItems?: NavSubItem[]; // 子菜单项
  comingSoon?: boolean;   // 即将推出标识
  newTab?: boolean;       // 是否新标签页打开
  isNew?: boolean;        // 新功能标识
}

/**
 * 导航分组接口
 */
export interface NavGroup {
  id: number;             // 唯一标识
  label?: string;         // 分组标签（可选）
  items: NavMainItem[];   // 菜单项列表
}
```

### 配置示例

```typescript
export const sidebarItems: NavGroup[] = [
  {
    id: 1,
    label: "导航菜单",
    items: [
      {
        title: "主页",
        url: "/demo/default",
        icon: LayoutDashboard,
      },
      {
        title: "示例",
        url: "/auth",
        icon: Fingerprint,
        subItems: [
          { 
            title: "测试", 
            url: "/demo/not-found", 
            newTab: false 
          },
        ],
      },
    ],
  },
];
```

## 主导航组件 (`nav-main.tsx`)

### 组件特性

- **响应式布局**：支持展开/折叠状态
- **多级菜单**：支持二级子菜单
- **状态管理**：自动高亮当前页面
- **交互反馈**：悬停效果和点击反馈
- **无障碍支持**：完整的 ARIA 属性

### 核心功能实现

#### 1. 活跃状态判断
```typescript
/**
 * 判断菜单项是否处于活跃状态
 * @param url - 菜单项URL
 * @param subItems - 子菜单项（可选）
 * @returns 是否活跃
 */
const isItemActive = (url: string, subItems?: NavMainItem["subItems"]) => {
  if (subItems?.length) {
    // 如果有子菜单，检查任一子项是否匹配当前路径
    return subItems.some((sub) => path.startsWith(sub.url));
  }
  // 精确匹配当前路径
  return path === url;
};
```

#### 2. 子菜单展开状态
```typescript
/**
 * 判断子菜单是否应该展开
 * @param subItems - 子菜单项
 * @returns 是否展开
 */
const isSubmenuOpen = (subItems?: NavMainItem["subItems"]) => {
  return subItems?.some((sub) => path.startsWith(sub.url)) ?? false;
};
```

#### 3. 即将推出标识组件
```typescript
const IsComingSoon = () => (
  <span className="ml-auto rounded-md bg-gray-200 px-2 py-1 text-xs dark:text-gray-800">
    Soon
  </span>
);
```

### 展开状态渲染 (`NavItemExpanded`)

```typescript
const NavItemExpanded = ({ item, isActive, isSubmenuOpen }) => {
  return (
    <Collapsible 
      key={item.title} 
      asChild 
      defaultOpen={isSubmenuOpen(item.subItems)} 
      className="group/collapsible"
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          {item.subItems ? (
            // 有子菜单的项目 - 显示展开箭头
            <SidebarMenuButton
              disabled={item.comingSoon}
              isActive={isActive(item.url, item.subItems)}
              tooltip={item.title}
            >
              {item.icon && <item.icon />}
              <span>{item.title}</span>
              {item.comingSoon && <IsComingSoon />}
              <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            </SidebarMenuButton>
          ) : (
            // 无子菜单的项目 - 直接链接
            <SidebarMenuButton
              asChild
              aria-disabled={item.comingSoon}
              isActive={isActive(item.url)}
              tooltip={item.title}
            >
              <Link href={item.url} target={item.newTab ? "_blank" : undefined}>
                {item.icon && <item.icon />}
                <span>{item.title}</span>
                {item.comingSoon && <IsComingSoon />}
              </Link>
            </SidebarMenuButton>
          )}
        </CollapsibleTrigger>
        
        {/* 子菜单渲染 */}
        {item.subItems && (
          <CollapsibleContent>
            <SidebarMenuSub>
              {item.subItems.map((subItem) => (
                <SidebarMenuSubItem key={subItem.title}>
                  <SidebarMenuSubButton 
                    aria-disabled={subItem.comingSoon} 
                    isActive={isActive(subItem.url)} 
                    asChild
                  >
                    <Link href={subItem.url} target={subItem.newTab ? "_blank" : undefined}>
                      {subItem.icon && <subItem.icon />}
                      <span>{subItem.title}</span>
                      {subItem.comingSoon && <IsComingSoon />}
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        )}
      </SidebarMenuItem>
    </Collapsible>
  );
};
```

### 折叠状态渲染 (`NavItemCollapsed`)

```typescript
const NavItemCollapsed = ({ item, isActive }) => {
  return (
    <SidebarMenuItem key={item.title}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuButton
            disabled={item.comingSoon}
            tooltip={item.title}
            isActive={isActive(item.url, item.subItems)}
          >
            {item.icon && <item.icon />}
            <span>{item.title}</span>
            <ChevronRight />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        
        {/* 下拉菜单内容 */}
        <DropdownMenuContent className="w-50 space-y-1" side="right" align="start">
          {item.subItems?.map((subItem) => (
            <DropdownMenuItem key={subItem.title} asChild>
              <SidebarMenuSubButton
                key={subItem.title}
                asChild
                className="focus-visible:ring-0"
                aria-disabled={subItem.comingSoon}
                isActive={isActive(subItem.url)}
              >
                <Link href={subItem.url} target={subItem.newTab ? "_blank" : undefined}>
                  {subItem.icon && <subItem.icon className="[&>svg]:text-sidebar-foreground" />}
                  <span>{subItem.title}</span>
                  {subItem.comingSoon && <IsComingSoon />}
                </Link>
              </SidebarMenuSubButton>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  );
};
```

## 响应式行为

### 桌面端展开状态
- 显示完整菜单结构
- 支持子菜单折叠/展开动画
- 显示所有文本和图标

### 桌面端折叠状态
- 仅显示图标和工具提示
- 子菜单通过下拉菜单显示
- 节省屏幕空间

### 移动端适配
- 自动使用展开布局
- 触摸友好的交互
- 优化的间距和尺寸

## 状态管理

### 侧边栏状态
```typescript
const { state, isMobile } = useSidebar();

// state: "expanded" | "collapsed"
// isMobile: boolean
```

### 路径状态
```typescript
const path = usePathname();
// 用于判断当前活跃菜单项
```

## 样式系统

### CSS 类名约定
```css
/* 折叠动画 */
.group/collapsible .transition-transform.duration-200.group-data-[state=open]/collapsible:rotate-90

/* 活跃状态 */
.data-[state=open]:bg-sidebar-accent
.data-[state=open]:text-sidebar-accent-foreground

/* 悬停效果 */
.hover:bg-sidebar-accent
.hover:text-sidebar-accent-foreground
```

### 主题变量
```css
:root {
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
}
```

## 使用示例

### 基础配置
```typescript
// 1. 定义导航配置
const myNavItems: NavGroup[] = [
  {
    id: 1,
    label: "主要功能",
    items: [
      {
        title: "仪表板",
        url: "/dashboard",
        icon: LayoutDashboard,
      },
      {
        title: "用户管理",
        url: "/users",
        icon: Users,
        subItems: [
          { title: "用户列表", url: "/users/list" },
          { title: "角色管理", url: "/users/roles" },
        ],
      },
    ],
  },
];

// 2. 在组件中使用
<NavMain items={myNavItems} />
```

### 高级配置
```typescript
const advancedNavItems: NavGroup[] = [
  {
    id: 1,
    items: [
      {
        title: "新功能",
        url: "/new-feature",
        icon: Sparkles,
        isNew: true,           // 显示 "新" 标识
      },
      {
        title: "即将推出",
        url: "/coming-soon",
        icon: Clock,
        comingSoon: true,      // 显示 "Soon" 标识并禁用
      },
      {
        title: "外部链接",
        url: "https://example.com",
        icon: ExternalLink,
        newTab: true,          // 新标签页打开
      },
    ],
  },
];
```

## 最佳实践

### 1. 菜单结构设计
```typescript
// ✅ 推荐：逻辑分组
const menuGroups = [
  {
    id: 1,
    label: "核心功能",
    items: [/* 主要功能 */],
  },
  {
    id: 2,
    label: "系统管理",
    items: [/* 管理功能 */],
  },
];

// ❌ 避免：过深的嵌套
// 最多支持二级菜单
```

### 2. 图标使用
```typescript
// ✅ 推荐：使用语义化图标
import { LayoutDashboard, Users, Settings } from "lucide-react";

// ✅ 推荐：保持图标风格一致
// 统一使用 lucide-react 图标库
```

### 3. URL 设计
```typescript
// ✅ 推荐：RESTful 风格
{
  title: "用户管理",
  url: "/users",
  subItems: [
    { title: "用户列表", url: "/users" },
    { title: "添加用户", url: "/users/create" },
    { title: "用户角色", url: "/users/roles" },
  ],
}

// ❌ 避免：不一致的 URL 结构
```

### 4. 状态管理
```typescript
// ✅ 推荐：合理使用状态标识
{
  title: "Beta 功能",
  url: "/beta-feature",
  comingSoon: true,    // 明确标识状态
}

// ✅ 推荐：新功能突出显示
{
  title: "AI 助手",
  url: "/ai-assistant",
  isNew: true,         // 吸引用户注意
}
```

## 扩展功能

### 自定义状态标识
```typescript
// 扩展接口支持更多状态
interface ExtendedNavItem extends NavMainItem {
  badge?: {
    text: string;
    variant: "default" | "success" | "warning" | "destructive";
  };
}

// 自定义渲染组件
const CustomBadge = ({ badge }: { badge: ExtendedNavItem["badge"] }) => (
  <Badge variant={badge?.variant}>{badge?.text}</Badge>
);
```

### 权限控制集成
```typescript
// 结合权限系统
const filteredItems = sidebarItems.map(group => ({
  ...group,
  items: group.items.filter(item => 
    hasPermission(user.permissions, item.requiredPermission)
  ),
}));
```

### 搜索功能
```typescript
// 菜单搜索过滤
const searchableItems = useMemo(() => 
  sidebarItems.flatMap(group => 
    group.items.flatMap(item => [
      item,
      ...(item.subItems || [])
    ])
  ), [sidebarItems]
);
```

## 性能优化

### 1. 记忆化配置
```typescript
// 避免重复渲染
const memoizedItems = useMemo(() => sidebarItems, []);
```

### 2. 懒加载图标
```typescript
// 动态导入图标
const IconComponent = lazy(() => import(`lucide-react`).then(mod => ({ 
  default: mod[iconName] 
})));
```

### 3. 虚拟滚动
```typescript
// 大量菜单项时使用虚拟滚动
import { FixedSizeList as List } from 'react-window';
```

---

**导航菜单系统总结**：这套导航系统提供了完整的菜单管理功能，支持多级结构、响应式布局、状态管理和丰富的交互效果。通过配置驱动的方式，可以轻松管理复杂的导航结构，同时保持良好的用户体验和开发体验。