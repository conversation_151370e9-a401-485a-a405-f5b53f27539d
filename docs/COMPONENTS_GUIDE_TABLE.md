# 组件库完整说明文档

## 概述

本项目基于 **shadcn/ui** 构建了完整的组件库，包含 40+ 个高质量 UI 组件。采用 **Tailwind CSS v4**、**TypeScript** 和 **Radix UI** 开发，支持主题切换、响应式设计和无障碍访问。

## 目录结构

```
src/components/
├── ui/                          # shadcn/ui 基础组件 (40+ 组件)
│   ├── button.tsx              # 按钮组件
│   ├── table.tsx               # 基础表格组件
│   └── ...                     # 其他 UI 组件
└── data-table/                 # 高级数据表格组件
    ├── data-table.tsx          # 主数据表格组件
    ├── data-table-pagination.tsx    # 分页组件
    ├── data-table-view-options.tsx  # 视图选项组件
    ├── data-table-column-header.tsx # 列头组件
    ├── draggable-row.tsx       # 可拖拽行组件
    ├── drag-column.tsx         # 拖拽列组件
    └── table-utils.ts          # 表格工具函数
```

## 数据表格组件系统

### 1. **DataTable 主组件** (`data-table.tsx`)

**功能特性**：
- 基于 `@tanstack/react-table` 构建
- 支持拖拽排序（可选）
- 完全可定制的列定义
- 响应式设计
- 支持数据重新排序回调

**核心 API**：
```tsx
interface DataTableProps<TData, TValue> {
  table: TanStackTable<TData>           // React Table 实例
  columns: ColumnDef<TData, TValue>[]   // 列定义
  dndEnabled?: boolean                  // 是否启用拖拽
  onReorder?: (newData: TData[]) => void // 重排序回调
}
```

**使用示例**：
```tsx
import { DataTable } from "@/components/data-table/data-table"
import { useDataTableInstance } from "@/hooks/use-data-table-instance"

function MyTable() {
  const [data, setData] = useState(initialData)
  const table = useDataTableInstance({ 
    data, 
    columns: myColumns,
    getRowId: (row) => row.id.toString()
  })

  return (
    <DataTable 
      table={table} 
      columns={myColumns}
      dndEnabled={true}
      onReorder={setData}
    />
  )
}
```

### 2. **DataTablePagination 分页组件** (`data-table-pagination.tsx`)

**功能特性**：
- 显示选中行数统计
- 每页行数选择器
- 页码导航按钮
- 响应式布局（移动端隐藏部分功能）

**使用示例**：
```tsx
<DataTablePagination table={table} />
```

**功能详情**：
- **行数统计**：显示 "X of Y row(s) selected"
- **页面大小选择**：支持 10, 20, 30, 40, 50 行/页
- **导航按钮**：首页、上一页、下一页、末页
- **页码显示**：当前页/总页数

### 3. **DataTableViewOptions 视图选项** (`data-table-view-options.tsx`)

**功能特性**：
- 列可见性切换
- 下拉菜单形式
- 自动过滤可隐藏的列
- 响应式显示（小屏幕隐藏）

**使用示例**：
```tsx
<DataTableViewOptions table={table} />
```

**实现细节**：
```tsx
// 自动过滤可切换的列
table
  .getAllColumns()
  .filter((column) => 
    typeof column.accessorFn !== "undefined" && 
    column.getCanHide()
  )
  .map((column) => (
    <DropdownMenuCheckboxItem
      key={column.id}
      checked={column.getIsVisible()}
      onCheckedChange={(value) => column.toggleVisibility(!!value)}
    >
      {column.id}
    </DropdownMenuCheckboxItem>
  ))
```

### 4. **DataTableColumnHeader 列头组件** (`data-table-column-header.tsx`)

**功能特性**：
- 支持排序功能（升序/降序）
- 列隐藏功能
- 下拉菜单操作
- 排序状态图标显示

**使用示例**：
```tsx
// 在列定义中使用
{
  accessorKey: "name",
  header: ({ column }) => (
    <DataTableColumnHeader column={column} title="姓名" />
  ),
}
```

**排序图标逻辑**：
```tsx
function getSortIcon(sort: "asc" | "desc" | false | undefined) {
  switch (sort) {
    case "desc": return <ArrowDown />
    case "asc": return <ArrowUp />
    default: return <ChevronsUpDown />
  }
}
```

### 5. **拖拽功能组件**

#### DraggableRow 可拖拽行 (`draggable-row.tsx`)
```tsx
// 基于 @dnd-kit/sortable 实现
export function DraggableRow<TData>({ row }: { row: Row<TData> }) {
  const { transform, transition, setNodeRef, isDragging } = useSortable({
    id: (row.original as { id: number }).id,
  })
  
  return (
    <TableRow
      ref={setNodeRef}
      data-dragging={isDragging}
      style={{
        transform: CSS.Transform.toString(transform),
        transition: transition,
      }}
    >
      {/* 渲染单元格 */}
    </TableRow>
  )
}
```

#### DragColumn 拖拽列 (`drag-column.tsx`)
```tsx
// 提供拖拽手柄的列定义
export const dragColumn: ColumnDef<any> = {
  id: "drag",
  header: () => null,
  cell: ({ row }) => <DragHandle id={row.original.id} />,
  enableSorting: false,
  enableHiding: false,
}

function DragHandle({ id }: { id: number }) {
  const { attributes, listeners } = useSortable({ id })
  
  return (
    <Button
      {...attributes}
      {...listeners}
      variant="ghost"
      size="icon"
    >
      <GripVertical className="size-3" />
    </Button>
  )
}
```

### 6. **工具函数** (`table-utils.ts`)

#### withDndColumn 工具函数
```tsx
// 自动添加拖拽列到列定义
export function withDndColumn<T>(columns: ColumnDef<T>[]): ColumnDef<T>[] {
  return [dragColumn as ColumnDef<T>, ...columns]
}

// 使用示例
const columns = withDndColumn(dashboardColumns)
```

## 完整使用示例

### 基础数据表格
```tsx
import { DataTable } from "@/components/data-table/data-table"
import { DataTablePagination } from "@/components/data-table/data-table-pagination"
import { DataTableViewOptions } from "@/components/data-table/data-table-view-options"
import { useDataTableInstance } from "@/hooks/use-data-table-instance"

function BasicDataTable() {
  const table = useDataTableInstance({
    data: myData,
    columns: myColumns,
    getRowId: (row) => row.id.toString(),
  })

  return (
    <div className="space-y-4">
      {/* 工具栏 */}
      <div className="flex items-center justify-between">
        <h2>数据表格</h2>
        <DataTableViewOptions table={table} />
      </div>
      
      {/* 表格 */}
      <div className="rounded-md border">
        <DataTable table={table} columns={myColumns} />
      </div>
      
      {/* 分页 */}
      <DataTablePagination table={table} />
    </div>
  )
}
```

### 支持拖拽的数据表格
```tsx
import { withDndColumn } from "@/components/data-table/table-utils"

function DraggableDataTable() {
  const [data, setData] = useState(initialData)
  const columns = withDndColumn(myColumns) // 添加拖拽列
  
  const table = useDataTableInstance({ 
    data, 
    columns,
    getRowId: (row) => row.id.toString()
  })

  return (
    <div className="rounded-md border">
      <DataTable 
        table={table} 
        columns={columns}
        dndEnabled={true}
        onReorder={setData} // 处理重排序
      />
    </div>
  )
}
```

### 复杂列定义示例
```tsx
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header"

const columns: ColumnDef<User>[] = [
  // 选择列
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  
  // 可排序列
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="姓名" />
    ),
    cell: ({ row }) => <div>{row.getValue("name")}</div>,
  },
  
  // 操作列
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>编辑</DropdownMenuItem>
          <DropdownMenuItem>删除</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ),
  },
]
```

## 技术特性

### 拖拽系统
- **库依赖**：`@dnd-kit/core`, `@dnd-kit/sortable`
- **拖拽策略**：垂直列表排序
- **碰撞检测**：`closestCenter`
- **约束修饰符**：`restrictToVerticalAxis`

### 表格状态管理
- **分页状态**：页码、页面大小
- **排序状态**：多列排序支持
- **过滤状态**：列过滤器
- **选择状态**：行选择管理
- **可见性状态**：列显示/隐藏

### 响应式特性
- **移动端适配**：隐藏非关键功能
- **触摸支持**：拖拽手势识别
- **断点响应**：不同屏幕尺寸的布局调整

## 性能优化

### 虚拟化支持
```tsx
// 大数据集可以结合虚拟化
import { useVirtualizer } from '@tanstack/react-virtual'

// 在 DataTable 中集成虚拟滚动
```

### 记忆化优化
```tsx
// 列定义记忆化
const columns = useMemo(() => myColumns, [])

// 数据处理记忆化
const processedData = useMemo(() => 
  data.map(processItem), [data]
)
```

## 最佳实践

### 1. 列定义规范
```tsx
// ✅ 推荐：使用 accessorKey
{
  accessorKey: "email",
  header: "邮箱",
}

// ✅ 推荐：复杂渲染使用 cell
{
  accessorKey: "status",
  header: "状态",
  cell: ({ row }) => (
    <Badge variant={getStatusVariant(row.original.status)}>
      {row.original.status}
    </Badge>
  ),
}
```

### 2. 性能优化
```tsx
// ✅ 推荐：稳定的 getRowId
const table = useDataTableInstance({
  data,
  columns,
  getRowId: (row) => row.id.toString(), // 稳定的 ID
})

// ✅ 推荐：记忆化列定义
const columns = useMemo(() => [...], [])
```

### 3. 类型安全
```tsx
// ✅ 推荐：强类型列定义
const columns: ColumnDef<User>[] = [
  {
    accessorKey: "name", // TypeScript 会验证 key 存在
    header: "姓名",
  },
]
```

---

**数据表格组件总结**：这套数据表格组件提供了企业级的表格功能，包括排序、分页、过滤、列管理和拖拽排序。基于 TanStack Table 构建，具有出色的性能和灵活性，适用于各种复杂的数据展示场景。