# 页面开发规范文档

## 概述

本文档定义了项目中所有新页面的开发规范，确保代码质量、结构一致性和可维护性。所有开发人员必须严格遵循本规范进行页面开发。

## 技术栈基础

- **框架**：Next.js 15 (App Router)
- **语言**：TypeScript (严格模式)
- **样式**：Tailwind CSS v4
- **组件库**：shadcn/ui + Radix UI
- **图标**：Lucide React
- **代码规范**：ESLint + Prettier

## 1. 页面目录结构规范

### 1.1 创建位置
**强制要求**：所有新页面必须在 `src/app/(main)/demo/` 目录下创建独立的子目录。

### 1.2 目录命名规则
- **命名格式**：使用 kebab-case 命名规则
- **命名示例**：
  - ✅ `user-management`
  - ✅ `data-analysis`
  - ✅ `quality-inspection`
  - ❌ `userManagement`
  - ❌ `DataAnalysis`
  - ❌ `quality_inspection`

### 1.3 标准目录结构
```
src/app/(main)/demo/[page-name]/
├── _components/              # 页面专用组件目录（必需）
│   ├── page-header.tsx      # 页面头部组件
│   ├── filter-section.tsx   # 筛选区域组件
│   ├── data-table.tsx       # 数据表格组件
│   ├── action-buttons.tsx   # 操作按钮组件
│   └── ...                  # 其他功能组件
├── page.tsx                 # 页面入口文件（必需）
├── loading.tsx              # 加载状态页面（可选）
├── error.tsx                # 错误处理页面（可选）
└── not-found.tsx            # 404页面（可选）
```

### 1.4 参考示例
参照现有的 `src/app/(main)/demo/default/` 目录结构：

```
_components/
├── feature-cards.tsx
├── hero-section.tsx
├── stats-section.tsx
└── ...
```

## 2. 组件拆分规范

### 2.1 强制拆分要求
- **页面复杂度控制**：单个 `page.tsx` 文件不得超过 100 行代码
- **组件化原则**：每个功能模块必须拆分为独立组件
- **目录隔离**：页面专用组件必须放置在对应的 `_components/` 目录中

### 2.2 组件命名规范
```typescript
// ✅ 正确示例
// 文件：user-table.tsx
export function UserTable() {
  return <div>...</div>
}

// 文件：filter-section.tsx  
export function FilterSection() {
  return <div>...</div>
}

// ❌ 错误示例
// 文件：userTable.tsx
export function userTable() {
  return <div>...</div>
}
```

### 2.3 组件职责划分
每个组件应遵循单一职责原则：

```typescript
// ✅ 推荐：职责明确的组件
export function PageHeader({ title, description }: PageHeaderProps) {
  return (
    <div className="mb-8">
      <h1 className="text-3xl font-bold">{title}</h1>
      <p className="text-muted-foreground">{description}</p>
    </div>
  )
}

// ❌ 避免：职责过多的组件
export function PageContent() {
  // 包含头部、筛选、表格、分页等多个功能
  return <div>...</div>
}
```

### 2.4 页面入口文件结构
```typescript
// src/app/(main)/demo/user-management/page.tsx
import { PageHeader } from "./_components/page-header"
import { FilterSection } from "./_components/filter-section"
import { UserTable } from "./_components/user-table"
import { ActionButtons } from "./_components/action-buttons"

export default function UserManagementPage() {
  return (
    <div className="container mx-auto p-6">
      <PageHeader 
        title="用户管理" 
        description="管理系统用户和权限设置" 
      />
      <FilterSection />
      <UserTable />
      <ActionButtons />
    </div>
  )
}
```

## 3. 原子组件使用规范

### 3.1 强制引用路径
所有基础 UI 组件必须从 `src/components/` 目录引用：

```typescript
// ✅ 正确引用
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

// ❌ 错误引用
import { Button } from "../../components/ui/button"
import Button from "@/components/ui/button"
```

### 3.2 组件优先级检查流程
开发新功能前，按以下顺序检查：

1. **检查现有组件**：查看 `src/components/ui/` 是否有可用组件
2. **shadcn/ui 组件**：检查是否可通过 CLI 添加
   ```bash
   npx shadcn@latest add [component-name]
   ```
3. **自定义组件**：如不存在，在 `src/components/ui/` 中创建

### 3.3 新组件创建规范
```typescript
// src/components/ui/custom-component.tsx
import * as React from "react"
import { cn } from "@/lib/utils"

export interface CustomComponentProps 
  extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "secondary"
  size?: "sm" | "md" | "lg"
}

const CustomComponent = React.forwardRef<
  HTMLDivElement,
  CustomComponentProps
>(({ className, variant = "default", size = "md", ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "base-styles",
        {
          "variant-styles": variant === "default",
          "size-styles": size === "md",
        },
        className
      )}
      {...props}
    />
  )
})
CustomComponent.displayName = "CustomComponent"

export { CustomComponent }
```

### 3.4 禁止行为
- ❌ 不得在页面中直接编写基础 UI 逻辑
- ❌ 不得重复实现已有的 UI 组件
- ❌ 不得绕过组件系统直接使用原生 HTML 元素（除非必要）

#### 3.4.1 强制引用路径
所有图表组件必须从项目的图表系统引用：

```typescript
// ✅ 正确引用
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartConfig } from "@/components/ui/chart"
// Recharts 组件通过 ChartContainer 内部使用，不需要直接导入

// ❌ 错误引用
import { AreaChart, Area, LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid } from "recharts"
import { ResponsiveContainer, Tooltip } from "recharts"
```

#### 3.4.2 标准图表实现模式

**配置定义**：
```typescript
const chartConfig = {
  dataKey1: {
    label: "显示名称",
    color: "var(--chart-1)",
  },
  dataKey2: {
    label: "显示名称",
    color: "var(--chart-2)",
  },
} satisfies ChartConfig
```

**基础结构**：
```typescript
// 在组件内部直接使用 Recharts 组件，但必须包裹在 ChartContainer 中
<ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
  <AreaChart
    accessibilityLayer
    data={chartData}
    margin={{
      left: 12,
      right: 12,
    }}
  >
    <CartesianGrid vertical={false} />
    <XAxis
      dataKey="date"
      tickLine={false}
      axisLine={false}
      tickMargin={8}
    />
    <ChartTooltip
      cursor={false}
      content={<ChartTooltipContent indicator="dot" />}
    />
    <Area
      dataKey="value"
      type="natural"
      fill="var(--color-value)"
      stroke="var(--color-value)"
      strokeWidth={2}
    />
  </AreaChart>
</ChartContainer>
```

**重要说明**：
- Recharts 组件（如 `AreaChart`、`LineChart` 等）可以在 `ChartContainer` 内部直接使用
- 但必须通过项目的 `ChartContainer` 包装器来管理样式和配置
- 不得绕过 `ChartContainer` 直接使用 `ResponsiveContainer`

#### 3.4.3 暗黑模式适配要求

**强制要求**：所有图表必须在暗黑模式下正常显示

```typescript
// ✅ 正确的颜色配置 - 使用 CSS 变量
const chartConfig = {
  dataKey: {
    label: "数据标签",
    color: "var(--chart-1)", // 自动适配暗黑模式
  },
} satisfies ChartConfig

// ✅ 正确的渐变定义
<defs>
  <linearGradient id="fillData" x1="0" y1="0" x2="0" y2="1">
    <stop offset="5%" stopColor="var(--color-dataKey)" stopOpacity={1.0} />
    <stop offset="95%" stopColor="var(--color-dataKey)" stopOpacity={0.1} />
  </linearGradient>
</defs>

// ❌ 错误的硬编码颜色
const chartConfig = {
  dataKey: {
    label: "数据标签",
    color: "#3b82f6", // 在暗黑模式下不可见
  },
}
```

**标准图表模板**：
```typescript
export function StandardChart() {
  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>图表标题</CardTitle>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
          <AreaChart data={chartData}>
            <defs>
              <linearGradient id="fillData" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="var(--color-dataKey)" stopOpacity={1.0} />
                <stop offset="95%" stopColor="var(--color-dataKey)" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("zh-CN", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("zh-CN", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey="dataKey"
              type="natural"
              fill="url(#fillData)"
              stroke="var(--color-dataKey)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
```

**验证清单**：
- [ ] 使用 `ChartContainer` 包装器
- [ ] 颜色配置使用 CSS 变量（`var(--chart-1)` 等）
- [ ] 在浅色和暗黑模式下都能正常显示
- [ ] 渐变定义使用 `var(--color-dataKey)` 格式
- [ ] 日期格式使用中文本地化
- [ ] 卡片容器添加 `@container/card` 类名

## 4. 页面类型一致性规范

### 4.1 列表页（List Pages）标准模板

```typescript
// 标准列表页结构
export default function ListPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面头部 */}
      <PageHeader title="列表标题" description="页面描述" />
      
      {/* 筛选区域 */}
      <Card>
        <CardContent className="p-6">
          <FilterSection />
        </CardContent>
      </Card>
      
      {/* 数据表格 */}
      <Card>
        <CardContent className="p-0">
          <DataTable />
        </CardContent>
      </Card>
      
      {/* 分页组件 */}
      <div className="flex justify-end">
        <Pagination />
      </div>
    </div>
  )
}
```

**必需组件**：
- `PageHeader` - 页面标题和描述
- `FilterSection` - 搜索和筛选功能
- `DataTable` - 数据展示表格
- `Pagination` - 分页导航

### 4.2 表单页（Form Pages）标准模板

```typescript
export default function FormPage() {
  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <PageHeader title="表单标题" description="表单说明" />
      
      <Card>
        <CardHeader>
          <CardTitle>基本信息</CardTitle>
        </CardHeader>
        <CardContent>
          <FormSection />
        </CardContent>
        <CardFooter className="flex justify-end space-x-4">
          <Button variant="outline">取消</Button>
          <Button type="submit">保存</Button>
        </CardFooter>
      </Card>
    </div>
  )
}
```

**必需组件**：
- `PageHeader` - 表单标题
- `FormSection` - 表单字段区域
- 操作按钮区域

### 4.3 详情页（Detail Pages）标准模板

```typescript
export default function DetailPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader title="详情标题" description="详情说明">
        <ActionButtons />
      </PageHeader>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <BasicInfoCard />
          <DetailInfoCard />
        </div>
        <div className="space-y-6">
          <StatusCard />
          <RelatedInfoCard />
        </div>
      </div>
    </div>
  )
}
```

### 4.4 统计分析页（Analytics Pages）标准模板

```typescript
export default function AnalyticsPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader title="统计分析" description="数据分析报告" />
      
      {/* 关键指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard />
        <MetricCard />
        <MetricCard />
        <MetricCard />
      </div>
      
      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard />
        <ChartCard />
      </div>
      
      {/* 数据表格 */}
      <Card>
        <CardHeader>
          <CardTitle>详细数据</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable />
        </CardContent>
      </Card>
    </div>
  )
}
```

### 4.5 仪表板页（Dashboard Pages）标准模板

```typescript
export default function DashboardPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader title="仪表板" description="系统概览" />
      
      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard />
        <StatsCard />
        <StatsCard />
        <StatsCard />
      </div>
      
      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <RecentActivityCard />
          <TrendChartCard />
        </div>
        <div className="space-y-6">
          <QuickActionsCard />
          <NotificationsCard />
        </div>
      </div>
    </div>
  )
}
```

## 5. 技术规范要求

### 5.1 TypeScript 规范

#### 5.1.1 组件类型定义
```typescript
// ✅ 推荐：完整的类型定义
interface PageHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode
  className?: string
}

export function PageHeader({ 
  title, 
  description, 
  children, 
  className 
}: PageHeaderProps) {
  return (
    <div className={cn("mb-8", className)}>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{title}</h1>
          {description && (
            <p className="text-muted-foreground mt-2">{description}</p>
          )}
        </div>
        {children}
      </div>
    </div>
  )
}
```

#### 5.1.2 数据类型定义
```typescript
// types/user.ts
export interface User {
  id: string
  name: string
  email: string
  role: UserRole
  status: UserStatus
  createdAt: Date
  updatedAt: Date
}

export type UserRole = "admin" | "user" | "moderator"
export type UserStatus = "active" | "inactive" | "pending"

// 页面中使用
interface UserTableProps {
  users: User[]
  onUserSelect: (user: User) => void
  loading?: boolean
}
```

### 5.2 样式系统规范

#### 5.2.1 Tailwind CSS 使用
```typescript
// ✅ 推荐：使用 Tailwind 类名
export function Card({ children, className }: CardProps) {
  return (
    <div className={cn(
      "rounded-lg border bg-card text-card-foreground shadow-sm",
      className
    )}>
      {children}
    </div>
  )
}

// ❌ 避免：内联样式
export function Card({ children }: CardProps) {
  return (
    <div style={{ 
      borderRadius: '8px', 
      border: '1px solid #e5e7eb',
      backgroundColor: 'white'
    }}>
      {children}
    </div>
  )
}
```

#### 5.2.2 CSS 变量使用
```typescript
// ✅ 使用项目定义的 CSS 变量
<div className="bg-background text-foreground border-border">
  <h1 className="text-primary">标题</h1>
  <p className="text-muted-foreground">描述</p>
</div>
```

#### 5.2.3 响应式设计
```typescript
// ✅ 移动端优先的响应式设计
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <Card />
  <Card />
  <Card />
</div>

// ✅ 响应式间距和尺寸
<div className="p-4 md:p-6 lg:p-8">
  <h1 className="text-xl md:text-2xl lg:text-3xl font-bold">
    响应式标题
  </h1>
</div>
```

### 5.3 代码质量规范

#### 5.3.1 ESLint 规则遵循
```typescript
// ✅ 正确的导入顺序
import { useState, useEffect } from "react"

import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

import { cn } from "@/lib/utils"

// ✅ 正确的文件命名（kebab-case）
// user-management.tsx
// data-table.tsx
// filter-section.tsx
```

#### 5.3.2 代码复杂度控制
```typescript
// ✅ 推荐：简单的组件函数
export function UserCard({ user }: UserCardProps) {
  const statusColor = getStatusColor(user.status)
  
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center space-x-4">
          <Avatar>
            <AvatarImage src={user.avatar} />
            <AvatarFallback>{user.name[0]}</AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold">{user.name}</h3>
            <Badge className={statusColor}>{user.status}</Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// 辅助函数单独定义
function getStatusColor(status: UserStatus): string {
  switch (status) {
    case "active": return "bg-green-100 text-green-800"
    case "inactive": return "bg-gray-100 text-gray-800"
    case "pending": return "bg-yellow-100 text-yellow-800"
    default: return "bg-gray-100 text-gray-800"
  }
}
```

### 5.4 性能优化规范

#### 5.4.1 组件记忆化
```typescript
// ✅ 适当使用 memo 优化
export const UserCard = memo(function UserCard({ user }: UserCardProps) {
  return (
    <Card>
      {/* 组件内容 */}
    </Card>
  )
})

// ✅ 回调函数记忆化
export function UserList({ users }: UserListProps) {
  const handleUserSelect = useCallback((user: User) => {
    // 处理用户选择
  }, [])
  
  return (
    <div>
      {users.map(user => (
        <UserCard 
          key={user.id} 
          user={user} 
          onSelect={handleUserSelect}
        />
      ))}
    </div>
  )
}
```

#### 5.4.2 懒加载和代码分割
```typescript
// ✅ 大型组件懒加载
const HeavyChart = lazy(() => import("./_components/heavy-chart"))

export default function AnalyticsPage() {
  return (
    <div>
      <PageHeader title="分析页面" />
      <Suspense fallback={<ChartSkeleton />}>
        <HeavyChart />
      </Suspense>
    </div>
  )
}
```

## 6. 开发流程规范

### 6.1 页面开发检查清单

开发新页面时，请按以下清单逐项检查：

#### 📁 目录结构
- [ ] 在 `src/app/(main)/demo/` 下创建 kebab-case 命名的目录
- [ ] 创建 `_components/` 子目录
- [ ] 创建 `page.tsx` 入口文件

#### 🧩 组件拆分
- [ ] `page.tsx` 文件不超过 100 行
- [ ] 功能模块已拆分为独立组件
- [ ] 组件放置在 `_components/` 目录中
- [ ] 组件命名遵循 PascalCase，文件名使用 kebab-case

#### 🎨 UI 组件使用
- [ ] 检查并使用现有的 `src/components/ui/` 组件
- [ ] 新组件通过 shadcn CLI 添加或在 `ui/` 目录创建
- [ ] 所有组件引用使用 `@/components/` 路径

#### 📝 TypeScript
- [ ] 所有组件都有完整的类型定义
- [ ] Props 接口定义清晰
- [ ] 无 TypeScript 错误和警告

#### 🎯 页面类型一致性
- [ ] 遵循对应页面类型的标准模板
- [ ] 布局和交互方式与同类页面一致
- [ ] 使用标准的组件组合模式

#### 🔧 代码质量
- [ ] 通过 ESLint 检查（`npm run lint`）
- [ ] 通过 Prettier 格式化（`npm run format`）
- [ ] 导入顺序正确，无尾随空格
- [ ] 函数复杂度控制在合理范围

#### 📱 响应式设计
- [ ] 移动端布局正常显示
- [ ] 桌面端布局美观实用
- [ ] 使用响应式 Tailwind 类名

### 6.2 代码审查要点

在代码审查时，重点关注以下方面：

1. **结构规范性**：目录结构、文件命名是否符合规范
2. **组件合理性**：组件拆分是否合理，职责是否单一
3. **类型完整性**：TypeScript 类型定义是否完整准确
4. **样式一致性**：是否遵循项目的设计系统
5. **性能考虑**：是否有不必要的重渲染或性能问题
6. **代码质量**：是否通过 ESLint 和 Prettier 检查

### 6.3 常见问题和解决方案

#### 问题1：组件过于复杂
**解决方案**：按功能职责进一步拆分，每个组件只负责一个明确的功能。

#### 问题2：样式不一致
**解决方案**：严格使用项目的 CSS 变量和 Tailwind 类名，参考现有页面的样式实现。

#### 问题3：TypeScript 类型错误
**解决方案**：为所有 Props 和状态定义明确的接口，使用项目中已定义的类型。

#### 问题4：响应式布局问题
**解决方案**：使用 Tailwind 的响应式前缀，遵循移动端优先的设计原则。

## 7. 示例页面实现

### 7.1 完整的用户管理页面示例

```typescript
// src/app/(main)/demo/user-management/page.tsx
import { PageHeader } from "./_components/page-header"
import { FilterSection } from "./_components/filter-section"
import { UserTable } from "./_components/user-table"
import { CreateUserDialog } from "./_components/create-user-dialog"

export default function UserManagementPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader 
        title="用户管理" 
        description="管理系统用户账户和权限设置"
      >
        <CreateUserDialog />
      </PageHeader>
      
      <FilterSection />
      <UserTable />
    </div>
  )
}
```

```typescript
// src/app/(main)/demo/user-management/_components/page-header.tsx
import { cn } from "@/lib/utils"

interface PageHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode
  className?: string
}

export function PageHeader({ 
  title, 
  description, 
  children, 
  className 
}: PageHeaderProps) {
  return (
    <div className={cn("flex items-center justify-between", className)}>
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        {description && (
          <p className="text-muted-foreground mt-2">{description}</p>
        )}
      </div>
      {children && (
        <div className="flex items-center space-x-4">
          {children}
        </div>
      )}
    </div>
  )
}
```

## 8. 总结

本规范文档确保了项目中所有页面的一致性、可维护性和高质量。开发人员必须严格遵循以下核心原则：

1. **结构化开发**：标准的目录结构和组件拆分
2. **类型安全**：完整的 TypeScript 类型定义
3. **设计一致性**：遵循项目的设计系统和 UI 规范
4. **代码质量**：通过 ESLint 和 Prettier 保证代码质量
5. **性能优化**：合理的组件设计和性能考虑

遵循本规范将确保项目的长期可维护性和团队协作效率。如有疑问或需要补充，请及时反馈和更新本文档。

---

**文档版本**：v1.0  
**最后更新**：2024年12月  
**维护者**：开发团队



