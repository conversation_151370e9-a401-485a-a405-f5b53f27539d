# 组件库完整说明文档

## 概述

本项目基于 **shadcn/ui** 构建了完整的组件库，包含 40+ 个高质量 UI 组件。采用 **Tailwind CSS v4**、**TypeScript** 和 **Radix UI** 开发，支持主题切换、响应式设计和无障碍访问。

## 目录结构

```
src/components/ui/
├── alert-dialog.tsx            # 警告对话框
├── alert.tsx                   # 警告提示
├── avatar.tsx                  # 头像组件
├── badge.tsx                   # 徽章标签
├── breadcrumb.tsx              # 面包屑导航
├── button.tsx                  # 按钮组件
├── calendar.tsx                # 日历组件
├── card.tsx                    # 卡片容器
├── carousel.tsx                # 轮播组件
├── chart.tsx                   # 图表组件
├── checkbox.tsx                # 复选框
├── collapsible.tsx             # 折叠面板
├── command.tsx                 # 命令面板
├── context-menu.tsx            # 右键菜单
├── dialog.tsx                  # 对话框
├── drawer.tsx                  # 抽屉组件
├── dropdown-menu.tsx           # 下拉菜单
├── form.tsx                    # 表单组件
├── hover-card.tsx              # 悬浮卡片
├── input.tsx                   # 输入框
├── label.tsx                   # 标签组件
├── menubar.tsx                 # 菜单栏
├── navigation-menu.tsx         # 导航菜单
├── pagination.tsx              # 分页组件
├── popover.tsx                 # 弹出框
├── progress.tsx                # 进度条
├── radio-group.tsx             # 单选按钮组
├── resizable.tsx               # 可调整大小面板
├── scroll-area.tsx             # 滚动区域
├── select.tsx                  # 选择器
├── separator.tsx               # 分隔线
├── sheet.tsx                   # 侧边面板
├── sidebar.tsx                 # 侧边栏
├── skeleton.tsx                # 骨架屏
├── slider.tsx                  # 滑块组件
├── sonner.tsx                  # 通知组件
├── switch.tsx                  # 开关组件
├── table.tsx                   # 表格组件
├── tabs.tsx                    # 标签页
├── textarea.tsx                # 文本域
├── toast.tsx                   # 提示消息
├── toggle-group.tsx            # 切换按钮组
├── toggle.tsx                  # 切换按钮
└── tooltip.tsx                 # 工具提示
```

## 组件分类

### 1. **基础组件 (Foundation)**

#### Button 按钮组件
```tsx
// 变体：default, destructive, outline, secondary, ghost, link
// 尺寸：default, sm, lg, icon
<Button variant="default" size="lg">点击按钮</Button>
<Button asChild><Link href="/demo">链接按钮</Link></Button>
```

#### Input 输入框
```tsx
<Input type="email" placeholder="请输入邮箱" />
<Input disabled placeholder="禁用状态" />
```

#### Textarea 文本域
```tsx
<Textarea placeholder="请输入多行文本..." />
```

#### Label 标签
```tsx
<Label htmlFor="email">邮箱地址</Label>
```

### 2. **表单组件 (Form Controls)**

#### Checkbox 复选框
```tsx
<Checkbox id="terms" />
<Label htmlFor="terms">同意条款</Label>
```

#### Radio Group 单选按钮组
```tsx
<RadioGroup defaultValue="option1">
  <RadioGroupItem value="option1" id="r1" />
  <Label htmlFor="r1">选项 1</Label>
</RadioGroup>
```

#### Switch 开关
```tsx
<Switch checked={enabled} onCheckedChange={setEnabled} />
```

#### Select 选择器
```tsx
<Select>
  <SelectTrigger>
    <SelectValue placeholder="请选择..." />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">选项 1</SelectItem>
  </SelectContent>
</Select>
```

#### Slider 滑块
```tsx
<Slider defaultValue={[50]} max={100} step={1} />
```

#### Form 表单
```tsx
<Form {...form}>
  <FormField
    control={form.control}
    name="username"
    render={({ field }) => (
      <FormItem>
        <FormLabel>用户名</FormLabel>
        <FormControl>
          <Input {...field} />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
</Form>
```

### 3. **导航组件 (Navigation)**

#### Sidebar 侧边栏
```tsx
<SidebarProvider>
  <Sidebar variant="sidebar" collapsible="icon">
    <SidebarHeader>头部内容</SidebarHeader>
    <SidebarContent>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton>菜单项</SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarContent>
  </Sidebar>
</SidebarProvider>
```

#### Navigation Menu 导航菜单
```tsx
<NavigationMenu>
  <NavigationMenuList>
    <NavigationMenuItem>
      <NavigationMenuTrigger>产品</NavigationMenuTrigger>
      <NavigationMenuContent>
        <NavigationMenuLink>产品 1</NavigationMenuLink>
      </NavigationMenuContent>
    </NavigationMenuItem>
  </NavigationMenuList>
</NavigationMenu>
```

#### Breadcrumb 面包屑
```tsx
<Breadcrumb>
  <BreadcrumbList>
    <BreadcrumbItem>
      <BreadcrumbLink href="/">首页</BreadcrumbLink>
    </BreadcrumbItem>
    <BreadcrumbSeparator />
    <BreadcrumbItem>
      <BreadcrumbPage>当前页</BreadcrumbPage>
    </BreadcrumbItem>
  </BreadcrumbList>
</Breadcrumb>
```

#### Pagination 分页
```tsx
<Pagination>
  <PaginationContent>
    <PaginationItem>
      <PaginationPrevious href="#" />
    </PaginationItem>
    <PaginationItem>
      <PaginationLink href="#">1</PaginationLink>
    </PaginationItem>
    <PaginationItem>
      <PaginationNext href="#" />
    </PaginationItem>
  </PaginationContent>
</Pagination>
```

### 4. **反馈组件 (Feedback)**

#### Alert 警告提示
```tsx
<Alert>
  <AlertCircle className="h-4 w-4" />
  <AlertTitle>注意</AlertTitle>
  <AlertDescription>这是一条重要提示信息。</AlertDescription>
</Alert>
```

#### Toast 提示消息
```tsx
const { toast } = useToast()

toast({
  title: "成功",
  description: "操作已完成",
})
```

#### Sonner 通知
```tsx
import { toast } from "sonner"

toast("操作成功")
toast.error("操作失败")
toast.success("保存成功")
```

#### Progress 进度条
```tsx
<Progress value={33} className="w-[60%]" />
```

#### Skeleton 骨架屏
```tsx
<Skeleton className="h-4 w-[250px]" />
<Skeleton className="h-4 w-[200px]" />
```

### 5. **覆盖层组件 (Overlay)**

#### Dialog 对话框
```tsx
<Dialog>
  <DialogTrigger asChild>
    <Button>打开对话框</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>标题</DialogTitle>
      <DialogDescription>描述内容</DialogDescription>
    </DialogHeader>
  </DialogContent>
</Dialog>
```

#### Alert Dialog 警告对话框
```tsx
<AlertDialog>
  <AlertDialogTrigger asChild>
    <Button variant="destructive">删除</Button>
  </AlertDialogTrigger>
  <AlertDialogContent>
    <AlertDialogHeader>
      <AlertDialogTitle>确认删除？</AlertDialogTitle>
      <AlertDialogDescription>此操作无法撤销</AlertDialogDescription>
    </AlertDialogHeader>
    <AlertDialogFooter>
      <AlertDialogCancel>取消</AlertDialogCancel>
      <AlertDialogAction>确认</AlertDialogAction>
    </AlertDialogFooter>
  </AlertDialogContent>
</AlertDialog>
```

#### Sheet 侧边面板
```tsx
<Sheet>
  <SheetTrigger asChild>
    <Button>打开面板</Button>
  </SheetTrigger>
  <SheetContent>
    <SheetHeader>
      <SheetTitle>面板标题</SheetTitle>
    </SheetHeader>
  </SheetContent>
</Sheet>
```

#### Drawer 抽屉
```tsx
<Drawer>
  <DrawerTrigger asChild>
    <Button>打开抽屉</Button>
  </DrawerTrigger>
  <DrawerContent>
    <DrawerHeader>
      <DrawerTitle>抽屉标题</DrawerTitle>
    </DrawerHeader>
  </DrawerContent>
</Drawer>
```

#### Popover 弹出框
```tsx
<Popover>
  <PopoverTrigger asChild>
    <Button>打开弹出框</Button>
  </PopoverTrigger>
  <PopoverContent>弹出内容</PopoverContent>
</Popover>
```

#### Tooltip 工具提示
```tsx
<TooltipProvider>
  <Tooltip>
    <TooltipTrigger asChild>
      <Button>悬停查看提示</Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>这是提示内容</p>
    </TooltipContent>
  </Tooltip>
</TooltipProvider>
```

#### Hover Card 悬浮卡片
```tsx
<HoverCard>
  <HoverCardTrigger asChild>
    <Button variant="link">@用户名</Button>
  </HoverCardTrigger>
  <HoverCardContent>
    <div>用户详细信息</div>
  </HoverCardContent>
</HoverCard>
```

### 6. **菜单组件 (Menu)**

#### Dropdown Menu 下拉菜单
```tsx
<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button>打开菜单</Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem>菜单项 1</DropdownMenuItem>
    <DropdownMenuSeparator />
    <DropdownMenuItem>菜单项 2</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

#### Context Menu 右键菜单
```tsx
<ContextMenu>
  <ContextMenuTrigger>右键点击</ContextMenuTrigger>
  <ContextMenuContent>
    <ContextMenuItem>复制</ContextMenuItem>
    <ContextMenuItem>粘贴</ContextMenuItem>
  </ContextMenuContent>
</ContextMenu>
```

#### Menubar 菜单栏
```tsx
<Menubar>
  <MenubarMenu>
    <MenubarTrigger>文件</MenubarTrigger>
    <MenubarContent>
      <MenubarItem>新建</MenubarItem>
      <MenubarItem>打开</MenubarItem>
    </MenubarContent>
  </MenubarMenu>
</Menubar>
```

### 7. **数据展示组件 (Data Display)**

#### Table 表格
```tsx
<Table>
  <TableHeader>
    <TableRow>
      <TableHead>姓名</TableHead>
      <TableHead>邮箱</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    <TableRow>
      <TableCell>张三</TableCell>
      <TableCell><EMAIL></TableCell>
    </TableRow>
  </TableBody>
</Table>
```

#### Card 卡片
```tsx
<Card>
  <CardHeader>
    <CardTitle>卡片标题</CardTitle>
    <CardDescription>卡片描述</CardDescription>
  </CardHeader>
  <CardContent>
    <p>卡片内容</p>
  </CardContent>
  <CardFooter>
    <Button>操作按钮</Button>
  </CardFooter>
</Card>
```

#### Avatar 头像
```tsx
<Avatar>
  <AvatarImage src="/avatar.jpg" alt="用户头像" />
  <AvatarFallback>张三</AvatarFallback>
</Avatar>
```

#### Badge 徽章
```tsx
<Badge variant="default">默认</Badge>
<Badge variant="secondary">次要</Badge>
<Badge variant="destructive">危险</Badge>
<Badge variant="outline">轮廓</Badge>
```

#### Chart 图表
```tsx
<ChartContainer config={chartConfig}>
  <BarChart data={data}>
    <CartesianGrid strokeDasharray="3 3" />
    <XAxis dataKey="name" />
    <YAxis />
    <ChartTooltip content={<ChartTooltipContent />} />
    <Bar dataKey="value" fill="var(--color-bar)" />
  </BarChart>
</ChartContainer>
```

### 8. **布局组件 (Layout)**

#### Tabs 标签页
```tsx
<Tabs defaultValue="tab1">
  <TabsList>
    <TabsTrigger value="tab1">标签 1</TabsTrigger>
    <TabsTrigger value="tab2">标签 2</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">内容 1</TabsContent>
  <TabsContent value="tab2">内容 2</TabsContent>
</Tabs>
```

#### Collapsible 折叠面板
```tsx
<Collapsible>
  <CollapsibleTrigger asChild>
    <Button>展开/折叠</Button>
  </CollapsibleTrigger>
  <CollapsibleContent>
    <div>折叠内容</div>
  </CollapsibleContent>
</Collapsible>
```

#### Separator 分隔线
```tsx
<Separator orientation="horizontal" />
<Separator orientation="vertical" />
```

#### Scroll Area 滚动区域
```tsx
<ScrollArea className="h-[200px] w-[350px]">
  <div>长内容...</div>
</ScrollArea>
```

#### Resizable 可调整大小面板
```tsx
<ResizablePanelGroup direction="horizontal">
  <ResizablePanel defaultSize={50}>
    <div>面板 1</div>
  </ResizablePanel>
  <ResizableHandle />
  <ResizablePanel defaultSize={50}>
    <div>面板 2</div>
  </ResizablePanel>
</ResizablePanelGroup>
```

### 9. **交互组件 (Interactive)**

#### Command 命令面板
```tsx
<Command>
  <CommandInput placeholder="搜索命令..." />
  <CommandList>
    <CommandEmpty>未找到结果</CommandEmpty>
    <CommandGroup heading="建议">
      <CommandItem>命令 1</CommandItem>
      <CommandItem>命令 2</CommandItem>
    </CommandGroup>
  </CommandList>
</Command>
```

#### Calendar 日历
```tsx
<Calendar
  mode="single"
  selected={date}
  onSelect={setDate}
  className="rounded-md border"
/>
```

#### Carousel 轮播
```tsx
<Carousel className="w-full max-w-xs">
  <CarouselContent>
    <CarouselItem>
      <Card>
        <CardContent>内容 1</CardContent>
      </Card>
    </CarouselItem>
    <CarouselItem>
      <Card>
        <CardContent>内容 2</CardContent>
      </Card>
    </CarouselItem>
  </CarouselContent>
  <CarouselPrevious />
  <CarouselNext />
</Carousel>
```

#### Toggle 切换按钮
```tsx
<Toggle aria-label="切换斜体">
  <Italic className="h-4 w-4" />
</Toggle>
```

#### Toggle Group 切换按钮组
```tsx
<ToggleGroup type="multiple">
  <ToggleGroupItem value="bold">
    <Bold className="h-4 w-4" />
  </ToggleGroupItem>
  <ToggleGroupItem value="italic">
    <Italic className="h-4 w-4" />
  </ToggleGroupItem>
</ToggleGroup>
```

## 设计系统

### 主题变量
```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}
```

### 响应式断点
```css
/* 移动端优先 */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
@media (min-width: 113rem) { /* 4xl - 项目自定义 */ }
```

## 最佳实践

### 1. 组件组合
```tsx
// ✅ 推荐：使用 asChild 模式
<Button asChild>
  <Link href="/demo">链接按钮</Link>
</Button>

// ✅ 推荐：组件组合
<Card>
  <CardHeader>
    <CardTitle>标题</CardTitle>
  </CardHeader>
  <CardContent>内容</CardContent>
</Card>
```

### 2. 类型安全
```tsx
interface CustomComponentProps extends React.ComponentProps<typeof Button> {
  customProp?: string
}

const CustomComponent: React.FC<CustomComponentProps> = ({ customProp, ...props }) => {
  return <Button {...props} />
}
```

### 3. 主题定制
```tsx
// 扩展主题变量
<Button className="bg-custom-color hover:bg-custom-color/90">
  自定义按钮
</Button>
```

### 4. 无障碍访问
```tsx
// 提供适当的 ARIA 属性
<Button aria-label="关闭对话框" aria-describedby="dialog-description">
  <X className="h-4 w-4" />
</Button>
```

## 维护指南

### 更新组件
```bash
# 更新单个组件
npx shadcn@latest add button --overwrite

# 更新所有组件
npx shadcn@latest add --all --overwrite
```

### 自定义组件
1. 基于现有组件扩展
2. 保持 API 一致性
3. 遵循设计系统规范
4. 添加适当的文档和示例

---

**总结**：本组件库提供了完整的 UI 组件生态系统，涵盖了现代 Web 应用的所有常见需求。所有组件都经过精心设计，支持主题切换、响应式布局和无障碍访问，为开发高质量的用户界面提供了坚实的基础。