# Demo 模式说明文档

## 概述

Demo 模式是基于 Dashboard 的精简版本，专为演示和原型展示而设计。它保留了核心的导航和布局功能，同时移除了复杂的用户管理和数据展示组件。

## 目录结构

```
src/app/(main)/demo/
├── layout.tsx                    # 简化布局
├── page.tsx                     # 根页面重定向
├── default/
│   └── page.tsx                 # 主页（极简版）
├── [...not-found]/
│   └── page.tsx                 # 404页面（中文化）
└── _components/
    └── sidebar/
        ├── app-sidebar.tsx      # 简化侧边栏
        ├── nav-documents.tsx    # 文档导航组件
        ├── nav-main.tsx         # 主导航组件
        ├── nav-user.tsx         # 用户导航组件
        └── search-dialog.tsx    # 搜索对话框组件
```

## 功能对比

### Dashboard vs Demo

| 功能模块 | Dashboard | Demo | 说明 |
|---------|-----------|------|------|
| **布局组件** |
| 搜索对话框 | ✅ | ❌ | 移除搜索功能 |
| 用户账户切换 | ✅ | ❌ | 移除用户管理 |
| 分隔线 | ✅ | ❌ | 简化视觉元素 |
| **侧边栏** |
| 主导航 | ✅ | ✅ | 保留核心导航 |
| 用户信息区域 | ✅ | ❌ | 移除用户信息 |
| 文档导航 | ✅ | ❌ | 注释掉文档功能 |
| 二级导航 | ✅ | ❌ | 注释掉辅助功能 |
| **页面内容** |
| 完整仪表板 | ✅ | ❌ | 仅显示文字内容 |
| 数据表格 | ✅ | ❌ | 移除数据展示 |
| 图表组件 | ✅ | ❌ | 移除可视化 |
| 卡片组件 | ✅ | ❌ | 移除统计卡片 |

## 主要简化内容

### 1. 布局简化 (`layout.tsx`)

```typescript
// 移除的功能
// <SearchDialog />
// <AccountSwitcher users={users} />
// <Separator />
```

**目的**：减少界面复杂度，专注于核心布局展示

### 2. 侧边栏精简 (`app-sidebar.tsx`)

```typescript
// 移除的功能
// <SidebarFooter>
//   <NavUser user={rootUser} />
// </SidebarFooter>
```

**目的**：去除用户相关功能，适合演示场景

### 3. 页面内容最小化

- **主页** (`default/page.tsx`)：仅显示 "主页" 文字
- **404页面**：中文化错误提示

## 使用场景

### 适用场景
- ✅ **产品演示**：向客户展示基础界面结构
- ✅ **原型验证**：快速验证布局和导航逻辑
- ✅ **开发测试**：测试基础功能而不被复杂数据干扰
- ✅ **培训教学**：简化界面便于学习理解

### 不适用场景
- ❌ **生产环境**：缺少完整功能
- ❌ **用户管理**：无用户相关功能
- ❌ **数据展示**：无图表和表格组件

## 路由配置

### 访问路径
- 主入口：`/demo` → 重定向到 `/demo/default`
- 主页：`/demo/default`
- 404页面：`/demo/not-found` 或任何不存在的 `/demo/*` 路径

### 导航配置
在 `src/navigation/sidebar/sidebar-items.ts` 中：

```typescript
{
  title: "示例",
  url: "/auth",
  icon: Fingerprint,
  subItems: [
    { title: "测试", url: "/demo/not-found", newTab: false },
  ],
}
```

## 技术特性

### 保留的核心功能
- ✅ **响应式布局**：完整的移动端适配
- ✅ **主题系统**：支持亮色/暗色主题切换
- ✅ **侧边栏状态**：支持折叠/展开
- ✅ **导航系统**：完整的路由导航
- ✅ **TypeScript**：完整的类型支持

### 移除的复杂功能
- ❌ **数据获取**：无 API 调用和数据处理
- ❌ **状态管理**：简化的状态逻辑
- ❌ **用户认证**：无登录和权限控制
- ❌ **数据可视化**：无图表和统计组件

## 开发建议

### 扩展 Demo 模式
1. **添加新页面**：在 `demo/` 目录下创建新的路由文件夹
2. **恢复功能**：取消注释需要的组件
3. **自定义内容**：替换占位文字为实际内容

### 从 Demo 到 Dashboard
1. **复制结构**：将 demo 页面复制到 dashboard 对应位置
2. **添加数据**：集成数据获取和状态管理
3. **恢复组件**：启用完整的 UI 组件

## 维护说明

- **同步更新**：Dashboard 的布局更改需要同步到 Demo
- **独立开发**：Demo 的简化逻辑不应影响 Dashboard
- **版本控制**：两个模式应保持功能对等的核心结构

---

**注意**：Demo 模式主要用于演示和开发测试，不建议直接用于生产环境。