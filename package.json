{"name": "studio-admin", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix"]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "eslint-plugin-unicorn": "^56.0.1", "initials": "^3.1.2", "input-otp": "^1.4.2", "lucide-react": "^0.453.0", "next": "^15.4.1", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "simple-icons": "^15.6.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.16.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.16.4", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/parser": "^8.26.0", "eslint": "^9.31.0", "eslint-config-next": "^15.4.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^3.0.4", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.15.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.5", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0"}}