# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

A modern Next.js 15 admin dashboard template built with TypeScript, Tailwind CSS v4, and Shadcn UI. Features colocation-based architecture, comprehensive component library, and production-ready tooling.

## Key Commands

### Development
- `npm run dev` - Start development server
- `npm run build` - Build production version
- `npm run start` - Start production server
- `npm run lint` - Run ESLint checks
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

### Component Management
- `npx shadcn@latest add [component]` - Add new shadcn/ui component
- `npx shadcn@latest add --help` - View available components

### Git Hooks
- Pre-commit runs `lint-staged` via Husky
- Staged files auto-format with <PERSON><PERSON><PERSON> and ESLint fix

## Architecture Overview

### Colocation File Structure
```
src/app/
├── (main)/              # Main application with auth
│   ├── dashboard/       # Dashboard pages
│   ├── auth/           # Authentication pages
│   └── demo/           # Demo mode pages
├── (external)/         # External pages (landing)
src/components/
├── ui/                 # Shadcn UI components (40+)
├── data-table/         # Advanced table components
└── simple-icon.tsx     # Icon wrapper
```

### Key Directories
- `src/app/(main)/` - Main authenticated application
- `src/components/ui/` - Reusable UI components (shadcn)
- `src/hooks/` - Custom React hooks
- `src/lib/` - Utility functions and helpers
- `src/stores/` - Zustand state management
- `src/types/` - TypeScript type definitions
- `src/config/` - App configuration
- `src/navigation/` - Navigation configuration

### Dashboard Pages Structure
Each dashboard follows colocation pattern:
- `dashboard/[type]/page.tsx` - Main page component
- `dashboard/[type]/_components/` - Page-specific components
- `dashboard/[type]/schema.ts` - Zod schemas
- `dashboard/[type]/columns.tsx` - Table columns (if applicable)

## Tech Stack

### Core
- **Next.js 15** with App Router
- **TypeScript** strict mode
- **Tailwind CSS v4** with CSS variables
- **React 19** with Server Components

### UI & Components
- **Shadcn UI** (40+ components)
- **Lucide React** icons
- **Radix UI** primitives
- **Embla Carousel** for carousels
- **Recharts** for charts

### Data Management
- **TanStack Table** for data tables
- **React Hook Form** for forms
- **Zod** for validation
- **Zustand** for state management
- **TanStack Query** for data fetching

### Development Tools
- **ESLint** with extensive rules (security, React, TypeScript)
- **Prettier** with Tailwind plugin
- **Husky** for Git hooks
- **Lint-staged** for pre-commit checks

## Theme System

### CSS Variables
Located in `src/app/globals.css` with CSS custom properties for:
- Primary/secondary colors
- Background colors
- Border radius
- Shadows and spacing

### Theme Files
- `src/styles/presets/` - Theme presets (brutalist, soft-pop, tangerine)
- Dark/light mode support via `next-themes`

## Authentication

### Auth Flow
- Middleware at `src/middleware/auth-middleware.ts`
- Cookie-based auth (`auth-token`)
- Demo mode accessible without auth
- Redirects: `/auth/login` → `/demo` on success

### Auth Pages
- `/auth/v1/login` - Version 1 login
- `/auth/v2/login` - Version 2 login (updated design)
- `/auth/v1/register` - Registration pages

## Demo Mode

### Features
- Simplified UI for client presentations
- `/demo` route (redirects to `/demo/default`)
- Chinese localization
- Reduced functionality for clean demos

## Code Standards

### ESLint Rules
- **File naming**: kebab-case for all files
- **Imports**: Ordered groups with newlines
- **Complexity**: Max 10 per function, 300 lines per file
- **TypeScript**: Strict null checks, no explicit any
- **React**: No nested components, proper context usage

### Prettier Config
- Semi-colons: always
- Single quotes: never
- Trailing commas: all
- Tab width: 2
- Print width: 120

### Git Workflow
1. Pre-commit runs lint-staged
2. ESLint fixes and Prettier formats staged files
3. TypeScript checks run via Next.js build

## Common Patterns

### Adding New Pages
1. Create folder in `src/app/(main)/[feature-name]/`
2. Add `page.tsx` for main component
3. Create `_components/` for page-specific components
4. Add schema files if using forms/tables

### Component Usage
- Use `src/components/ui/` for base components
- Import via `@/components/ui/[component]`
- Extend with variants using `class-variance-authority`

### Data Tables
- Use `src/components/data-table/` components
- Define columns in separate file
- Use Zod schemas for type safety
- Implement drag-and-drop with @dnd-kit

### State Management
- Global state: Zustand stores in `src/stores/`
- Local state: React hooks
- Server state: React Query for data fetching
- Preferences: Custom preferences store

## Browser Support
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Environment Setup

### Required Environment
- Node.js 18+ recommended
- npm (comes with Node.js)

### Initial Setup
```bash
npm install
npm run dev
```

### Build for Production
```bash
npm run build
npm run start
```