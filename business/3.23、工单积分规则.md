
---

### **“工单积分规则”页面内容详解**

#### **一、 页面核心目标**

1.  **定义积分事件**: 明确哪些员工行为可以获得或扣除积分。
2.  **量化积分值**: 为每种行为设定具体的加分或减分数值。
3.  **设定复杂条件**: 允许积分的获取与多种工单属性（如优先级、类型）挂钩，实现差异化激励。
4.  **管理规则生命周期**: 创建、编辑、启用/停用积分规则。
5.  **透明化规则**: 让管理者和员工都能清晰地了解积分的计算方式，确保公平性。

---

### **二、 页面内容与布局**

页面通常采用**规则列表 + 编辑/新建表单**的模式。由于规则之间通常是独立的，采用列表加弹窗或新页面的方式进行编辑即可。

#### **1. 规则列表区 (Rule List)**

*   **页面顶部**: 一个醒目的 `[ + 新建积分规则 ]` 按钮。
*   **搜索框**: 按规则名称搜索。
*   **规则列表**: 以表格形式展示所有已创建的积分规则。
    *   **表头**: `规则名称`, `事件类型`, `积分变化`, `条件摘要`, `状态 (启用/停用)`, `最后修改时间`, `操作`。
    *   **积分变化**: 用醒目的绿色（如 `+10`）和红色（如 `-5`）数字来表示加分和减分。
    *   **条件摘要**: 简要描述该规则生效的条件，如“仅限高优先级工单”。
    *   **行末操作**: `[ 编辑 ]`, `[ 复制 ]`, `[ 启用/停用 ]`, `[ 查看应用日志 ]`, `[ 删除 ]`。

---

#### **2. 规则构建器/编辑器 (Rule Builder/Editor)**

点击“新建”或“编辑”后，会进入一个功能强大的表单页面，其逻辑结构与“自动化规则”页面非常相似，都是**“事件-条件-结果”**的三段式。

**1. 规则基本信息区**

*   **规则名称**: [必填] 如“按时解决高优工单加分”、“超时扣分”。
*   **规则描述**: 解释此规则的激励目的。
*   **状态**: `[ 启用 ]` / `[ 停用 ]` 的开关。

**2. “当……” - 积分事件定义区 (Events)**

这一步定义了**“什么行为会触发积分计算”**。

*   **事件触发器 (Event Trigger)**: 一个下拉菜单，选择一个核心的员工行为事件。
    *   `当处理人【解决】工单时`
    *   `当处理人【关闭】工单时` (由回访员确认关闭)
    *   `当工单获得【客户评价】时`
    *   `当工单【SLA状态】变更时` (如变为“已超时”)
    *   `当处理人获得【客户点名表扬】标签时`
    *   `当处理人【首次响应】工单时`

**3. “如果……” - 附加条件区 (Conditions)**

这一步让积分规则变得**精细和公平**。可以使用一个强大的**条件构建器**。

*   **条件逻辑**: **“满足所有条件 (AND)”** 或 **“满足任意条件 (OR)”**。
*   **条件构建**: 每一条条件都是一个 **`[ 字段 ]` `[ 操作符 ]` `[ 值 ]`** 的组合。
*   **示例**:
    *   **场景1：解决高优工单**
        *   `[优先级]` `等于` `紧急`
    *   **场景2：解决时长特别短**
        *   `[解决时长(小时)]` `小于` `1`
    *   **场景3：获得五星好评**
        *   `[评价分数]` `等于` `5`

**4. “那么……” - 积分计算区 (Points Calculation)**

这一步定义了**具体的积分变化**。

*   **积分操作**:
    *   `增加积分` 或 `减少积分` (单选)
*   **积分值**:
    *   **固定值**: 直接输入一个数字，如 `10`。
    *   **动态值/公式 (高级功能)**:
        *   允许输入一个简单的计算公式，使用工单中的变量。
        *   例如，对于“超时扣分”，可以设置为 `扣除积分 = 2 * {{work_order.overdue_hours}}` (每超时1小时扣2分)。
        *   例如，对于“解决难题”，可以设置为 `增加积分 = 5 + {{work_order.difficulty_level}}` (基础分+难度分)。

---

### **已配置规则的示例**

1.  **规则名称**: 基础解决分
    *   **事件**: 当处理人【解决】工单时
    *   **条件**: (无)
    *   **积分**: `增加积分` `5`
2.  **规则名称**: 高优先级额外奖励
    *   **事件**: 当处理人【解决】工单时
    *   **条件**: `[优先级]` `等于` `紧急` **OR** `[优先级]` `等于` `高`
    *   **积分**: `增加积分` `10`
3.  **规则名称**: 客户五星好评奖励
    *   **事件**: 当工单获得【客户评价】时
    *   **条件**: `[评价分数]` `等于` `5`
    *   **积分**: `增加积分` `20`
4.  **规则名称**: SLA解决超时惩罚
    *   **事件**: 当工单【SLA状态】变更时
    *   **条件**: `[解决SLA状态]` `变更为` `已超时`
    *   **积分**: `减少积分` `15`

当一个高优先级的工单被按时解决，并获得了五星好评时，处理人就可以通过这套规则自动获得 `5 + 10 + 20 = 35` 分。

---