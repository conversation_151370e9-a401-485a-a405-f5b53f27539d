
---

### **“工单总体统计”页面内容详解**

#### **一、 页面核心目标**

1.  **多维度数据探索**: 允许用户从不同维度（时间、部门、客户、类型等）交叉分析工单数据。
2.  **数据对比与关联分析**: 能够方便地对比不同时间段、不同团队之间的数据差异，发现数据间的关联性。
3.  **灵活的视图切换**: 用户可以根据自己的分析需求，改变图表的展现形式或分析的维度。
4.  **数据导出与分享**: 支持将分析结果导出为图表或数据，用于制作更详细的报告。

---

### **二、 页面内容与布局**

页面通常采用**“筛选器区 + 多个可交互图表卡片”**的布局。核心特点是，顶部的筛选器会联动控制页面上所有的图表，实现“一处筛选，全体刷新”。

#### **1. 全局筛选器区 (Global Filters)**

位于页面最顶部，比驾驶舱的筛选器功能更强大。

*   **核心筛选维度**:
    *   **时间范围**: **[最重要的筛选器]** 提供预设（今天、本周、上个月、本季度等）和自定义日期范围选择。
    *   **工单模板/类型**: 下拉多选。
    *   **部门/团队**: 树形选择器，可以选择一个或多个处理团队。
    *   **处理人员**: 多选搜索框。
    *   **客户/客户组**: 搜索并选择特定的客户或客户分组。
*   **对比功能**:
    *   **`[ 与上一周期对比 ]`**: 一个开关或勾选项。选中后，所有图表都会同时展示当前周期和上一周期的数据（例如，本周与上周），并自动计算环比增长率。

#### **2. 可交互图表卡片区 (Interactive Chart Cards)**

页面主体由一系列可交互的图表卡片组成，每个卡片聚焦一个分析主题。

*   **卡片1：工单量综合分析**
    *   **上部**: 几个核心KPI数字卡，如`新增工单总量`、`解决工单总量`、`未解决工单量`，并显示对比周期的环比变化（如 `↑15%`）。
    *   **下部**: 一个**堆叠面积图或组合图（折线+柱状）**。
        *   **X轴**: 时间（根据所选范围自动调整粒度，如按天、周、月）。
        *   **Y轴**: 工单数量。
        *   **数据系列**: 同时展示`新增量`、`解决量`，甚至可以再叠加一条`关闭量`的折线。用户可以点击图例来显示/隐藏某个数据系列。

*   **卡片2：工单构成分析 (多维分解)**
    *   这是一个**可切换视图**的图表。用户可以通过下拉菜单选择分解的维度。
    *   **默认视图**: **按“工单类型”分解的饼图或环形图**，显示各类工单的占比。
    *   **切换维度**: 用户可以切换为**按“优先级”分解**、**按“渠道来源”分解**、**按“标签”分解**。
    *   **交互**: 点击饼图的某个扇区（如“客户投诉”），页面上的**所有其他图表都会被联动筛选**，只显示与“客户投诉”相关的数据，实现强大的下钻分析。

*   **卡片3：工源与分布分析**
    *   **左侧**: 一个**按“客户”或“客户公司”排名的新增工单量排行榜 (水平条形图)**，快速定位工单量最大的客户。
    *   **右侧**: 一个**按“处理团队/部门”排名的解决工单量排行榜 (水平条形图)**，展示各团队的贡献度。
    *   **交互**: 点击排行榜中的任何一项，同样可以联动筛选整个页面的数据。

*   **卡片4：工单状态漏斗分析 (可选，高级功能)**
    *   一个**漏斗图**，展示工单从`新增` -> `处理中` -> `已办结` -> `已关闭`的转化率。
    *   这可以帮助发现流程中的瓶颈，例如，如果“已办结”到“已关闭”的转化率很低，说明回访环节可能存在问题或效率不高。

*   **卡片5：数据明细表格 (Data Table)**
    *   位于页面的最下方，是一个与顶部筛选器联动的**原始数据表格**。
    *   当用户在上面的图表中进行下钻分析后，这个表格会实时刷新，显示符合所有筛选条件的工单明细列表。
    *   提供**导出为Excel/CSV**的功能。

---