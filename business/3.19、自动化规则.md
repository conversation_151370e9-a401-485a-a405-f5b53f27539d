
---

### **“自动化规则”页面内容详解**

#### **一、 页面核心目标**

1.  **定义触发条件**: 精确地定义在什么情况下，自动化规则应该被触发。
2.  **设定执行动作**: 明确地规定当条件满足时，系统应该自动执行哪些操作。
3.  **管理规则生命周期**: 创建、编辑、启用/停用、复制和删除规则，并能查看其执行历史。
4.  **避免规则冲突**: 提供机制来管理多个规则的执行顺序或优先级。

---

### **二、 页面内容与布局**

与“工单模板管理”类似，这个页面也分为两个主要视图：
1.  **规则列表视图 (Rule List View)**: 管理所有已创建的规则。
2.  **规则构建器/编辑器视图 (Rule Builder/Editor View)**: 创建或编辑单个规则的核心界面。

#### **模式一：规则列表视图**

*   **页面顶部**: 一个醒目的 `[ + 新建自动化规则 ]` 按钮。
*   **搜索框**: 按规则名称搜索。
*   **规则列表**: 以表格形式展示所有已创建的规则。
    *   **表头**: `规则名称`, `触发事件`, `状态 (启用/停用)`, `执行顺序/优先级`, `创建人`, `最后修改时间`, `操作`。
    *   **执行顺序/优先级**: 这是一个非常重要的字段。系统需要一个机制来决定当一个事件同时触发多个规则时，应该按什么顺序执行。管理员可以在这里设置一个数字（越小越优先）或通过拖拽来调整。
    *   **行末操作**: `[ 编辑 ]`, `[ 复制 ]`, `[ 启用/停用 ]`, `[ 查看执行日志 ]`, `[ 删除 ]`。

---

#### **模式二：规则构建器/编辑器视图**

这是最核心的界面，清晰地分为**“触发器”、“条件”和“动作”**三个逻辑构建块。

**1. 规则基本信息区 (Top Section)**

*   **规则名称**: [必填] 如“高优先级投诉自动通知总监”。
*   **规则描述**: 解释此规则的目的和逻辑。
*   **状态**: `[ 启用 ]` / `[ 停用 ]` 的开关。
*   **执行顺序/优先级**: 设置一个数字来定义其执行优先级。

**2. “当……” - 触发器选择区 (Triggers - "When...")**

这一步定义了规则的**“启动时机”**。

*   **触发事件 (Trigger Event)**: 一个下拉菜单，选择规则在哪个事件发生时被检查。
    *   `当工单被创建时`
    *   `当工单被更新时` (可以进一步细化，如“当工单状态变更时”、“当工单优先级变更时”)
    *   `当工单被分配时`
    *   `当添加新评论/补记时`
    *   `当SLA即将超时/已超时时`
    *   `当收到客户评价时`
    *   `基于时间的触发器` (例如，“每小时运行一次”、“每天凌晨执行”)

**3. “如果……” - 条件判断区 (Conditions - "If...")**

这一步定义了规则执行的**“具体前提”**。用户可以通过一个强大的**条件构建器**（与“工单明细报表”的类似）来设定一个或多个条件。

*   **条件逻辑**: 可以选择条件之间的关系是 **“满足所有条件 (AND)”** 还是 **“满足任意条件 (OR)”**。
*   **条件构建**: 每一条条件都是一个 **`[ 字段 ]` `[ 操作符 ]` `[ 值 ]`** 的组合。
    *   **字段**: 下拉菜单，可以选择工单的所有字段（包括自定义字段）。
    *   **操作符**: 等于、不等于、包含、大于、小于等。
    *   **值**: 用户输入或选择。
*   **示例**:
    *   `[工单模板]` `等于` `客户投诉`
    *   **AND** `[优先级]` `等于` `紧急`
    *   **AND** `[工单状态]` `变更为` `待处理`

**4. “那么……” - 动作执行区 (Actions - "Then...")**

这一步定义了当条件满足时，系统应该**“做什么”**。用户可以添加一个或多个要执行的动作。

*   **添加动作按钮**: 点击后会弹出一个分类好的动作列表供选择。
*   **可执行的动作列表**:
    *   **更新工单属性**:
        *   `设置状态为...`
        *   `设置优先级为...`
        *   `设置处理人为/团队为...`
        *   `添加/移除标签...`
        *   `修改自定义字段的值...`
    *   **发送通知**:
        *   `发送邮件给...` (可以指定具体的人、处理人、客户、或某个邮箱地址，并支持使用变量的邮件模板)。
        *   `发送短信通知...`
        *   `发送站内信...`
    *   **执行人员操作**:
        *   `添加评论/补记...` (可以添加一段预设的文本，如“此工单已自动升级”)。
        *   `指派给...`
        *   `添加协办方...`
        *   `抄送给...`
    *   **外部系统交互 (高级)**:
        *   `调用Webhook...` (向一个外部系统的URL发送数据，用于系统集成)。
        *   `创建/更新CRM中的记录...` (如果与CRM深度集成)。

*   **示例**:
    *   **动作1**: `抄送给` `质量部经理`
    *   **动作2**: `发送邮件给` `服务总监`，邮件内容：“一个新的高优先级投诉工单已创建，ID为{{work_order.id}}，请关注。”

**页面底部操作按钮**: `[ 保存规则 ]`, `[ 取消 ]`。

---
