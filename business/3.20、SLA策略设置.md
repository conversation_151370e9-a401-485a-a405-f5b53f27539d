
---

### **“SLA策略设置”页面内容详解**

#### **一、 页面核心目标**

1.  **定义多种SLA策略**: 能够创建多个不同的SLA策略，以应对不同客户、不同业务、不同优先级的服务需求。
2.  **设定精确的时间目标**: 为每个策略明确定义“首次响应”和“问题解决”的时间目标。
3.  **规定工作时间**: 定义SLA计时器的工作日历（如工作日9-5，还是7x24小时），确保计时公平合理。
4.  **关联应用范围**: 将创建好的SLA策略应用到符合特定条件的工单上。

---

### **二、 页面内容与布局**

与前几个设置页面类似，它也分为两个主要视图：
1.  **SLA策略列表视图 (Policy List View)**: 管理所有已创建的SLA策略。
2.  **SLA策略构建器/编辑器视图 (Policy Builder/Editor View)**: 创建或编辑单个策略的核心界面。

#### **模式一：SLA策略列表视图**

*   **页面顶部**: 一个醒目的 `[ + 新建SLA策略 ]` 按钮。
*   **搜索框**: 按策略名称搜索。
*   **策略列表**: 以表格形式展示所有已创建的SLA策略。
    *   **表头**: `策略名称`, `描述`, `状态 (启用/停用)`, `应用优先级`, `创建人`, `最后修改时间`, `操作`。
    *   **应用优先级**: **[核心字段]** 当一个工单同时满足多个SLA策略的条件时，系统需要知道应用哪个。这里可以设置一个数字（越小越优先）或通过拖拽来调整策略的应用顺序。
    *   **行末操作**: `[ 编辑 ]`, `[ 复制 ]`, `[ 启用/停用 ]`, `[ 删除 ]`。

---

#### **模式二：SLA策略构建器/编辑器视图**

这是最核心的界面，清晰地分为**“基本信息”、“应用条件”、“目标设定”和“工作日历”**四个逻辑构建块。

**1. 策略基本信息区 (Top Section)**

*   **策略名称**: [必填] 如“VIP客户-紧急问题SLA”、“标准IT服务SLA”。
*   **策略描述**: 解释此策略的适用范围和目标。
*   **状态**: `[ 启用 ]` / `[ 停用 ]` 的开关。
*   **应用优先级**: 设置一个数字来定义其应用优先级。

**2. “当工单满足以下条件时应用此策略” - 应用条件区 (Conditions)**

这一步定义了**“什么样的工单应该使用这个SLA策略”**。它使用了一个强大的**条件构建器**（与自动化规则的类似）。

*   **条件逻辑**: 可以选择条件之间的关系是 **“满足所有条件 (AND)”** 还是 **“满足任意条件 (OR)”**。
*   **条件构建**: 每一条条件都是一个 **`[ 字段 ]` `[ 操作符 ]` `[ 值 ]`** 的组合。
    *   **字段**: 下拉菜单，可以选择工单的所有字段。
    *   **操作符**: 等于、不等于、包含等。
    *   **值**: 用户输入或选择。
*   **示例**:
    *   `[工单模板]` `等于` `IT设备报修单`
    *   **AND** `[客户级别]` `等于` `VIP`
    *   **AND** `[优先级]` `属于...之一...` `[紧急, 高]`

**3. “SLA目标” - 时间目标设定区 (Targets)**

这一步定义了**具体的服务承诺时间**。一个SLA策略通常包含多个目标。

*   **目标列表**: 用户可以添加多个不同的SLA目标。
    *   **目标一：首次响应时间 (First Response Time)**
        *   `目标名称`: (默认为首次响应时间)
        *   `时间目标`: 输入数字和单位，如 `30` `分钟`。
        *   `基于优先级`: **[核心功能]** 可以为**不同的优先级**设置**不同的时间目标**。
            *   `紧急`: `15 分钟`
            *   `高`: `30 分钟`
            *   `中`: `1 小时`
            *   `低`: `4 小时`
    *   **目标二：解决时间 (Resolution Time)**
        *   `目标名称`: (默认为解决时间)
        *   `时间目标`:
        *   `基于优先级`:
            *   `紧急`: `4 小时`
            *   `高`: `8 小时`
            *   `中`: `3 个工作日`
            *   `低`: `7 个工作日`
    *   **目标三（可选）：更新时间 (Next Reply Time)**
        *   要求处理人员在与客户的每次沟通之间，不能超过设定的时间。

*   **SLA计时器控制**:
    *   `计时开始于`: 工单状态为 `[待处理]` 时。
    *   `计时暂停于`: 工单状态为 `[已挂起]` 或 `[等待客户回复]` 时。
    *   `计时结束于`: 工单状态为 `[已办结]` 时。

**4. “工作日历” - 计时规则区 (Business Hours / Calendar)**

这一步定义了**“SLA的表是怎么走的”**，确保计时的公平性。

*   **日历选择**: 一个下拉菜单，可以选择一个预设好的工作日历。
    *   `7 x 24 小时`
    *   `工作日 (周一至周五, 9:00 - 18:00)`
    *   `自定义日历...` (点击后可以进入一个专门的日历设置页面，定义工作日、工作时间、以及节假日等例外日期)。
*   **时区设置**: 定义该SLA策略所依据的时区。

**页面底部操作按钮**: `[ 保存策略 ]`, `[ 取消 ]`。

---