
---

### **“工单明细报表”页面内容详解**

#### **一、 页面核心目标**

1.  **终极筛选能力**: 提供系统中最全面、最无死角的筛选条件组合。
2.  **自定义列与数据呈现**: 允许用户自由选择报表中需要包含哪些数据列，并能调整顺序。
3.  **强大的导出功能**: 支持多种格式、大批量数据的异步导出。
4.  **报表模板化**: 允许用户将常用的报表配置（筛选条件+列定义）保存为模板，一键生成。

---

### **二、 页面内容与布局**

页面布局清晰地分为三个核心步骤区域：**1. 定义筛选条件 -> 2. 定义报表列 -> 3. 预览并导出结果**。

#### **区域一：筛选条件定义区 (Filter Definition)**

这部分的功能与“工单查询”页面的高级搜索区类似，但可能更加强大和结构化。

*   **条件构建器 (Condition Builder)**:
    *   用户可以通过“添加筛选条件”按钮，像搭积木一样，逐条添加过滤规则。
    *   每一条规则包含三个部分：**`[ 字段 ]` `[ 操作符 ]` `[ 值 ]`**。
        *   **字段**: 一个下拉菜单，包含了系统中**所有**可以被筛选的字段（包括所有自定义字段）。
        *   **操作符**: 根据所选字段的类型动态变化。
            *   对于文本字段：等于、不等于、包含、不包含、开头是...
            *   对于数字/日期字段：大于、小于、等于、介于...之间...
            *   对于选择字段（如状态、优先级）：等于、不等于、属于...之一...
        *   **值**: 用户输入的具体数值或从选项中选择。
    *   可以添加**多条规则**，并选择规则之间的逻辑关系是 **“与 (AND)”** 还是 **“或 (OR)”**，甚至支持创建嵌套的逻辑分组 `(A and B) or C`。

*   **已保存的筛选器**:
    *   一个下拉菜单，可以快速加载之前保存过的常用筛选组合。

#### **区域二：报表列定义区 (Column Definition)**

这是此页面区别于“工单查询”的核心功能之一。

*   **双栏选择器 (Dual-List Selector)**:
    *   **左栏“可用列 (Available Columns)”**: 列出了系统中**所有**可以作为报表列的字段。这个列表非常长，按模块（如客户信息、工单属性、处理信息、SLA信息等）进行分组，并提供搜索功能。
    *   **右栏“已选列 (Selected Columns)”**: 显示用户已经选择要包含在报表中的列。
    *   用户可以通过点击、拖拽或中间的箭头按钮，在两栏之间移动字段。
*   **排序与重命名**:
    *   在右栏“已选列”中，用户可以**上下拖拽**来调整列在最终报表中的顺序。
    *   用户还可以**重命名**列的标题（例如，将系统内的“field_123”重命名为“客户反馈渠道”），使导出的报表更具可读性。

#### **区域三：预览与导出区 (Preview & Export)**

*   **操作按钮**:
    *   **`[ 预 览 ]`**: 点击后，下方的结果列表会根据当前的筛选条件和列定义，加载前100条数据作为预览。这可以帮助用户在正式导出前，检查配置是否正确。
    *   **`[ 立即导出 ]`**: 点击后，会弹出一个配置窗口。
        *   **文件名**: 自定义导出的文件名。
        *   **文件格式**: 选择 `Excel (.xlsx)`、`CSV (.csv)`、`PDF (.pdf)`。
        *   **导出范围**: 选择“仅预览数据”或“所有匹配结果”。
        *   点击确认后，系统会启动一个**异步导出任务**。对于大量数据，用户无需在页面等待，任务完成后会收到系统通知，并提供下载链接。

*   **报表模板操作**:
    *   **`[ 保存为模板 ]`**: 将当前配置好的“筛选条件 + 列定义”保存为一个新的报表模板，并为其命名。
    *   **`[ 加载模板 ]`**: 在页面顶部提供一个下拉菜单，可以选择并一键加载已保存的报表模板。

*   **数据预览表格 (Data Preview Grid)**:
    *   一个只读的表格，用于展示预览数据。其表头和列顺序会严格按照用户在区域二的定义来显示。

---