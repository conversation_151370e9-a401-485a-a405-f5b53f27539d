
---

### **“智能摘要与写作模型”页面内容详解**

#### **一、 页面核心目标**

1.  **模型集成与选择**: 允许管理员连接和选择用于生成文本的后端AI模型。
2.  **功能启用与配置**: 控制在系统的哪些地方、为哪些角色启用AI辅助功能。
3.  **定义生成风格 (Prompt工程)**: 通过定义系统指令（Prompt），微调AI生成的文本，使其更符合公司的品牌口吻和业务需求。
4.  **使用情况监控与反馈**: 追踪AI辅助功能的使用频率和效果，并收集用户反馈。

---

### **二、 页面内容与布局**

页面通常采用**多标签页 (Tabs)** 或**功能卡片式**的布局，将不同的AI辅助功能（摘要、润色、翻译等）的配置进行清晰的分区。

#### **模块一：模型连接与管理 (Model Connection & Management)**

这是所有功能的基础，负责连接到提供生成能力的AI服务。

*   **模型提供商列表**:
    *   以卡片形式展示系统支持的LLM提供商，如 `Google AI Platform (Gemini)`, `OpenAI (GPT-4/3.5)`, `Azure OpenAI Service` 等。
*   **连接配置**:
    *   点击某个提供商的“连接”按钮，会弹出一个窗口，要求管理员输入：
        *   `API Key`
        *   `Endpoint URL` (对于私有部署或特定区域的Azure服务)
*   **默认模型选择**:
    *   一个下拉菜单，让管理员为系统内的**不同任务**选择**默认使用的模型**。这允许进行成本和性能的平衡。
        *   `工单摘要生成`: 使用 `gemini-1.5-pro` (能力强，效果好)
        *   `回复草稿生成`: 使用 `gemini-1.5-flash` (速度快，成本低)
        *   `文本润色`: 使用 `gemini-1.5-flash`

#### **模块二：AI辅助功能配置 (AI Assistant Feature Configuration)**

这是一个功能开关和配置列表，管理员可以在这里精细控制每一个AI辅助功能的行为。

*   **功能列表**: 每一项都是一个可展开的配置卡片。

    *   **卡片1：工单摘要 (Work Order Summarization)**
        *   `[ ✓ ] 启用工单摘要功能`: 总开关。
        *   **可见角色**: 多选框，选择哪些角色可以看到“生成摘要”按钮（如 `[处理人]`, `[管理者]`）。
        *   **摘要风格指令 (Prompt)**: **[核心配置]** 一个文本框，用于定义生成摘要的规则。
            > **示例指令**:
            > "你是一个专业的IT服务助理。请根据以下的工单时间轴，生成一段不超过150字的摘要。摘要必须包含以下几点：1. 客户的核心问题是什么。2. 关键的处理步骤有哪些。3. 最终的解决方案是什么。请使用客观、专业的语气，忽略闲聊内容。"

    *   **卡片2：文本润色 (Tone Polishing)**
        *   `[ ✓ ] 启用文本润色功能`: 总开关。
        *   **可见角色**: `[客服]`, `[处理人]`。
        *   **预设润色风格**: 管理员可以定义多个“一键润色”的选项，并为每个选项配置其背后的Prompt。
            *   **`[ 专业风格 ]`**: 指令 -> "将以下文本改写得更加专业、书面化，避免使用口语和俚语。"
            *   **`[ 安抚风格 ]`**: 指令 -> "将以下文本改写得更具同理心和安抚性，首先对客户表示理解和歉意，然后清晰地说明下一步的计划。"
            *   **`[ 简洁风格 ]`**: 指令 -> "将以下文本精简，保留核心信息，使其更易于阅读。"

    *   **卡片3：回复草稿生成 (Draft Generation)**
        *   `[ ✓ ] 启用回复草稿生成功能`: 总开关。
        *   **可见角色**: `[客服]`, `[处理人]`。
        *   **草稿生成指令 (Prompt)**:
            > **示例指令**:
            > "你是一个金牌客服。请根据工单的完整上下文，为我起草一封回复客户的邮件。邮件需要：1. 称呼客户姓名。2. 简要回顾问题。3. 清晰地告知解决方案或下一步计划。4. 保持友好和专业的语气。"

    *   **卡片4：多语言翻译 (Translation)**
        *   `[ ✓ ] 启用翻译功能`: 总开关。
        *   **支持的语言**: 一个多选框，选择系统支持翻译的目标语言。

#### **模块三：使用情况与反馈监控 (Usage & Feedback Monitoring)**

这个模块用于评估AI辅助功能的实际效果和价值。

*   **使用情况仪表盘**:
    *   **KPI指标卡**:
        *   `本月AI功能总调用次数`: `8,500`
        *   `摘要生成次数`: `2,500`
        *   `文本润色次数`: `4,000`
    *   **图表**:
        *   一个条形图，展示不同团队或员工对AI功能的使用频率排行榜。

*   **用户反馈列表**:
    *   在前端，每次AI生成内容后，旁边都会有 `[ 👍 ]` 和 `[ 👎 ]` 的反馈按钮。
    *   这个列表会展示所有被“点踩”的记录。
    *   **列表表头**: `时间`, `功能` (如“摘要生成”), `用户`, `关联工单ID`, `原始输入`, `AI生成结果`, `用户反馈`。
    *   **价值**: 这是**最重要的优化依据**。管理员可以通过分析这些“坏案例”，来反向优化Prompt指令或调整模型选择。

---