
---

### **“SLA达成率报告”页面内容详解**

#### **一、 页面核心目标**

1.  **宏观达成率概览**: 一眼看出总体的SLA达成情况，是好是坏。
2.  **违规情况聚焦**: 快速定位哪些工单、哪些类型的工单、哪些团队的工单最容易违规。
3.  **违规原因分析**: 分析导致SLA违规的具体原因（是响应超时还是解决超时）。
4.  **趋势监控**: 跟踪SLA达成率随时间的变化，评估服务水平的稳定性。
5.  **数据追溯与问责**: 提供详细的违规工单列表，用于问题复盘和责任定位。

---

### **二、 页面内容与布局**

页面采用**“筛选器 + 核心指标卡 + 多个分析图表 + 违规明细列表”**的经典布局。

#### **1. 全局筛选器区 (Global Filters)**

*   **时间范围**: **[最重要的筛选器]** 提供预设和自定义日期范围选择。
*   **部门/团队**: 树形选择器，分析特定团队的SLA表现。
*   **工单模板/类型**: 分析处理不同业务类型工单的SLA情况。
*   **优先级**: 分析不同优先级工单的SLA达成情况（通常高优先级的SLA要求更严）。
*   **客户/客户组**: 分析对特定客户的服务承诺履行情况。

#### **2. 核心SLA指标卡 (Key SLA Metrics)**

位于页面顶部，用醒目的方式展示最核心的达成率数据。

*   **总体SLA达成率**: 一个大的**仪表盘图**或**百分比数字**，如 `98.2%`。
*   **已评估工单总数**: `5,250` (在选定时间范围内所有适用SLA的工单)。
*   **SLA达标工单数**: `5,155` (用绿色数字表示)。
*   **SLA违规工单数**: `95` (用醒目的红色数字表示)。
*   **平均超期时长**: `1.2小时` (指所有违规工单平均超出SLA规定多长时间)。

#### **3. SLA分析图表卡片区 (SLA Analysis Charts)**

这是页面的主体，从不同维度深入剖析SLA数据。

*   **卡片1：SLA状态分布**
    *   **图表类型**: **环形图 (Donut Chart)** 或 **百分比堆叠条形图**。
    *   **内容**: 将所有工单清晰地分为几个部分：
        *   `按时解决 (Met)`
        *   `响应超时，但按时解决 (Response SLA Breached)`
        *   `解决超时 (Resolution SLA Breached)`
        *   `响应和解决均超时 (Both Breached)`
    *   **洞察**: 一眼看出主要的违规类型是响应不及时，还是解决能力不足。

*   **卡片2：SLA达成率趋势图**
    *   **图表类型**: **折线图**。
    *   **X轴**: 时间（按天、周、月）。
    *   **Y轴**: 百分比。
    *   **内容**: 展示“总体SLA达成率”在过去一段时间内的波动情况。可以叠加一条“团队目标线”（如95%），直观对比。
    *   **洞察**: 评估服务水平的稳定性和改进措施的效果。

*   **卡片3：按不同维度分析违规情况 - [核心根因分析工具]**
    *   **图表类型**: **分组水平条形图 (Grouped Horizontal Bar Chart)**。
    *   **内容**: 这是一个**可交互的图表**，以“**SLA违规工单数**”或“**SLA违规率**”作为衡量指标，然后按不同维度进行排名和分解。
        *   **按“工单类型”分解**: 快速定位哪种业务类型的工单最容易导致SLA违规，可能意味着这类问题的处理流程或资源不足。
        *   **按“处理团队”分解**: 对比不同团队的SLA表现，发现团队间的差异。
        *   **按“优先级”分解**: 验证是否所有优先级的工单都得到了应有的对待。
        *   **按“客户”分解**: 识别哪些客户的服务体验可能受损最严重。

*   **卡片4：首次响应与解决SLA分析**
    *   **图表类型**: **两个并排的仪表盘图或指标卡**。
    *   **左侧**: `首次响应SLA达成率` (例如 `99.5%`)。
    *   **右侧**: `解决SLA达成率` (例如 `98.5%`)。
    *   **洞察**: 将响应和服务解决两个承诺分开评估，更精细地定位问题。

#### **4. SLA违规工单明细列表 (SLA Breach Details Table)**

位于页面的最下方，是所有分析的最终落脚点。

*   **内容**: 这个表格会**与顶部的筛选器和图表的交互联动**。例如，当你在上面的图表中点击了“IT报障”这个分类，这个列表就会自动筛选出所有“IT报障”类型下的SLA违规工单。
*   **列表表头**:
    *   `工单ID`: 可点击跳转到详情页。
    *   `标题`:
    *   `处理人/团队`:
    *   `客户`:
    *   `优先级`:
    *   `**SLA策略**`: 显示该工单适用的具体SLA规则（如“高-4小时解决”）。
    *   `**违规类型**`: 明确标出是“响应超时”还是“解决超时”。
    *   `**超期时长**`: 精确显示超出了多长时间。
    *   `关闭时间`:
*   **功能**: 提供**导出**功能，方便管理者将违规明细导出，用于复盘会议或作为绩效评估的依据。

---