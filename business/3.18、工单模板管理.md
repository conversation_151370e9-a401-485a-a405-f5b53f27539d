
---

### **“工单模板管理”页面内容详解**

#### **一、 页面核心目标**

1.  **管理模板**: 创建、编辑、复制、启用/停用和删除不同的工单模板。
2.  **定义表单结构 (自定义字段)**: 为每个模板添加、配置和排序其专属的数据字段。
3.  **配置关联流程**: 将每个模板与特定的SLA策略、自动化规则等后台流程进行绑定。
4.  **提供一致体验**: 确保通过模板创建的工单，其数据结构和初始流程是标准化的。

---

### **二、 页面内容与布局**

这个页面通常分为两个视图：
1.  **模板列表视图 (Template List View)**: 管理所有模板的入口。
2.  **模板构建器/编辑器视图 (Template Builder/Editor View)**: 创建或编辑单个模板的核心界面。

#### **模式一：模板列表视图**

*   **页面顶部**: 一个醒目的 `[ + 新建工单模板 ]` 按钮。
*   **搜索框**: 按模板名称搜索。
*   **模板列表**: 以表格形式展示所有已创建的模板。
    *   **表头**: `模板名称`, `关联业务`, `状态 (启用/停用)`, `创建人`, `最后修改时间`, `操作`。
    *   **行末操作**:
        *   `[ 编辑 ]`: 进入模板构建器视图。
        *   `[ 复制 ]`: 快速创建一个与此模板配置类似的新模板。
        *   `[ 启用/停用 ]`: 控制此模板是否在“新建工单”页面对用户可见。
        *   `[ 删除 ]` (对于没有被工单使用过的模板)。

---

#### **模式二：模板构建器/编辑器视图**

这是最核心、最复杂的界面，通常采用**多标签页 (Tabs)** 的布局，将模板的各项配置进行逻辑分区。

**1. 标签页一：基本信息 (Basic Information)**

*   **模板名称**: [必填] 如“IT设备报修单”、“客户投诉与建议”。
*   **模板图标 (可选)**: 为模板选择一个直观的图标，方便在“新建工单”页面展示。
*   **模板描述**: 解释此模板的适用场景和用途。
*   **状态**: `[ 启用 ]` / `[ 停用 ]` 的开关。

**2. 标签页二：表单设计器 (Form Builder) - [核心中的核心]**

这是一个**可视化的、拖拽式**的表单构建界面。

*   **左侧：字段控件库 (Field Control Library)**
    *   列出了所有可用的字段类型，用户可以从中拖拽到右侧的画布上。
    *   **基础控件**:
        *   `单行文本`
        *   `多行文本` (富文本)
        *   `数字`
        *   `日期/时间`
        *   `下拉单选`
        *   `复选框`
        *   `单选按钮组`
    *   **高级控件**:
        *   `附件上传`
        *   `用户选择器` (用于关联员工)
        *   `部门选择器`
        *   `客户选择器`
        *   `关联资产选择器` (如果集成了资产管理)

*   **中间：表单画布 (Form Canvas)**
    *   这是一个所见即所得的区域，实时展示了当前表单的布局。
    *   用户可以从左侧**拖拽**控件到画布上。
    *   可以**上下拖拽**画布上的字段，调整它们的显示**顺序**。
    *   可以将字段拖拽成多列布局（如两列或三列）。

*   **右侧：字段属性配置 (Field Properties Configuration)**
    *   当用户在中间的画布上**点击某个字段**时，右侧会浮现该字段的详细配置项。
    *   **通用属性**:
        *   `字段标签`: 用户看到的字段名称，如“问题描述”。
        *   `占位提示 (Placeholder)`: 输入框内的提示文字。
        *   `帮助文本`: 字段下方的小字说明。
        *   `是否必填`: 一个开关。
        *   `是否只读`: 一个开关。
    *   **特定属性**:
        *   对于“下拉单选”：需要配置**选项列表**（可以手动添加，或从数据字典中引用）。
        *   对于“数字”：可以设置**最小值、最大值、单位**。
        *   对于“附件上传”：可以限制**文件类型和大小**。

**3. 标签页三：流程配置 (Process Configuration)**

这部分用于将模板与后台的自动化流程绑定。

*   **默认指派规则**:
    *   `默认主办方`: 可以预设当使用此模板时，工单默认指派给哪个团队或个人。
    *   `默认协办方`: 预设协办方。
*   **SLA策略关联**:
    *   一个下拉菜单，选择此模板创建的工单应遵循哪个**SLA策略**。例如，“IT设备报修单”关联“IT服务SLA”。
*   **自动化规则触发**:
    *   列出已配置的自动化规则，允许用户将此模板与某个规则进行关联。例如，“当使用【客户投诉】模板创建工单时，自动抄送给质量部经理”。
*   **默认优先级**:
    *   为此模板设置一个默认的优先级。

**4. 标签页四：预览 (Preview)**

*   提供一个完整的、可交互的“新建工单”页面预览，让管理员在发布前，能以最终用户的视角体验和测试整个模板的配置是否合理。

**页面顶部操作按钮**: `[ 保存 ]`, `[ 保存并发布 ]`, `[ 取消 ]`。

---