<div align="center">
  <strong>Next.js 管理后台模板 - TypeScript & Shadcn UI</strong><br />
  基于 Next.js 15、Tailwind CSS v4、App Router、TypeScript 和 Shadcn UI 构建的现代化管理后台模板
</div>

<br />

<div align="center">
  <a href="https://next-shadcn-admin-dashboard.vercel.app">查看演示</a>
</div>

<br />

<div align="center">
  <img src="https://github.com/arhamkhnz/next-shadcn-admin-dashboard/blob/main/media/dashboard.png?version=5" alt="仪表板截图" width="75%">
</div>

## 项目愿景

本项目旨在创建一个开源的管理后台模板，包含多个示例页面、预构建组件和精心设计的 UI 模式，所有功能都基于清晰的架构和完善的项目配置。

它致力于为 SaaS 平台、内部仪表板和管理面板提供强大的起点，内置多租户、RBAC（基于角色的访问控制）和功能扩展支持。

## 技术栈概览

本项目使用 `Next.js 15 (App Router)`、`TypeScript`、`Tailwind CSS v4` 和 `Shadcn UI` 作为主要技术栈。

同时集成了 `Zod` 用于数据验证，`ESLint` 和 `Prettier` 用于代码规范，`Husky` 用于 Git 钩子。

项目将支持 `React Hook Form`、`Zustand`、`TanStack Table` 等相关工具，并在后续版本中添加更多页面。RBAC（基于角色的访问控制）配置驱动 UI 和多租户 UI 支持也在功能路线图中。

当前版本使用 [Tweakcn Tangerine](https://tweakcn.com/) 主题。

> 寻找 **Next 14 + Tailwind CSS v3** 版本？  
> 查看 [`archive/next14-tailwindv3`](https://github.com/arhamkhnz/next-shadcn-admin-dashboard/tree/archive/next14-tailwindv3) 分支。  
> 该分支使用不同的颜色主题，不再积极维护，但会尽量保持与最新更改和页面的同步。

## 功能页面

✅ 已完成  
🚧 开发中

### 仪表板
- ✅ 默认仪表板
- ✅ CRM 仪表板
- ✅ 财务仪表板
- ✅ Demo 演示模式
- 🚧 分析仪表板
- 🚧 电商仪表板
- 🚧 学院仪表板
- 🚧 物流仪表板

### 应用页面
- 🚧 邮件系统
- 🚧 聊天应用
- 🚧 日历系统
- 🚧 看板管理
- 🚧 发票管理
- 🚧 用户管理
- 🚧 角色管理
- ✅ 身份认证

## 核心特性

### 🎨 **完整的组件库**
- **40+ UI 组件**：基于 shadcn/ui 构建的完整组件生态
- **数据表格系统**：支持排序、分页、过滤、拖拽的高级表格
- **主题系统**：支持亮色/暗色主题切换
- **响应式设计**：完美适配移动端和桌面端

### 🏗️ **现代化架构**
- **Colocation 文件系统**：按功能分组的模块化架构
- **TypeScript 严格模式**：完整的类型安全保障
- **App Router**：基于 Next.js 15 的最新路由系统
- **服务端组件**：优化的性能和 SEO

### 🛠️ **开发体验**
- **ESLint + Prettier**：严格的代码规范和格式化
- **Husky Git 钩子**：提交前自动检查
- **热重载**：快速的开发反馈
- **TypeScript 智能提示**：完整的开发工具支持

### 📊 **数据管理**
- **TanStack Table**：强大的表格数据处理
- **Zustand 状态管理**：轻量级的状态管理方案
- **Zod 数据验证**：类型安全的数据验证
- **React Hook Form**：高性能的表单处理

## Colocation 文件系统架构

页面、组件和逻辑按功能分组。每个路由文件夹包含其所需的所有内容。共享的 UI、钩子和配置位于顶层。这使代码库保持模块化，随着应用增长易于导航。

查看 [此仓库](https://github.com/arhamkhnz/next-colocation-template) 了解完整的文件结构和示例。

```
src/app/(main)/
├── dashboard/
│   ├── default/                # 默认仪表板
│   │   ├── _components/        # 页面专用组件
│   │   └── page.tsx           # 页面入口
│   ├── crm/                   # CRM 仪表板
│   └── finance/               # 财务仪表板
├── demo/                      # 演示模式
│   ├── _components/           # 简化组件
│   └── default/               # 演示页面
└── auth/                      # 认证页面
```

## 快速开始

### 使用 Vercel 部署

<a href="https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Farhamkhnz%2Fnext-shadcn-admin-dashboard">
  <img src="https://vercel.com/button" alt="使用 Vercel 部署" />
</a>  

*点击上方按钮将此仪表板部署到 Vercel。*

### 本地开发

按照以下步骤在本地设置和运行项目：

1. **克隆仓库**
   ```bash
   git clone https://github.com/arhamkhnz/next-shadcn-admin-dashboard.git
   cd next-shadcn-admin-dashboard
   ```
   
2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **访问应用**
   
   打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### 可用命令

```bash
# 开发
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器

# 代码质量
npm run lint         # 运行 ESLint 检查
npm run format       # 格式化代码
npm run format:check # 检查代码格式

# 组件管理
npx shadcn@latest add [component]  # 添加新组件
```

## 项目结构

```
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (main)/            # 主应用布局组
│   │   └── auth/              # 认证页面
│   ├── components/            # 组件库
│   │   ├── ui/                # shadcn/ui 基础组件
│   │   └── data-table/        # 数据表格组件
│   ├── hooks/                 # 自定义钩子
│   ├── lib/                   # 工具函数
│   ├── stores/                # Zustand 状态管理
│   └── types/                 # TypeScript 类型定义
├── docs/                      # 项目文档
├── public/                    # 静态资源
└── 配置文件...
```

## 组件库

### UI 组件 (40+)
- **基础组件**：Button, Input, Label, Textarea
- **表单组件**：Checkbox, Radio, Select, Switch, Slider
- **导航组件**：Sidebar, Breadcrumb, Pagination, Navigation Menu
- **反馈组件**：Alert, Toast, Progress, Skeleton
- **覆盖层组件**：Dialog, Sheet, Popover, Tooltip
- **数据展示**：Table, Card, Avatar, Badge, Chart
- **布局组件**：Tabs, Collapsible, Separator, Scroll Area

### 数据表格系统
- **DataTable**：主表格组件，支持排序、分页、过滤
- **拖拽功能**：基于 @dnd-kit 的行拖拽排序
- **列管理**：动态显示/隐藏列
- **响应式**：移动端优化的表格体验

## Demo 模式

项目包含一个精简的 Demo 模式，专为演示和原型展示设计：

- **简化界面**：移除复杂的数据展示和用户管理功能
- **核心导航**：保留基本的布局和导航结构
- **中文化**：完整的中文界面支持
- **快速预览**：适合向客户展示基础功能

访问路径：`/demo`

## 开发指南

### 添加新页面
```bash
# 1. 在对应路由下创建文件夹
mkdir src/app/(main)/dashboard/new-feature

# 2. 创建页面组件
touch src/app/(main)/dashboard/new-feature/page.tsx

# 3. 添加专用组件（可选）
mkdir src/app/(main)/dashboard/new-feature/_components
```

### 自定义主题
```css
/* 在 globals.css 中修改 CSS 变量 */
:root {
  --primary: 221.2 83.2% 53.3%;
  --secondary: 210 40% 96%;
  /* 更多主题变量... */
}
```

### 添加新组件
```bash
# 使用 shadcn CLI 添加组件
npx shadcn@latest add [component-name]

# 查看可用组件
npx shadcn@latest add --help
```

## 性能优化

- **服务端组件**：默认使用 Server Components 提升性能
- **代码分割**：基于路由的自动代码分割
- **图片优化**：Next.js Image 组件优化
- **字体优化**：自动字体优化和预加载

## 浏览器支持

- Chrome (最新版本)
- Firefox (最新版本)
- Safari (最新版本)
- Edge (最新版本)

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证。查看 [LICENSE](LICENSE) 文件了解详情。

---

> [!IMPORTANT]  
> 本项目经常更新。如果您从 fork 或之前克隆的副本工作，请在同步前检查最新更改。某些更新可能包含破坏性更改。

---

如有问题、功能请求或想要贡献改进建议，请随时开启 issue 或开始讨论。

<br />

**愉快编码！** 🚀
