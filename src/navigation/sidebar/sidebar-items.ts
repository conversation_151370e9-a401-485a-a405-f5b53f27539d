import {
  // 基础图标
  Home,
  type LucideIcon,

  // 工作台相关
  Briefcase,
  FileText,

  // 报表分析相关
  BarChart3,

  // 客户管理相关
  UserCheck,

  // AI智能中心相关
  Brain,

  // 系统设置相关
  Settings,

  // 运行流程
  Heart,
} from "lucide-react";

/**
 * 导航子项接口定义
 */
export interface NavSubItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  comingSoon?: boolean;
  newTab?: boolean;
  isNew?: boolean;
  subItems?: NavSubItem[]; // 支持多级嵌套
}

/**
 * 导航主项接口定义
 */
export interface NavMainItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  subItems?: NavSubItem[];
  comingSoon?: boolean;
  newTab?: boolean;
  isNew?: boolean;
}

/**
 * 导航分组接口定义
 */
export interface NavGroup {
  id: number;
  label?: string;
  items: NavMainItem[];
}

/**
 * 工单系统导航菜单配置
 * 基于业务需求文档设计的完整导航结构
 */
export const sidebarItems: NavGroup[] = [
  {
    id: 1,
    label: "导航菜单",
    items: [
      {
        title: "系统介绍",
        url: "/demo/default",
        icon: Home,
      },
      {
        title: "运行流程",
        url: "/demo/processing",
        icon: Heart,
      },
      {
        title: "我的工作台",
        url: "/demo/workbench",
        icon: Briefcase,
        subItems: [
          { title: "我的工单", url: "/demo/workbench/my-orders" },
          { title: "我发起的", url: "/demo/workbench/initiated" },
          { title: "我参与的", url: "/demo/workbench/participated" },
          { title: "我的审批", url: "/demo/workbench/approvals" },
        ],
      },
      {
        title: "工单管理",
        url: "/demo/orders",
        icon: FileText,
        subItems: [
          { title: "新建工单", url: "/demo/orders/create" },
          { title: "工单详情", url: "/demo/orders/detail" },
          { title: "工单查询", url: "/demo/orders/search" },
          { title: "回访队列", url: "/demo/orders/revisit-queue" },
          { title: "工单池", url: "/demo/orders/pool" },
        ],
      },
      {
        title: "报表与分析",
        url: "/demo/reports",
        icon: BarChart3,
        subItems: [
          { title: "服务运营驾驶舱", url: "/demo/reports/dashboard" },
          {
            title: "工单分析",
            url: "/demo/reports/orders",
            subItems: [
              { title: "工单总体统计", url: "/demo/reports/orders/statistics" },
              { title: "工单明细报表", url: "/demo/reports/orders/details" },
            ]
          },
          {
            title: "绩效与效率",
            url: "/demo/reports/performance",
            subItems: [
              { title: "个人绩效报表", url: "/demo/reports/performance/individual" },
              { title: "团队效率分析", url: "/demo/reports/performance/team" },
            ]
          },
          {
            title: "质量与满意度",
            url: "/demo/reports/quality",
            subItems: [
              { title: "SLA达成率报告", url: "/demo/reports/quality/sla" },
              { title: "客户满意度分析", url: "/demo/reports/quality/satisfaction" },
            ]
          },
          { title: "自定义报表", url: "/demo/reports/custom" },
        ],
      },
      {
        title: "客户管理",
        url: "/demo/customers",
        icon: UserCheck,
        subItems: [
          { title: "客户列表与详情", url: "/demo/customers/list" },
        ],
      },
      {
        title: "AI与智能中心",
        url: "/demo/ai",
        icon: Brain,
        subItems: [
          {
            title: "智能路由与派单",
            url: "/demo/ai/routing",
            subItems: [
              { title: "派单策略管理", url: "/demo/ai/routing/strategy" },
              { title: "技能与负载模型", url: "/demo/ai/routing/skills" },
            ]
          },
          {
            title: "智能推荐配置",
            url: "/demo/ai/recommendation",
            subItems: [
              { title: "相似工单推荐设置", url: "/demo/ai/recommendation/similar" },
              { title: "知识库文章推荐设置", url: "/demo/ai/recommendation/knowledge" },
            ]
          },
          {
            title: "自然语言处理配置",
            url: "/demo/ai/nlp",
            subItems: [
              { title: "意图识别与分类", url: "/demo/ai/nlp/intent" },
              { title: "情感分析设置", url: "/demo/ai/nlp/sentiment" },
            ]
          },
          {
            title: "AI辅助功能管理",
            url: "/demo/ai/assistant",
            subItems: [
              { title: "智能摘要与写作模型", url: "/demo/ai/assistant/writing" },
              { title: "审批建议模型", url: "/demo/ai/assistant/approval" },
            ]
          },
        ],
      },
      {
        title: "系统设置",
        url: "/demo/settings",
        icon: Settings,
        subItems: [
          {
            title: "流程配置",
            url: "/demo/settings/process",
            subItems: [
              { title: "工单模板管理", url: "/demo/settings/process/templates" },
              { title: "自动化规则", url: "/demo/settings/process/automation" },
              { title: "SLA策略设置", url: "/demo/settings/process/sla" },
            ]
          },
          {
            title: "业务配置",
            url: "/demo/settings/business",
            subItems: [
              { title: "工单标签管理", url: "/demo/settings/business/tags" },
              { title: "自定义回复模板", url: "/demo/settings/business/templates" },
              { title: "工单积分规则", url: "/demo/settings/business/points" },
            ]
          },
          {
            title: "系统管理",
            url: "/demo/settings/system",
            subItems: [
              { title: "集成与接口", url: "/demo/settings/system/integrations" },
              { title: "系统日志", url: "/demo/settings/system/logs" },
            ]
          },
        ],
      },
    ],
  },
];

