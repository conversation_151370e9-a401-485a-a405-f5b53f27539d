@import "tailwindcss";
@import "tw-animate-css";

/* Theme preset styles: these override CSS variables based on the selected data-theme-preset */
@import "../styles/presets/brutalist.css";
@import "../styles/presets/soft-pop.css";
@import "../styles/presets/tangerine.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Default theme styles (used when no data-theme-preset is set or when 'default' is selected).
These serve as the fallback; there is no separate default.css file. */
:root {
  --radius: 0.625rem;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.3211 0 0);
  --chart-2: oklch(0.4495 0 0);
  --chart-3: oklch(0.5693 0 0);
  --chart-4: oklch(0.6830 0 0);
  --chart-5: oklch(0.7921 0 0);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.9521 0 0);
  --chart-2: oklch(0.8576 0 0);
  --chart-3: oklch(0.7572 0 0);
  --chart-4: oklch(0.6534 0 0);
  --chart-5: oklch(0.5452 0 0);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground overscroll-none;
  }
}

.disable-transitions * {
  transition: none !important;
}

/* Sidebar 滚动条样式 - 增强版 */
[data-sidebar="sidebar"]::-webkit-scrollbar,
[data-sidebar="sidebar"] *::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-sidebar="sidebar"]::-webkit-scrollbar-track,
[data-sidebar="sidebar"] *::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

[data-sidebar="sidebar"]::-webkit-scrollbar-thumb,
[data-sidebar="sidebar"] *::-webkit-scrollbar-thumb {
  background: hsl(var(--sidebar-border));
  border-radius: 4px;
  border: 1px solid transparent;
  background-clip: padding-box;
  transition: all 0.2s ease;
}

[data-sidebar="sidebar"]::-webkit-scrollbar-thumb:hover,
[data-sidebar="sidebar"] *::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--sidebar-accent));
}

/* 暗黑模式下的滚动条 */
.dark [data-sidebar="sidebar"]::-webkit-scrollbar-thumb,
.dark [data-sidebar="sidebar"] *::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark [data-sidebar="sidebar"]::-webkit-scrollbar-thumb:hover,
.dark [data-sidebar="sidebar"] *::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 滚动条角落 */
[data-sidebar="sidebar"]::-webkit-scrollbar-corner,
[data-sidebar="sidebar"] *::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条 */
[data-sidebar="sidebar"],
[data-sidebar="sidebar"] * {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--sidebar-border)) transparent;
}

.dark [data-sidebar="sidebar"],
.dark [data-sidebar="sidebar"] * {
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}

/* 针对具体的滚动容器 */
[data-sidebar="sidebar"] .overflow-auto::-webkit-scrollbar,
[data-sidebar="sidebar"] .overflow-y-auto::-webkit-scrollbar,
[data-sidebar="sidebar"] .overflow-x-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-sidebar="sidebar"] .overflow-auto::-webkit-scrollbar-thumb,
[data-sidebar="sidebar"] .overflow-y-auto::-webkit-scrollbar-thumb,
[data-sidebar="sidebar"] .overflow-x-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--sidebar-border));
  border-radius: 4px;
  transition: background 0.2s ease;
}

.dark [data-sidebar="sidebar"] .overflow-auto::-webkit-scrollbar-thumb,
.dark [data-sidebar="sidebar"] .overflow-y-auto::-webkit-scrollbar-thumb,
.dark [data-sidebar="sidebar"] .overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}

.dark [data-sidebar="sidebar"] .overflow-auto::-webkit-scrollbar-thumb:hover,
.dark [data-sidebar="sidebar"] .overflow-y-auto::-webkit-scrollbar-thumb:hover,
.dark [data-sidebar="sidebar"] .overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* 滚动条淡入淡出效果 */
[data-sidebar="sidebar"] [data-slot="scroll-area-scrollbar"] {
  opacity: 0;
  transition: opacity 0.2s ease;
}

[data-sidebar="sidebar"]:hover [data-slot="scroll-area-scrollbar"] {
  opacity: 1;
}

/* 移动端滚动条隐藏 */
@media (max-width: 768px) {
  [data-sidebar="sidebar"] .scroll-area-viewport::-webkit-scrollbar {
    display: none;
  }
  
  [data-sidebar="sidebar"] .scroll-area-viewport {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}
