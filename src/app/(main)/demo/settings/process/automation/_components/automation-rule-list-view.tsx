"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Copy, 
  Trash2,
  Zap,
  GripVertical,
  CheckCircle,
  XCircle,
  FileText,
  Activity
} from "lucide-react"

interface AutomationRuleListViewProps {
  onCreateRule: () => void
  onEditRule: (rule: any) => void
}

/**
 * 自动化规则列表视图组件
 * 展示所有已创建的自动化规则，支持搜索、排序、启用/停用等操作
 */
export function AutomationRuleListView({ onCreateRule, onEditRule }: AutomationRuleListViewProps) {
  const [searchQuery, setSearchQuery] = useState("")

  // 模拟自动化规则数据
  const [rules, setRules] = useState([
    {
      id: "1",
      name: "高优先级投诉自动通知总监",
      description: "当收到高优先级客户投诉时，自动通知服务总监",
      triggerEvent: "工单创建",
      isEnabled: true,
      priority: 1,
      creator: "系统管理员",
      updatedAt: "2024-01-15 14:30:00",
      conditions: ["工单类型 = 客户投诉", "优先级 = 紧急"],
      actions: ["抄送给质量部经理", "发送邮件给服务总监"],
      executionCount: 45
    },
    {
      id: "2",
      name: "VIP客户工单自动分派",
      description: "VIP客户的工单自动分派给高级工程师",
      triggerEvent: "工单创建",
      isEnabled: true,
      priority: 2,
      creator: "张三",
      updatedAt: "2024-01-12 09:20:00",
      conditions: ["客户级别 = VIP"],
      actions: ["设置处理人为高级工程师组", "设置优先级为高"],
      executionCount: 128
    },
    {
      id: "3",
      name: "SLA超时自动升级",
      description: "当工单SLA即将超时时自动升级处理",
      triggerEvent: "SLA预警",
      isEnabled: false,
      priority: 3,
      creator: "李四",
      updatedAt: "2024-01-10 16:45:00",
      conditions: ["SLA剩余时间 < 30分钟", "工单状态 = 处理中"],
      actions: ["升级给上级处理人", "发送紧急通知"],
      executionCount: 23
    },
    {
      id: "4",
      name: "工单完成自动回访",
      description: "工单完成后自动安排客户满意度回访",
      triggerEvent: "工单状态变更",
      isEnabled: true,
      priority: 4,
      creator: "王五",
      updatedAt: "2024-01-08 11:15:00",
      conditions: ["工单状态 = 已完成"],
      actions: ["创建回访任务", "发送满意度调查邮件"],
      executionCount: 89
    }
  ])

  const filteredRules = rules.filter(rule =>
    rule.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rule.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleToggleRule = (ruleId: string) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId 
        ? { ...rule, isEnabled: !rule.isEnabled }
        : rule
    ))
  }

  const handleCopyRule = (rule: any) => {
    console.log("复制规则:", rule.name)
  }

  const handleViewLogs = (rule: any) => {
    console.log("查看执行日志:", rule.name)
  }

  const handleDeleteRule = (rule: any) => {
    console.log("删除规则:", rule.name)
  }

  const getTriggerEventBadge = (event: string) => {
    const eventColors = {
      "工单创建": "bg-blue-500",
      "工单状态变更": "bg-green-500",
      "SLA预警": "bg-orange-500",
      "工单分派": "bg-purple-500"
    }
    return eventColors[event] || "bg-gray-500"
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            自动化规则
          </h1>
          <p className="text-muted-foreground mt-2">
            创建和管理工单处理的自动化规则，提升工作效率和响应速度
          </p>
        </div>
        
        <Button onClick={onCreateRule} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          新建自动化规则
        </Button>
      </div>

      {/* 搜索和统计 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="搜索规则名称或描述..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>已启用: {rules.filter(r => r.isEnabled).length}</span>
              </div>
              <div className="flex items-center space-x-2">
                <XCircle className="h-4 w-4 text-gray-400" />
                <span>已停用: {rules.filter(r => !r.isEnabled).length}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4 text-blue-500" />
                <span>总计: {rules.length}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 规则列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>自动化规则列表</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">排序</TableHead>
                <TableHead>规则名称</TableHead>
                <TableHead>触发事件</TableHead>
                <TableHead>条件</TableHead>
                <TableHead>动作</TableHead>
                <TableHead>优先级</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>执行次数</TableHead>
                <TableHead>创建人</TableHead>
                <TableHead>最后修改</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRules.map((rule) => (
                <TableRow key={rule.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <TableCell>
                    <div className="flex items-center justify-center">
                      <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {rule.name}
                      </p>
                      <p className="text-sm text-muted-foreground mt-1">
                        {rule.description}
                      </p>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Badge 
                      className={`${getTriggerEventBadge(rule.triggerEvent)} text-white`}
                    >
                      {rule.triggerEvent}
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      {rule.conditions.slice(0, 2).map((condition, index) => (
                        <Badge key={index} variant="outline" className="text-xs block w-fit">
                          {condition}
                        </Badge>
                      ))}
                      {rule.conditions.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{rule.conditions.length - 2} 更多
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      {rule.actions.slice(0, 2).map((action, index) => (
                        <Badge key={index} variant="secondary" className="text-xs block w-fit">
                          {action}
                        </Badge>
                      ))}
                      {rule.actions.length > 2 && (
                        <Badge variant="secondary" className="text-xs">
                          +{rule.actions.length - 2} 更多
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Badge variant="secondary" className="font-mono">
                      {rule.priority}
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={rule.isEnabled}
                        onCheckedChange={() => handleToggleRule(rule.id)}
                      />
                      <span className={`text-sm ${
                        rule.isEnabled ? 'text-green-600' : 'text-gray-500'
                      }`}>
                        {rule.isEnabled ? '已启用' : '已停用'}
                      </span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Activity className="h-3 w-3 text-blue-500" />
                      <span className="text-sm font-medium">{rule.executionCount}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <span className="text-sm">{rule.creator}</span>
                  </TableCell>
                  
                  <TableCell>
                    <span className="text-sm text-muted-foreground">
                      {new Date(rule.updatedAt).toLocaleDateString()}
                    </span>
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEditRule(rule)}>
                          <Edit className="h-4 w-4 mr-2" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyRule(rule)}>
                          <Copy className="h-4 w-4 mr-2" />
                          复制
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleViewLogs(rule)}>
                          <FileText className="h-4 w-4 mr-2" />
                          查看执行日志
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteRule(rule)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredRules.length === 0 && (
            <div className="text-center py-12">
              <Zap className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                {searchQuery ? "没有找到匹配的自动化规则" : "还没有创建任何自动化规则"}
              </p>
              {!searchQuery && (
                <Button onClick={onCreateRule} className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  创建第一个自动化规则
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
        <CardContent className="p-4">
          <div className="flex items-start space-x-2">
            <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                自动化规则执行规则
              </h4>
              <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• 规则按优先级顺序执行，数字越小优先级越高</li>
                <li>• 同一事件可以触发多个规则，按优先级依次执行</li>
                <li>• 停用的规则不会被触发执行</li>
                <li>• 可以通过拖拽调整规则的执行顺序</li>
                <li>• 建议定期查看执行日志，确保规则正常运行</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
