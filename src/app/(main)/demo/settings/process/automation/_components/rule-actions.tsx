"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Plus, X, Zap, ChevronDown, ChevronRight, Settings, Mail, User, Edit } from "lucide-react"

interface RuleActionsProps {
  data: Array<{
    id: string
    type: string
    config: any
  }>
  onChange: (data: any) => void
}

/**
 * 规则动作组件
 * 用于设置自动化规则的执行动作
 */
export function RuleActions({ data, onChange }: RuleActionsProps) {
  const [expandedActions, setExpandedActions] = useState<string[]>([])

  // 动作类型
  const actionTypes = [
    {
      category: "更新工单属性",
      icon: Edit,
      actions: [
        { value: "update_status", label: "设置状态", description: "更改工单状态" },
        { value: "update_priority", label: "设置优先级", description: "更改工单优先级" },
        { value: "update_assignee", label: "设置处理人", description: "分配或重新分配处理人" },
        { value: "add_tags", label: "添加标签", description: "为工单添加标签" },
        { value: "update_custom_field", label: "修改自定义字段", description: "更新自定义字段的值" }
      ]
    },
    {
      category: "发送通知",
      icon: Mail,
      actions: [
        { value: "send_email", label: "发送邮件", description: "发送邮件通知" },
        { value: "send_sms", label: "发送短信", description: "发送短信通知" },
        { value: "send_internal_message", label: "发送站内信", description: "发送系统内部消息" }
      ]
    },
    {
      category: "执行人员操作",
      icon: User,
      actions: [
        { value: "add_comment", label: "添加评论/补记", description: "自动添加评论或补记" },
        { value: "assign_to", label: "指派给", description: "指派工单给特定人员" },
        { value: "add_collaborator", label: "添加协办方", description: "邀请协办人员" },
        { value: "copy_to", label: "抄送给", description: "抄送工单给相关人员" }
      ]
    },
    {
      category: "外部系统交互",
      icon: Settings,
      actions: [
        { value: "webhook", label: "调用Webhook", description: "向外部系统发送数据" },
        { value: "create_crm_record", label: "创建CRM记录", description: "在CRM系统中创建记录" }
      ]
    }
  ]

  const addAction = (actionType: string) => {
    const newAction = {
      id: `action_${Date.now()}`,
      type: actionType,
      config: getDefaultConfig(actionType)
    }
    onChange([...data, newAction])
    setExpandedActions(prev => [...prev, newAction.id])
  }

  const removeAction = (actionId: string) => {
    onChange(data.filter(action => action.id !== actionId))
    setExpandedActions(prev => prev.filter(id => id !== actionId))
  }

  const updateAction = (actionId: string, config: any) => {
    onChange(data.map(action => 
      action.id === actionId 
        ? { ...action, config }
        : action
    ))
  }

  const toggleExpanded = (actionId: string) => {
    setExpandedActions(prev => 
      prev.includes(actionId)
        ? prev.filter(id => id !== actionId)
        : [...prev, actionId]
    )
  }

  const getDefaultConfig = (actionType: string) => {
    const defaults = {
      update_status: { status: "" },
      update_priority: { priority: "" },
      update_assignee: { assignee: "" },
      add_tags: { tags: [] },
      update_custom_field: { field: "", value: "" },
      send_email: { recipient: "", template: "", subject: "", body: "" },
      send_sms: { recipient: "", message: "" },
      send_internal_message: { recipient: "", message: "" },
      add_comment: { content: "", isPrivate: false },
      assign_to: { assignee: "" },
      add_collaborator: { collaborator: "" },
      copy_to: { recipient: "" },
      webhook: { url: "", method: "POST", headers: {}, body: "" },
      create_crm_record: { recordType: "", fields: {} }
    }
    return defaults[actionType] || {}
  }

  const getActionTypeInfo = (actionType: string) => {
    for (const category of actionTypes) {
      const action = category.actions.find(a => a.value === actionType)
      if (action) return action
    }
    return null
  }

  const renderActionConfig = (action: any) => {
    const { type, config } = action

    switch (type) {
      case "update_status":
        return (
          <Select
            value={config.status || ""}
            onValueChange={(value) => updateAction(action.id, { ...config, status: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">待处理</SelectItem>
              <SelectItem value="in_progress">处理中</SelectItem>
              <SelectItem value="resolved">已解决</SelectItem>
              <SelectItem value="closed">已关闭</SelectItem>
            </SelectContent>
          </Select>
        )

      case "update_priority":
        return (
          <Select
            value={config.priority || ""}
            onValueChange={(value) => updateAction(action.id, { ...config, priority: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择优先级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="urgent">紧急</SelectItem>
              <SelectItem value="high">高</SelectItem>
              <SelectItem value="medium">中</SelectItem>
              <SelectItem value="low">低</SelectItem>
            </SelectContent>
          </Select>
        )

      case "send_email":
        return (
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium">收件人</label>
              <Select
                value={config.recipient || ""}
                onValueChange={(value) => updateAction(action.id, { ...config, recipient: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择收件人" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="assignee">处理人</SelectItem>
                  <SelectItem value="customer">客户</SelectItem>
                  <SelectItem value="manager">部门经理</SelectItem>
                  <SelectItem value="custom">自定义邮箱</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-xs font-medium">邮件主题</label>
              <Input
                value={config.subject || ""}
                onChange={(e) => updateAction(action.id, { ...config, subject: e.target.value })}
                placeholder="邮件主题"
              />
            </div>
            <div>
              <label className="text-xs font-medium">邮件内容</label>
              <Textarea
                value={config.body || ""}
                onChange={(e) => updateAction(action.id, { ...config, body: e.target.value })}
                placeholder="邮件内容，可使用变量如 {{ticket.id}}"
                rows={3}
              />
            </div>
          </div>
        )

      case "add_comment":
        return (
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium">评论内容</label>
              <Textarea
                value={config.content || ""}
                onChange={(e) => updateAction(action.id, { ...config, content: e.target.value })}
                placeholder="评论内容，可使用变量如 {{ticket.id}}"
                rows={3}
              />
            </div>
          </div>
        )

      case "webhook":
        return (
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium">Webhook URL</label>
              <Input
                value={config.url || ""}
                onChange={(e) => updateAction(action.id, { ...config, url: e.target.value })}
                placeholder="https://api.example.com/webhook"
              />
            </div>
            <div>
              <label className="text-xs font-medium">请求方法</label>
              <Select
                value={config.method || "POST"}
                onValueChange={(value) => updateAction(action.id, { ...config, method: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                  <SelectItem value="PATCH">PATCH</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )

      default:
        return (
          <div className="text-sm text-muted-foreground">
            请配置 {getActionTypeInfo(type)?.label} 的参数
          </div>
        )
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Zap className="h-5 w-5" />
          <span>动作 - "那么..."</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 添加动作按钮 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">
              执行动作 <span className="text-red-500">*</span>
            </label>
          </div>
          
          <div className="space-y-3">
            {actionTypes.map((category) => {
              const IconComponent = category.icon
              return (
                <div key={category.category}>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center space-x-2">
                    <IconComponent className="h-4 w-4" />
                    <span>{category.category}</span>
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {category.actions.map((action) => (
                      <Button
                        key={action.value}
                        variant="outline"
                        size="sm"
                        onClick={() => addAction(action.value)}
                        className="justify-start h-auto p-3"
                      >
                        <div className="text-left">
                          <div className="font-medium text-xs">{action.label}</div>
                          <div className="text-xs text-muted-foreground">{action.description}</div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* 已添加的动作列表 */}
        {data.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">已配置的动作</h4>
            <div className="space-y-3">
              {data.map((action, index) => {
                const isExpanded = expandedActions.includes(action.id)
                const actionInfo = getActionTypeInfo(action.type)
                
                return (
                  <Collapsible key={action.id} open={isExpanded} onOpenChange={() => toggleExpanded(action.id)}>
                    <div className="border rounded-lg">
                      <div className="w-full p-3 flex items-center justify-between">
                        <CollapsibleTrigger className="flex items-center space-x-3 flex-1 hover:bg-gray-50 dark:hover:bg-gray-800 rounded p-2">
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                          <Badge variant="secondary" className="text-xs">
                            动作 {index + 1}
                          </Badge>
                          <span className="font-medium">{actionInfo?.label}</span>
                          <Badge variant="outline" className="text-xs">
                            {actionInfo?.description}
                          </Badge>
                        </CollapsibleTrigger>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAction(action.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <CollapsibleContent>
                        <div className="p-3 border-t bg-gray-50 dark:bg-gray-800">
                          {renderActionConfig(action)}
                        </div>
                      </CollapsibleContent>
                    </div>
                  </Collapsible>
                )
              })}
            </div>
          </div>
        )}

        {data.length === 0 && (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
            <Zap className="h-8 w-8 mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-muted-foreground mb-4">
              还没有添加任何动作
            </p>
            <p className="text-xs text-muted-foreground">
              从上方选择要执行的动作类型
            </p>
          </div>
        )}

        {/* 使用说明 */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">
            动作执行说明
          </h4>
          <ul className="text-xs text-green-700 dark:text-green-300 space-y-1">
            <li>• 动作会按照添加的顺序依次执行</li>
            <li>• 可以在邮件和评论中使用变量，如 &#123;&#123;ticket.id&#125;&#125;、&#123;&#123;customer.name&#125;&#125;</li>
            <li>• Webhook 动作可以与外部系统集成</li>
            <li>• 建议测试规则确保动作按预期执行</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
