"use client"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Play, Clock, AlertTriangle } from "lucide-react"

interface RuleTriggersProps {
  data: {
    event: string
    details: any
  }
  onChange: (data: any) => void
}

/**
 * 规则触发器组件
 * 用于设置自动化规则的触发事件和相关参数
 */
export function RuleTriggers({ data, onChange }: RuleTriggersProps) {
  
  // 触发事件选项
  const triggerEvents = [
    {
      value: "ticket_created",
      label: "当工单被创建时",
      description: "新工单创建后立即触发",
      category: "工单生命周期",
      icon: "📝"
    },
    {
      value: "ticket_updated",
      label: "当工单被更新时",
      description: "工单任何字段发生变化时触发",
      category: "工单生命周期",
      icon: "✏️"
    },
    {
      value: "ticket_status_changed",
      label: "当工单状态变更时",
      description: "工单状态发生改变时触发",
      category: "工单生命周期",
      icon: "🔄"
    },
    {
      value: "ticket_assigned",
      label: "当工单被分配时",
      description: "工单被分配给处理人时触发",
      category: "工单生命周期",
      icon: "👤"
    },
    {
      value: "comment_added",
      label: "当添加新评论/补记时",
      description: "工单添加新的评论或补记时触发",
      category: "工单交互",
      icon: "💬"
    },
    {
      value: "customer_rating",
      label: "当收到客户评价时",
      description: "客户提交满意度评价时触发",
      category: "工单交互",
      icon: "⭐"
    },
    {
      value: "sla_warning",
      label: "当SLA即将超时时",
      description: "SLA剩余时间达到预警阈值时触发",
      category: "SLA管理",
      icon: "⚠️"
    },
    {
      value: "sla_breach",
      label: "当SLA已超时时",
      description: "SLA超过截止时间时触发",
      category: "SLA管理",
      icon: "🚨"
    },
    {
      value: "time_based",
      label: "基于时间的触发器",
      description: "按照设定的时间间隔定期触发",
      category: "定时任务",
      icon: "⏰"
    }
  ]

  const updateEvent = (event: string) => {
    onChange({
      event,
      details: {}
    })
  }

  const updateDetails = (key: string, value: any) => {
    onChange({
      ...data,
      details: {
        ...data.details,
        [key]: value
      }
    })
  }

  const getEventInfo = (eventValue: string) => {
    return triggerEvents.find(e => e.value === eventValue)
  }

  const renderEventDetails = () => {
    switch (data.event) {
      case "ticket_status_changed":
        return (
          <div className="space-y-3">
            <Label className="text-sm font-medium">状态变更详情</Label>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-xs text-muted-foreground">从状态</Label>
                <Select
                  value={data.details.fromStatus || ""}
                  onValueChange={(value) => updateDetails("fromStatus", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="任意状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">任意状态</SelectItem>
                    <SelectItem value="pending">待处理</SelectItem>
                    <SelectItem value="in_progress">处理中</SelectItem>
                    <SelectItem value="resolved">已解决</SelectItem>
                    <SelectItem value="closed">已关闭</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">到状态</Label>
                <Select
                  value={data.details.toStatus || ""}
                  onValueChange={(value) => updateDetails("toStatus", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="任意状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">任意状态</SelectItem>
                    <SelectItem value="pending">待处理</SelectItem>
                    <SelectItem value="in_progress">处理中</SelectItem>
                    <SelectItem value="resolved">已解决</SelectItem>
                    <SelectItem value="closed">已关闭</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )
      
      case "sla_warning":
        return (
          <div className="space-y-3">
            <Label className="text-sm font-medium">SLA预警设置</Label>
            <div className="flex items-center space-x-3">
              <Input
                type="number"
                min="1"
                value={data.details.warningThreshold || 30}
                onChange={(e) => updateDetails("warningThreshold", parseInt(e.target.value))}
                className="w-20"
              />
              <Select
                value={data.details.warningUnit || "minutes"}
                onValueChange={(value) => updateDetails("warningUnit", value)}
              >
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="minutes">分钟</SelectItem>
                  <SelectItem value="hours">小时</SelectItem>
                  <SelectItem value="days">天</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-muted-foreground">前触发</span>
            </div>
          </div>
        )
      
      case "time_based":
        return (
          <div className="space-y-3">
            <Label className="text-sm font-medium">定时设置</Label>
            <div className="space-y-3">
              <Select
                value={data.details.scheduleType || ""}
                onValueChange={(value) => updateDetails("scheduleType", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择定时类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="interval">按间隔执行</SelectItem>
                  <SelectItem value="daily">每天执行</SelectItem>
                  <SelectItem value="weekly">每周执行</SelectItem>
                  <SelectItem value="monthly">每月执行</SelectItem>
                </SelectContent>
              </Select>
              
              {data.details.scheduleType === "interval" && (
                <div className="flex items-center space-x-3">
                  <span className="text-sm">每</span>
                  <Input
                    type="number"
                    min="1"
                    value={data.details.intervalValue || 1}
                    onChange={(e) => updateDetails("intervalValue", parseInt(e.target.value))}
                    className="w-20"
                  />
                  <Select
                    value={data.details.intervalUnit || "hours"}
                    onValueChange={(value) => updateDetails("intervalUnit", value)}
                  >
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="minutes">分钟</SelectItem>
                      <SelectItem value="hours">小时</SelectItem>
                      <SelectItem value="days">天</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm">执行一次</span>
                </div>
              )}
            </div>
          </div>
        )
      
      default:
        return null
    }
  }

  const categories = [...new Set(triggerEvents.map(e => e.category))]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Play className="h-5 w-5" />
          <span>触发器 - "当..."</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 触发事件选择 */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">
            触发事件 <span className="text-red-500">*</span>
          </Label>
          <Select value={data.event} onValueChange={updateEvent}>
            <SelectTrigger>
              <SelectValue placeholder="选择触发事件" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <div key={category}>
                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                    {category}
                  </div>
                  {triggerEvents
                    .filter(event => event.category === category)
                    .map((event) => (
                      <SelectItem key={event.value} value={event.value}>
                        <div className="flex items-center space-x-2">
                          <span>{event.icon}</span>
                          <div>
                            <div className="font-medium">{event.label}</div>
                            <div className="text-xs text-muted-foreground">{event.description}</div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                </div>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            选择什么时候触发这个自动化规则
          </p>
        </div>

        {/* 当前选择的事件信息 */}
        {data.event && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <span className="text-lg">{getEventInfo(data.event)?.icon}</span>
              <div>
                <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  {getEventInfo(data.event)?.label}
                </h4>
                <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                  {getEventInfo(data.event)?.description}
                </p>
                <Badge variant="outline" className="mt-2 text-blue-700 border-blue-300">
                  {getEventInfo(data.event)?.category}
                </Badge>
              </div>
            </div>
          </div>
        )}

        {/* 事件详细设置 */}
        {data.event && renderEventDetails()}

        {/* 触发器说明 */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                触发器执行说明
              </h4>
              <ul className="text-xs text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• 触发器定义了规则何时被检查和执行</li>
                <li>• 每次触发事件发生时，系统会检查所有相关的启用规则</li>
                <li>• 基于时间的触发器会按照设定的时间间隔定期执行</li>
                <li>• SLA相关触发器会在SLA状态变化时自动执行</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
