"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Info } from "lucide-react"

interface RuleBasicInfoProps {
  data: {
    name: string
    description: string
    isEnabled: boolean
    priority: number
  }
  onNameChange: (name: string) => void
  onDescriptionChange: (description: string) => void
  onEnabledChange: (enabled: boolean) => void
  onPriorityChange: (priority: number) => void
}

/**
 * 规则基本信息组件
 * 用于设置自动化规则的名称、描述、状态和优先级
 */
export function RuleBasicInfo({ 
  data, 
  onNameChange, 
  onDescriptionChange, 
  onEnabledChange, 
  onPriorityChange 
}: RuleBasicInfoProps) {

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Info className="h-5 w-5" />
          <span>规则基本信息</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 规则名称 */}
        <div className="space-y-2">
          <Label htmlFor="rule-name" className="text-sm font-medium">
            规则名称 <span className="text-red-500">*</span>
          </Label>
          <Input
            id="rule-name"
            placeholder="例如：高优先级投诉自动通知总监"
            value={data.name}
            onChange={(e) => onNameChange(e.target.value)}
            className="w-full"
          />
          <p className="text-xs text-muted-foreground">
            为规则起一个清晰易懂的名称，便于识别和管理
          </p>
        </div>

        {/* 规则描述 */}
        <div className="space-y-2">
          <Label htmlFor="rule-description" className="text-sm font-medium">
            规则描述
          </Label>
          <Textarea
            id="rule-description"
            placeholder="描述此规则的目的和逻辑..."
            value={data.description}
            onChange={(e) => onDescriptionChange(e.target.value)}
            rows={3}
            className="w-full"
          />
          <p className="text-xs text-muted-foreground">
            详细说明规则的用途、触发条件和预期效果
          </p>
        </div>

        {/* 规则状态 */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">规则状态</Label>
          <div className="flex items-center space-x-3">
            <Switch
              checked={data.isEnabled}
              onCheckedChange={onEnabledChange}
            />
            <span className={`text-sm ${data.isEnabled ? 'text-green-600' : 'text-gray-500'}`}>
              {data.isEnabled ? '已启用' : '已停用'}
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            只有启用的规则才会在触发条件满足时执行
          </p>
        </div>

        {/* 执行优先级 */}
        <div className="space-y-2">
          <Label htmlFor="rule-priority" className="text-sm font-medium">
            执行优先级
          </Label>
          <div className="flex items-center space-x-3">
            <Input
              id="rule-priority"
              type="number"
              min="1"
              max="100"
              value={data.priority}
              onChange={(e) => onPriorityChange(parseInt(e.target.value) || 1)}
              className="w-24"
            />
            <span className="text-sm text-muted-foreground">
              数字越小，优先级越高
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            当同一事件触发多个规则时，系统会按优先级顺序执行
          </p>
        </div>

        {/* 优先级说明 */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            优先级设置建议
          </h4>
          <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
            <li>• 优先级 1-10：紧急规则，如安全事件、VIP客户处理</li>
            <li>• 优先级 11-50：重要规则，如SLA管理、自动分派</li>
            <li>• 优先级 51-100：常规规则，如通知、状态更新</li>
            <li>• 建议为不同类型的规则预留优先级区间</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
