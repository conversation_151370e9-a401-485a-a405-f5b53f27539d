"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, Save, X } from "lucide-react"
import { RuleBasicInfo } from "./rule-basic-info"
import { RuleTriggers } from "./rule-triggers"
import { RuleConditions } from "./rule-conditions"
import { RuleActions } from "./rule-actions"

interface AutomationRuleBuilderViewProps {
  editingRule: any
  onBackToList: () => void
}

/**
 * 自动化规则构建器视图组件
 * 提供创建和编辑自动化规则的完整界面
 */
export function AutomationRuleBuilderView({ editingRule, onBackToList }: AutomationRuleBuilderViewProps) {
  const [ruleData, setRuleData] = useState({
    name: "",
    description: "",
    isEnabled: true,
    priority: 1,
    trigger: {
      event: "",
      details: {}
    },
    conditions: {
      logic: "AND" as "AND" | "OR",
      rules: []
    },
    actions: []
  })

  // 根据编辑的规则加载数据
  useEffect(() => {
    if (editingRule) {
      setRuleData({
        name: editingRule.name,
        description: editingRule.description,
        isEnabled: editingRule.isEnabled,
        priority: editingRule.priority,
        trigger: {
          event: "ticket_created",
          details: {}
        },
        conditions: {
          logic: "AND",
          rules: [
            { field: "ticket_type", operator: "equals", value: "客户投诉" },
            { field: "priority", operator: "equals", value: "紧急" }
          ]
        },
        actions: [
          {
            id: "action_1",
            type: "send_notification",
            config: {
              recipient: "质量部经理",
              method: "email",
              template: "高优先级投诉通知"
            }
          },
          {
            id: "action_2",
            type: "update_field",
            config: {
              field: "assignee",
              value: "高级工程师组"
            }
          }
        ]
      })
    } else {
      // 新建规则时重置数据
      setRuleData({
        name: "",
        description: "",
        isEnabled: true,
        priority: 1,
        trigger: {
          event: "",
          details: {}
        },
        conditions: {
          logic: "AND",
          rules: []
        },
        actions: []
      })
    }
  }, [editingRule])

  const handleSave = () => {
    console.log("保存自动化规则:", ruleData)
    // 这里可以添加保存逻辑
    onBackToList()
  }

  const handleCancel = () => {
    onBackToList()
  }

  const updateRuleData = (section: string, data: any) => {
    setRuleData(prev => ({
      ...prev,
      [section]: data
    }))
  }

  const isFormValid = () => {
    return ruleData.name.trim() !== "" && 
           ruleData.trigger.event !== "" && 
           ruleData.conditions.rules.length > 0 && 
           ruleData.actions.length > 0
  }

  return (
    <div className="space-y-6">
      {/* 顶部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBackToList}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回列表
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold">
              {editingRule ? "编辑自动化规则" : "新建自动化规则"}
            </h1>
            <p className="text-sm text-muted-foreground mt-1">
              定义触发条件和执行动作，实现工单处理自动化
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {editingRule && (
            <Badge variant="outline" className="text-xs">
              编辑模式
            </Badge>
          )}
          <Button variant="outline" onClick={handleCancel}>
            <X className="h-4 w-4 mr-2" />
            取消
          </Button>
          <Button onClick={handleSave} disabled={!isFormValid()}>
            <Save className="h-4 w-4 mr-2" />
            保存规则
          </Button>
        </div>
      </div>

      {/* 规则构建区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：基本信息和触发器 */}
        <div className="space-y-6">
          <RuleBasicInfo
            data={{
              name: ruleData.name,
              description: ruleData.description,
              isEnabled: ruleData.isEnabled,
              priority: ruleData.priority
            }}
            onNameChange={(name) => setRuleData(prev => ({ ...prev, name }))}
            onDescriptionChange={(description) => setRuleData(prev => ({ ...prev, description }))}
            onEnabledChange={(isEnabled) => setRuleData(prev => ({ ...prev, isEnabled }))}
            onPriorityChange={(priority) => setRuleData(prev => ({ ...prev, priority }))}
          />
          
          <RuleTriggers
            data={ruleData.trigger}
            onChange={(trigger) => updateRuleData("trigger", trigger)}
          />
        </div>
        
        {/* 右侧：条件和动作 */}
        <div className="space-y-6">
          <RuleConditions
            data={ruleData.conditions}
            onChange={(conditions) => updateRuleData("conditions", conditions)}
          />
          
          <RuleActions
            data={ruleData.actions}
            onChange={(actions) => updateRuleData("actions", actions)}
          />
        </div>
      </div>

      {/* 规则预览和验证 */}
      <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
        <CardHeader>
          <CardTitle className="text-lg text-green-800 dark:text-green-200">
            规则预览
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-green-700 dark:text-green-300">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">基本信息</h4>
                <div className="space-y-1">
                  <div>规则名称: {ruleData.name || "未设置"}</div>
                  <div>优先级: {ruleData.priority}</div>
                  <div>状态: {ruleData.isEnabled ? "启用" : "停用"}</div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">触发器</h4>
                <div className="space-y-1">
                  <div>触发事件: {ruleData.trigger.event || "未设置"}</div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">条件</h4>
                <div className="space-y-1">
                  <div>条件逻辑: {ruleData.conditions.logic}</div>
                  <div>条件数量: {ruleData.conditions.rules.length} 个</div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">动作</h4>
                <div className="space-y-1">
                  <div>动作数量: {ruleData.actions.length} 个</div>
                  {ruleData.actions.map((action, index) => (
                    <div key={index}>• {action.type}</div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          {/* 规则逻辑描述 */}
          <div className="bg-white dark:bg-gray-900 border border-green-200 dark:border-green-800 rounded p-3">
            <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">
              规则逻辑描述
            </h4>
            <p className="text-sm text-green-700 dark:text-green-300">
              <strong>当</strong> {ruleData.trigger.event ? "工单被创建" : "触发事件"} 时，
              <strong>如果</strong> 满足 {ruleData.conditions.rules.length} 个条件（{ruleData.conditions.logic}逻辑），
              <strong>那么</strong> 执行 {ruleData.actions.length} 个动作。
            </p>
          </div>
          
          {!isFormValid() && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded p-3">
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                请完成以下必填项：
              </h4>
              <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                {!ruleData.name.trim() && <li>• 规则名称</li>}
                {!ruleData.trigger.event && <li>• 触发事件</li>}
                {ruleData.conditions.rules.length === 0 && <li>• 至少添加一个条件</li>}
                {ruleData.actions.length === 0 && <li>• 至少添加一个动作</li>}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
