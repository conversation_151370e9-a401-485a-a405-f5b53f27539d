"use client"

import { useState } from "react"
import { AutomationRuleListView } from "./_components/automation-rule-list-view"
import { AutomationRuleBuilderView } from "./_components/automation-rule-builder-view"

/**
 * 自动化规则页面
 * 支持自动化规则列表视图和规则构建器视图两种模式
 */
export default function AutomationRulePage() {
  const [currentView, setCurrentView] = useState<"list" | "builder">("list")
  const [editingRule, setEditingRule] = useState<any>(null)

  const handleCreateRule = () => {
    setEditingRule(null)
    setCurrentView("builder")
  }

  const handleEditRule = (rule: any) => {
    setEditingRule(rule)
    setCurrentView("builder")
  }

  const handleBackToList = () => {
    setCurrentView("list")
    setEditingRule(null)
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {currentView === "list" ? (
        <AutomationRuleListView 
          onCreateRule={handleCreateRule}
          onEditRule={handleEditRule}
        />
      ) : (
        <AutomationRuleBuilderView 
          editingRule={editingRule}
          onBackToList={handleBackToList}
        />
      )}
    </div>
  )
}
