"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { 
  Info, 
  FileText, 
  Monitor, 
  MessageSquare, 
  ShoppingCart, 
  Wrench, 
  AlertTriangle,
  Users,
  Settings,
  Heart
} from "lucide-react"

interface TemplateBasicInfoProps {
  data: {
    name: string
    description: string
    icon: string
    isEnabled: boolean
    business: string
  }
  onChange: (data: any) => void
}

/**
 * 模板基本信息组件
 * 用于设置模板的名称、描述、图标、状态和关联业务
 */
export function TemplateBasicInfo({ data, onChange }: TemplateBasicInfoProps) {
  
  // 可选图标
  const iconOptions = [
    { value: "FileText", label: "文档", icon: FileText },
    { value: "Monitor", label: "设备", icon: Monitor },
    { value: "MessageSquare", label: "消息", icon: MessageSquare },
    { value: "ShoppingCart", label: "采购", icon: ShoppingCart },
    { value: "Wrench", label: "维修", icon: Wrench },
    { value: "AlertTriangle", label: "警告", icon: AlertTriangle },
    { value: "Users", label: "用户", icon: Users },
    { value: "Settings", label: "设置", icon: Settings },
    { value: "Heart", label: "服务", icon: Heart }
  ]

  // 业务类型选项
  const businessOptions = [
    "IT服务",
    "客户服务",
    "行政管理",
    "技术支持",
    "设备管理",
    "人力资源",
    "财务管理",
    "质量管理",
    "项目管理",
    "其他"
  ]

  const updateField = (field: string, value: any) => {
    onChange({
      ...data,
      [field]: value
    })
  }

  const getSelectedIcon = () => {
    return iconOptions.find(option => option.value === data.icon) || iconOptions[0]
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 左侧：基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="h-5 w-5" />
            <span>基本信息</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 模板名称 */}
          <div className="space-y-2">
            <Label htmlFor="template-name" className="text-sm font-medium">
              模板名称 <span className="text-red-500">*</span>
            </Label>
            <Input
              id="template-name"
              placeholder="例如：IT设备报修单"
              value={data.name}
              onChange={(e) => updateField("name", e.target.value)}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              为模板起一个清晰易懂的名称，便于用户识别和选择
            </p>
          </div>

          {/* 模板描述 */}
          <div className="space-y-2">
            <Label htmlFor="template-description" className="text-sm font-medium">
              模板描述
            </Label>
            <Textarea
              id="template-description"
              placeholder="描述此模板的适用场景和用途..."
              value={data.description}
              onChange={(e) => updateField("description", e.target.value)}
              rows={3}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              详细说明模板的用途和适用场景，帮助用户正确选择
            </p>
          </div>

          {/* 关联业务 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              关联业务 <span className="text-red-500">*</span>
            </Label>
            <Select value={data.business} onValueChange={(value) => updateField("business", value)}>
              <SelectTrigger>
                <SelectValue placeholder="选择业务类型" />
              </SelectTrigger>
              <SelectContent>
                {businessOptions.map((business) => (
                  <SelectItem key={business} value={business}>
                    {business}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              选择此模板所属的业务领域，便于分类管理
            </p>
          </div>

          {/* 模板状态 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">模板状态</Label>
            <div className="flex items-center space-x-3">
              <Switch
                checked={data.isEnabled}
                onCheckedChange={(checked) => updateField("isEnabled", checked)}
              />
              <span className={`text-sm ${data.isEnabled ? 'text-green-600' : 'text-gray-500'}`}>
                {data.isEnabled ? '已启用' : '已停用'}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              启用后，用户在新建工单时可以看到并使用此模板
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 右侧：图标选择和预览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>模板图标</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 当前选择的图标预览 */}
          <div className="text-center p-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
            <div className="flex flex-col items-center space-y-3">
              {(() => {
                const SelectedIcon = getSelectedIcon().icon
                return <SelectedIcon className="h-16 w-16 text-blue-500" />
              })()}
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {data.name || "模板名称"}
                </p>
                <p className="text-sm text-muted-foreground">
                  {getSelectedIcon().label}图标
                </p>
              </div>
            </div>
          </div>

          {/* 图标选择网格 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">选择图标</Label>
            <div className="grid grid-cols-3 gap-3">
              {iconOptions.map((option) => {
                const IconComponent = option.icon
                const isSelected = data.icon === option.value
                
                return (
                  <Button
                    key={option.value}
                    variant={isSelected ? "default" : "outline"}
                    onClick={() => updateField("icon", option.value)}
                    className="h-16 flex flex-col items-center space-y-1 p-2"
                  >
                    <IconComponent className="h-6 w-6" />
                    <span className="text-xs">{option.label}</span>
                  </Button>
                )
              })}
            </div>
            <p className="text-xs text-muted-foreground">
              选择一个直观的图标，帮助用户快速识别模板类型
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 配置提示 */}
      <div className="lg:col-span-2">
        <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
          <CardContent className="p-4">
            <div className="flex items-start space-x-2">
              <Info className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                  配置建议
                </h4>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• 模板名称应该简洁明了，能够准确反映其用途</li>
                  <li>• 选择合适的图标有助于用户快速识别和选择模板</li>
                  <li>• 详细的描述可以帮助用户了解模板的适用场景</li>
                  <li>• 关联业务有助于对模板进行分类管理和权限控制</li>
                  <li>• 建议先停用状态下完成所有配置，测试无误后再启用</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
