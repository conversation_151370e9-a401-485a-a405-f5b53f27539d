"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Copy, 
  Trash2,
  FileText,
  CheckCircle,
  XCircle,
  Monitor,
  MessageSquare,
  ShoppingCart,
  Wrench,
  AlertTriangle
} from "lucide-react"

interface TemplateListViewProps {
  onCreateTemplate: () => void
  onEditTemplate: (template: any) => void
}

/**
 * 模板列表视图组件
 * 展示所有已创建的工单模板，支持搜索、启用/停用等操作
 */
export function TemplateListView({ onCreateTemplate, onEditTemplate }: TemplateListViewProps) {
  const [searchQuery, setSearchQuery] = useState("")

  // 模拟工单模板数据
  const [templates, setTemplates] = useState([
    {
      id: "1",
      name: "IT设备报修单",
      description: "用于IT设备故障报修和维护申请",
      business: "IT服务",
      icon: Monitor,
      isEnabled: true,
      creator: "系统管理员",
      createdAt: "2024-01-15 14:30:00",
      updatedAt: "2024-01-20 09:15:00",
      usageCount: 156,
      fieldCount: 8
    },
    {
      id: "2",
      name: "客户投诉与建议",
      description: "处理客户投诉和意见建议的标准模板",
      business: "客户服务",
      icon: MessageSquare,
      isEnabled: true,
      creator: "张三",
      createdAt: "2024-01-12 10:20:00",
      updatedAt: "2024-01-18 16:45:00",
      usageCount: 89,
      fieldCount: 12
    },
    {
      id: "3",
      name: "采购申请单",
      description: "办公用品和设备采购申请流程",
      business: "行政管理",
      icon: ShoppingCart,
      isEnabled: false,
      creator: "李四",
      createdAt: "2024-01-10 15:30:00",
      updatedAt: "2024-01-15 11:20:00",
      usageCount: 45,
      fieldCount: 10
    },
    {
      id: "4",
      name: "系统故障报告",
      description: "系统异常和故障的快速报告模板",
      business: "技术支持",
      icon: AlertTriangle,
      isEnabled: true,
      creator: "王五",
      createdAt: "2024-01-08 09:45:00",
      updatedAt: "2024-01-19 14:30:00",
      usageCount: 234,
      fieldCount: 6
    },
    {
      id: "5",
      name: "设备维护申请",
      description: "定期设备维护和保养申请",
      business: "设备管理",
      icon: Wrench,
      isEnabled: true,
      creator: "赵六",
      createdAt: "2024-01-05 13:15:00",
      updatedAt: "2024-01-17 10:30:00",
      usageCount: 67,
      fieldCount: 9
    }
  ])

  const filteredTemplates = templates.filter(template =>
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.business.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleToggleTemplate = (templateId: string) => {
    setTemplates(prev => prev.map(template => 
      template.id === templateId 
        ? { ...template, isEnabled: !template.isEnabled }
        : template
    ))
  }

  const handleCopyTemplate = (template: any) => {
    console.log("复制模板:", template.name)
  }

  const handleDeleteTemplate = (template: any) => {
    console.log("删除模板:", template.name)
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            工单模板管理
          </h1>
          <p className="text-muted-foreground mt-2">
            创建和管理工单模板，定义表单结构和关联流程，提供标准化的工单创建体验
          </p>
        </div>
        
        <Button onClick={onCreateTemplate} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          新建工单模板
        </Button>
      </div>

      {/* 搜索和统计 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="搜索模板名称、描述或关联业务..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>已启用: {templates.filter(t => t.isEnabled).length}</span>
              </div>
              <div className="flex items-center space-x-2">
                <XCircle className="h-4 w-4 text-gray-400" />
                <span>已停用: {templates.filter(t => !t.isEnabled).length}</span>
              </div>
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4 text-blue-500" />
                <span>总计: {templates.length}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 模板列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>模板列表</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>模板名称</TableHead>
                <TableHead>关联业务</TableHead>
                <TableHead>字段数量</TableHead>
                <TableHead>使用次数</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建人</TableHead>
                <TableHead>最后修改</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTemplates.map((template) => {
                const IconComponent = template.icon
                
                return (
                  <TableRow key={template.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <IconComponent className="h-8 w-8 text-blue-500" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {template.name}
                          </p>
                          <p className="text-sm text-muted-foreground mt-1">
                            {template.description}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {template.business}
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <FileText className="h-3 w-3 text-gray-400" />
                        <span className="text-sm font-medium">{template.fieldCount}</span>
                        <span className="text-xs text-muted-foreground">个字段</span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <span className="text-sm font-medium">{template.usageCount}</span>
                        <span className="text-xs text-muted-foreground">次</span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={template.isEnabled}
                          onCheckedChange={() => handleToggleTemplate(template.id)}
                        />
                        <span className={`text-sm ${
                          template.isEnabled ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          {template.isEnabled ? '已启用' : '已停用'}
                        </span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <span className="text-sm">{template.creator}</span>
                    </TableCell>
                    
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {new Date(template.updatedAt).toLocaleDateString()}
                      </span>
                    </TableCell>
                    
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onEditTemplate(template)}>
                            <Edit className="h-4 w-4 mr-2" />
                            编辑
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleCopyTemplate(template)}>
                            <Copy className="h-4 w-4 mr-2" />
                            复制
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteTemplate(template)}
                            className="text-red-600"
                            disabled={template.usageCount > 0}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
          
          {filteredTemplates.length === 0 && (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                {searchQuery ? "没有找到匹配的工单模板" : "还没有创建任何工单模板"}
              </p>
              {!searchQuery && (
                <Button onClick={onCreateTemplate} className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  创建第一个模板
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
        <CardContent className="p-4">
          <div className="flex items-start space-x-2">
            <FileText className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                模板管理说明
              </h4>
              <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• 启用的模板会在"新建工单"页面对用户可见</li>
                <li>• 已被使用过的模板不能删除，只能停用</li>
                <li>• 复制模板可以快速创建相似配置的新模板</li>
                <li>• 建议为不同业务场景创建专门的模板</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
