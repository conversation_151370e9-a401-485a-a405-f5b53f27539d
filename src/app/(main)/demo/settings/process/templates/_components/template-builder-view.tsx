"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Save, Eye, X } from "lucide-react"
import { TemplateBasicInfo } from "./template-basic-info"
import { TemplateFormBuilder } from "./template-form-builder"
import { TemplateProcessConfig } from "./template-process-config"
import { TemplatePreview } from "./template-preview"

interface TemplateBuilderViewProps {
  editingTemplate: any
  onBackToList: () => void
}

/**
 * 模板构建器视图组件
 * 提供创建和编辑工单模板的完整界面，采用多标签页布局
 */
export function TemplateBuilderView({ editingTemplate, onBackToList }: TemplateBuilderViewProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [templateData, setTemplateData] = useState({
    name: "",
    description: "",
    icon: "FileText",
    isEnabled: true,
    business: "",
    fields: [],
    processConfig: {
      defaultAssignee: "",
      defaultCollaborators: [],
      slaPolicy: "",
      automationRules: [],
      defaultPriority: "medium"
    }
  })

  // 根据编辑的模板加载数据
  useEffect(() => {
    if (editingTemplate) {
      setTemplateData({
        name: editingTemplate.name,
        description: editingTemplate.description,
        icon: editingTemplate.icon?.name || "FileText",
        isEnabled: editingTemplate.isEnabled,
        business: editingTemplate.business,
        fields: [
          {
            id: "field_1",
            type: "text",
            label: "问题标题",
            placeholder: "请简要描述问题",
            required: true,
            readonly: false,
            helpText: "请用一句话概括您遇到的问题",
            order: 1
          },
          {
            id: "field_2",
            type: "textarea",
            label: "问题详细描述",
            placeholder: "请详细描述问题的具体情况...",
            required: true,
            readonly: false,
            helpText: "请尽可能详细地描述问题，包括出现时间、频率等",
            order: 2
          },
          {
            id: "field_3",
            type: "select",
            label: "设备类型",
            required: true,
            readonly: false,
            options: ["台式电脑", "笔记本电脑", "打印机", "网络设备", "其他"],
            order: 3
          },
          {
            id: "field_4",
            type: "file",
            label: "相关附件",
            required: false,
            readonly: false,
            helpText: "可上传错误截图或相关文档",
            fileTypes: ["image/*", ".pdf", ".doc", ".docx"],
            maxSize: "10MB",
            order: 4
          }
        ],
        processConfig: {
          defaultAssignee: "IT支持组",
          defaultCollaborators: [],
          slaPolicy: "IT服务SLA",
          automationRules: ["高优先级自动通知"],
          defaultPriority: "medium"
        }
      })
    } else {
      // 新建模板时重置数据
      setTemplateData({
        name: "",
        description: "",
        icon: "FileText",
        isEnabled: true,
        business: "",
        fields: [],
        processConfig: {
          defaultAssignee: "",
          defaultCollaborators: [],
          slaPolicy: "",
          automationRules: [],
          defaultPriority: "medium"
        }
      })
    }
  }, [editingTemplate])

  const handleSave = () => {
    console.log("保存模板:", templateData)
    // 这里可以添加保存逻辑
    onBackToList()
  }

  const handleSaveAndPublish = () => {
    console.log("保存并发布模板:", { ...templateData, isEnabled: true })
    // 这里可以添加保存并发布逻辑
    onBackToList()
  }

  const handleCancel = () => {
    onBackToList()
  }

  const updateTemplateData = (section: string, data: any) => {
    setTemplateData(prev => ({
      ...prev,
      [section]: data
    }))
  }

  const isFormValid = () => {
    return templateData.name.trim() !== "" && 
           templateData.business.trim() !== "" && 
           templateData.fields.length > 0
  }

  const getTabValidation = (tab: string) => {
    switch (tab) {
      case "basic":
        return templateData.name.trim() !== "" && templateData.business.trim() !== ""
      case "form":
        return templateData.fields.length > 0
      case "process":
        return true // 流程配置是可选的
      case "preview":
        return isFormValid()
      default:
        return false
    }
  }

  return (
    <div className="space-y-6">
      {/* 顶部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBackToList}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回列表
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold">
              {editingTemplate ? "编辑工单模板" : "新建工单模板"}
            </h1>
            <p className="text-sm text-muted-foreground mt-1">
              设计表单结构，配置关联流程，创建标准化的工单模板
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {editingTemplate && (
            <Badge variant="outline" className="text-xs">
              编辑模式
            </Badge>
          )}
          <Button variant="outline" onClick={handleCancel}>
            <X className="h-4 w-4 mr-2" />
            取消
          </Button>
          <Button variant="outline" onClick={handleSave} disabled={!isFormValid()}>
            <Save className="h-4 w-4 mr-2" />
            保存
          </Button>
          <Button onClick={handleSaveAndPublish} disabled={!isFormValid()}>
            <Eye className="h-4 w-4 mr-2" />
            保存并发布
          </Button>
        </div>
      </div>

      {/* 多标签页构建器 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic" className="relative">
            基本信息
            {getTabValidation("basic") && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"></div>
            )}
          </TabsTrigger>
          <TabsTrigger value="form" className="relative">
            表单设计器
            {getTabValidation("form") && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"></div>
            )}
          </TabsTrigger>
          <TabsTrigger value="process" className="relative">
            流程配置
            {getTabValidation("process") && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"></div>
            )}
          </TabsTrigger>
          <TabsTrigger value="preview" className="relative" disabled={!isFormValid()}>
            预览
            {getTabValidation("preview") && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"></div>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          <TemplateBasicInfo
            data={{
              name: templateData.name,
              description: templateData.description,
              icon: templateData.icon,
              isEnabled: templateData.isEnabled,
              business: templateData.business
            }}
            onChange={(data) => updateTemplateData("basic", data)}
          />
        </TabsContent>

        <TabsContent value="form" className="space-y-6">
          <TemplateFormBuilder
            fields={templateData.fields}
            onChange={(fields) => updateTemplateData("fields", fields)}
          />
        </TabsContent>

        <TabsContent value="process" className="space-y-6">
          <TemplateProcessConfig
            data={templateData.processConfig}
            onChange={(data) => updateTemplateData("processConfig", data)}
          />
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <TemplatePreview
            templateData={templateData}
          />
        </TabsContent>
      </Tabs>

      {/* 进度提示 */}
      <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">
          配置进度
        </h4>
        <div className="grid grid-cols-4 gap-4 text-xs">
          <div className={`flex items-center space-x-2 ${getTabValidation("basic") ? "text-green-600" : "text-gray-500"}`}>
            <div className={`w-2 h-2 rounded-full ${getTabValidation("basic") ? "bg-green-500" : "bg-gray-300"}`}></div>
            <span>基本信息</span>
          </div>
          <div className={`flex items-center space-x-2 ${getTabValidation("form") ? "text-green-600" : "text-gray-500"}`}>
            <div className={`w-2 h-2 rounded-full ${getTabValidation("form") ? "bg-green-500" : "bg-gray-300"}`}></div>
            <span>表单设计</span>
          </div>
          <div className={`flex items-center space-x-2 ${getTabValidation("process") ? "text-green-600" : "text-gray-500"}`}>
            <div className={`w-2 h-2 rounded-full ${getTabValidation("process") ? "bg-green-500" : "bg-gray-300"}`}></div>
            <span>流程配置</span>
          </div>
          <div className={`flex items-center space-x-2 ${getTabValidation("preview") ? "text-green-600" : "text-gray-500"}`}>
            <div className={`w-2 h-2 rounded-full ${getTabValidation("preview") ? "bg-green-500" : "bg-gray-300"}`}></div>
            <span>预览测试</span>
          </div>
        </div>
      </div>
    </div>
  )
}
