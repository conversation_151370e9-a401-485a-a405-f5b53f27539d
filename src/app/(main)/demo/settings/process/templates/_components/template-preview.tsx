"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Eye, 
  FileText, 
  Monitor, 
  MessageSquare, 
  ShoppingCart, 
  Wrench, 
  AlertTriangle,
  Users,
  Settings,
  Heart,
  Upload,
  Calendar
} from "lucide-react"

interface TemplatePreviewProps {
  templateData: {
    name: string
    description: string
    icon: string
    business: string
    fields: any[]
    processConfig: any
  }
}

/**
 * 模板预览组件
 * 展示模板的最终效果，模拟用户使用体验
 */
export function TemplatePreview({ templateData }: TemplatePreviewProps) {
  
  // 图标映射
  const iconMap = {
    FileText,
    Monitor,
    MessageSquare,
    ShoppingCart,
    Wrench,
    AlertTriangle,
    Users,
    Settings,
    Heart
  }

  const getIcon = (iconName: string) => {
    return iconMap[iconName as keyof typeof iconMap] || FileText
  }

  const renderField = (field: any) => {
    const baseProps = {
      id: field.id,
      placeholder: field.placeholder,
      disabled: field.readonly
    }

    switch (field.type) {
      case "text":
      case "email":
      case "phone":
      case "url":
        return (
          <Input
            {...baseProps}
            type={field.type === "text" ? "text" : field.type}
          />
        )

      case "textarea":
        return (
          <Textarea
            {...baseProps}
            rows={3}
          />
        )

      case "number":
        return (
          <Input
            {...baseProps}
            type="number"
            min={field.min}
            max={field.max}
            step={field.step}
          />
        )

      case "select":
        return (
          <Select>
            <SelectTrigger>
              <SelectValue placeholder={field.placeholder || "请选择"} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option: string, index: number) => (
                <SelectItem key={index} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case "multiselect":
        return (
          <div className="space-y-2">
            {field.options?.map((option: string, index: number) => (
              <div key={index} className="flex items-center space-x-2">
                <Checkbox id={`${field.id}_${index}`} />
                <Label htmlFor={`${field.id}_${index}`} className="text-sm">
                  {option}
                </Label>
              </div>
            ))}
          </div>
        )

      case "radio":
        return (
          <RadioGroup>
            {field.options?.map((option: string, index: number) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`${field.id}_${index}`} />
                <Label htmlFor={`${field.id}_${index}`} className="text-sm">
                  {option}
                </Label>
              </div>
            ))}
          </RadioGroup>
        )

      case "checkbox":
        return (
          <div className="space-y-2">
            {field.options?.map((option: string, index: number) => (
              <div key={index} className="flex items-center space-x-2">
                <Checkbox id={`${field.id}_${index}`} />
                <Label htmlFor={`${field.id}_${index}`} className="text-sm">
                  {option}
                </Label>
              </div>
            ))}
          </div>
        )

      case "date":
      case "datetime":
        return (
          <div className="flex items-center space-x-2">
            <Input
              {...baseProps}
              type={field.type === "date" ? "date" : "datetime-local"}
            />
            <Calendar className="h-4 w-4 text-gray-400" />
          </div>
        )

      case "file":
        return (
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center">
            <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-gray-600 dark:text-gray-400">
              点击上传或拖拽文件到此处
            </p>
            {field.fileTypes && (
              <p className="text-xs text-gray-500 mt-1">
                支持格式：{field.fileTypes.join(", ")}
              </p>
            )}
            {field.maxSize && (
              <p className="text-xs text-gray-500">
                最大大小：{field.maxSize}
              </p>
            )}
          </div>
        )

      default:
        return (
          <Input {...baseProps} />
        )
    }
  }

  const IconComponent = getIcon(templateData.icon)

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 左侧：模板信息预览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <span>模板信息</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 模板卡片预览 */}
          <div className="border rounded-lg p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <IconComponent className="h-12 w-12 text-blue-500" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {templateData.name || "模板名称"}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {templateData.description || "模板描述"}
                </p>
                <div className="flex items-center space-x-2 mt-2">
                  <Badge variant="outline" className="text-xs">
                    {templateData.business || "业务类型"}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {templateData.fields.length} 个字段
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* 流程配置预览 */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900 dark:text-white">关联流程配置</h4>
            
            <div className="grid grid-cols-1 gap-3">
              {templateData.processConfig.defaultAssignee && (
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded">
                  <span className="text-sm text-gray-600 dark:text-gray-400">默认处理人</span>
                  <span className="text-sm font-medium">{templateData.processConfig.defaultAssignee}</span>
                </div>
              )}
              
              {templateData.processConfig.slaPolicy && (
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded">
                  <span className="text-sm text-gray-600 dark:text-gray-400">SLA策略</span>
                  <span className="text-sm font-medium">{templateData.processConfig.slaPolicy}</span>
                </div>
              )}
              
              {templateData.processConfig.defaultPriority && (
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded">
                  <span className="text-sm text-gray-600 dark:text-gray-400">默认优先级</span>
                  <Badge variant="outline" className="text-xs">
                    {templateData.processConfig.defaultPriority}
                  </Badge>
                </div>
              )}
              
              {templateData.processConfig.automationRules.length > 0 && (
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">自动化规则</span>
                    <Badge variant="secondary" className="text-xs">
                      {templateData.processConfig.automationRules.length} 个
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    {templateData.processConfig.automationRules.map((rule: string, index: number) => (
                      <div key={index} className="text-xs text-gray-500">
                        • {rule}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 右侧：表单预览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>表单预览</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          {templateData.fields.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm">还没有添加任何字段</p>
            </div>
          ) : (
            <div className="space-y-6">
              {templateData.fields
                .sort((a, b) => a.order - b.order)
                .map((field) => (
                  <div key={field.id} className="space-y-2">
                    <Label className="text-sm font-medium flex items-center space-x-1">
                      <span>{field.label}</span>
                      {field.required && <span className="text-red-500">*</span>}
                    </Label>
                    
                    {renderField(field)}
                    
                    {field.helpText && (
                      <p className="text-xs text-gray-500">{field.helpText}</p>
                    )}
                  </div>
                ))}
              
              {/* 提交按钮 */}
              <div className="pt-4 border-t">
                <div className="flex items-center space-x-3">
                  <Button className="flex-1">
                    提交工单
                  </Button>
                  <Button variant="outline">
                    保存草稿
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <div className="lg:col-span-2">
        <Card className="border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20">
          <CardContent className="p-4">
            <div className="flex items-start space-x-2">
              <Eye className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                  预览说明
                </h4>
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  <li>• 此预览展示了用户在"新建工单"页面看到的实际效果</li>
                  <li>• 表单字段按照设置的顺序显示，必填字段标有红色星号</li>
                  <li>• 流程配置将在工单创建时自动应用</li>
                  <li>• 建议在发布前充分测试表单的可用性和流程的正确性</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
