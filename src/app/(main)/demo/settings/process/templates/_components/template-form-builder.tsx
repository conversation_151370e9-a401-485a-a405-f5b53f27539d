"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  X, 
  GripVertical, 
  Type, 
  AlignLeft, 
  List, 
  Calendar, 
  FileText, 
  Upload,
  ToggleLeft,
  Hash,
  Mail,
  Phone,
  Link,
  Eye
} from "lucide-react"

interface TemplateFormBuilderProps {
  fields: any[]
  onChange: (fields: any[]) => void
}

/**
 * 表单构建器组件
 * 提供拖拽式表单字段设计功能
 */
export function TemplateFormBuilder({ fields, onChange }: TemplateFormBuilderProps) {
  const [selectedField, setSelectedField] = useState<any>(null)

  // 字段类型选项
  const fieldTypes = [
    { value: "text", label: "单行文本", icon: Type, description: "短文本输入" },
    { value: "textarea", label: "多行文本", icon: AlignLeft, description: "长文本输入" },
    { value: "select", label: "下拉选择", icon: List, description: "单选下拉框" },
    { value: "multiselect", label: "多选", icon: List, description: "多选下拉框" },
    { value: "radio", label: "单选按钮", icon: ToggleLeft, description: "单选按钮组" },
    { value: "checkbox", label: "复选框", icon: ToggleLeft, description: "多选复选框" },
    { value: "date", label: "日期", icon: Calendar, description: "日期选择器" },
    { value: "datetime", label: "日期时间", icon: Calendar, description: "日期时间选择器" },
    { value: "number", label: "数字", icon: Hash, description: "数字输入" },
    { value: "email", label: "邮箱", icon: Mail, description: "邮箱地址" },
    { value: "phone", label: "电话", icon: Phone, description: "电话号码" },
    { value: "url", label: "网址", icon: Link, description: "URL链接" },
    { value: "file", label: "文件上传", icon: Upload, description: "文件上传" }
  ]

  const addField = (fieldType: string) => {
    const newField = {
      id: `field_${Date.now()}`,
      type: fieldType,
      label: `新${fieldTypes.find(t => t.value === fieldType)?.label}`,
      placeholder: "",
      required: false,
      readonly: false,
      helpText: "",
      order: fields.length + 1,
      ...getDefaultConfig(fieldType)
    }
    
    onChange([...fields, newField])
    setSelectedField(newField)
  }

  const removeField = (fieldId: string) => {
    onChange(fields.filter(field => field.id !== fieldId))
    if (selectedField?.id === fieldId) {
      setSelectedField(null)
    }
  }

  const updateField = (fieldId: string, updates: any) => {
    onChange(fields.map(field => 
      field.id === fieldId 
        ? { ...field, ...updates }
        : field
    ))
    
    if (selectedField?.id === fieldId) {
      setSelectedField({ ...selectedField, ...updates })
    }
  }

  const getDefaultConfig = (fieldType: string) => {
    const configs = {
      select: { options: ["选项1", "选项2", "选项3"] },
      multiselect: { options: ["选项1", "选项2", "选项3"] },
      radio: { options: ["选项1", "选项2", "选项3"] },
      checkbox: { options: ["选项1", "选项2", "选项3"] },
      file: { fileTypes: ["*"], maxSize: "10MB", multiple: false },
      number: { min: "", max: "", step: "1" }
    }
    return configs[fieldType] || {}
  }

  const getFieldIcon = (fieldType: string) => {
    return fieldTypes.find(t => t.value === fieldType)?.icon || Type
  }

  const renderFieldPreview = (field: any) => {
    const IconComponent = getFieldIcon(field.type)
    
    return (
      <div className="p-3 border rounded-lg bg-white dark:bg-gray-900 hover:border-blue-300 cursor-pointer">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
            <IconComponent className="h-4 w-4 text-blue-500" />
            <div>
              <p className="font-medium text-sm">{field.label}</p>
              <p className="text-xs text-muted-foreground">
                {fieldTypes.find(t => t.value === field.type)?.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedField(field)}
            >
              <Eye className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeField(field.id)}
              className="text-red-600 hover:text-red-700"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const renderFieldConfig = () => {
    if (!selectedField) return null

    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">字段配置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 基本配置 */}
          <div className="space-y-3">
            <div>
              <Label className="text-sm font-medium">字段标签</Label>
              <Input
                value={selectedField.label}
                onChange={(e) => updateField(selectedField.id, { label: e.target.value })}
                placeholder="字段标签"
              />
            </div>
            
            <div>
              <Label className="text-sm font-medium">占位符文本</Label>
              <Input
                value={selectedField.placeholder || ""}
                onChange={(e) => updateField(selectedField.id, { placeholder: e.target.value })}
                placeholder="占位符文本"
              />
            </div>
            
            <div>
              <Label className="text-sm font-medium">帮助文本</Label>
              <Textarea
                value={selectedField.helpText || ""}
                onChange={(e) => updateField(selectedField.id, { helpText: e.target.value })}
                placeholder="帮助文本"
                rows={2}
              />
            </div>
          </div>

          {/* 字段属性 */}
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Switch
                checked={selectedField.required}
                onCheckedChange={(checked) => updateField(selectedField.id, { required: checked })}
              />
              <Label className="text-sm">必填字段</Label>
            </div>
            
            <div className="flex items-center space-x-3">
              <Switch
                checked={selectedField.readonly}
                onCheckedChange={(checked) => updateField(selectedField.id, { readonly: checked })}
              />
              <Label className="text-sm">只读字段</Label>
            </div>
          </div>

          {/* 特殊配置 */}
          {["select", "multiselect", "radio", "checkbox"].includes(selectedField.type) && (
            <div>
              <Label className="text-sm font-medium">选项配置</Label>
              <div className="space-y-2">
                {selectedField.options?.map((option: string, index: number) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Input
                      value={option}
                      onChange={(e) => {
                        const newOptions = [...selectedField.options]
                        newOptions[index] = e.target.value
                        updateField(selectedField.id, { options: newOptions })
                      }}
                      placeholder={`选项 ${index + 1}`}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const newOptions = selectedField.options.filter((_: any, i: number) => i !== index)
                        updateField(selectedField.id, { options: newOptions })
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newOptions = [...(selectedField.options || []), `选项${(selectedField.options?.length || 0) + 1}`]
                    updateField(selectedField.id, { options: newOptions })
                  }}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  添加选项
                </Button>
              </div>
            </div>
          )}

          {selectedField.type === "file" && (
            <div className="space-y-3">
              <div>
                <Label className="text-sm font-medium">允许的文件类型</Label>
                <Input
                  value={selectedField.fileTypes?.join(", ") || "*"}
                  onChange={(e) => updateField(selectedField.id, { 
                    fileTypes: e.target.value.split(",").map(s => s.trim()) 
                  })}
                  placeholder="例如: image/*, .pdf, .doc"
                />
              </div>
              <div>
                <Label className="text-sm font-medium">最大文件大小</Label>
                <Input
                  value={selectedField.maxSize || "10MB"}
                  onChange={(e) => updateField(selectedField.id, { maxSize: e.target.value })}
                  placeholder="例如: 10MB"
                />
              </div>
              <div className="flex items-center space-x-3">
                <Switch
                  checked={selectedField.multiple}
                  onCheckedChange={(checked) => updateField(selectedField.id, { multiple: checked })}
                />
                <Label className="text-sm">允许多文件上传</Label>
              </div>
            </div>
          )}

          {selectedField.type === "number" && (
            <div className="grid grid-cols-3 gap-3">
              <div>
                <Label className="text-sm font-medium">最小值</Label>
                <Input
                  type="number"
                  value={selectedField.min || ""}
                  onChange={(e) => updateField(selectedField.id, { min: e.target.value })}
                  placeholder="最小值"
                />
              </div>
              <div>
                <Label className="text-sm font-medium">最大值</Label>
                <Input
                  type="number"
                  value={selectedField.max || ""}
                  onChange={(e) => updateField(selectedField.id, { max: e.target.value })}
                  placeholder="最大值"
                />
              </div>
              <div>
                <Label className="text-sm font-medium">步长</Label>
                <Input
                  value={selectedField.step || "1"}
                  onChange={(e) => updateField(selectedField.id, { step: e.target.value })}
                  placeholder="步长"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* 左侧：字段类型选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">字段类型</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {fieldTypes.map((fieldType) => {
              const IconComponent = fieldType.icon
              return (
                <Button
                  key={fieldType.value}
                  variant="outline"
                  onClick={() => addField(fieldType.value)}
                  className="w-full justify-start h-auto p-3"
                >
                  <div className="flex items-center space-x-3">
                    <IconComponent className="h-4 w-4" />
                    <div className="text-left">
                      <div className="font-medium text-sm">{fieldType.label}</div>
                      <div className="text-xs text-muted-foreground">{fieldType.description}</div>
                    </div>
                  </div>
                </Button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 中间：表单预览 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center justify-between">
            <span>表单预览</span>
            <Badge variant="outline">{fields.length} 个字段</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {fields.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm">还没有添加任何字段</p>
              <p className="text-xs">从左侧选择字段类型开始设计表单</p>
            </div>
          ) : (
            <div className="space-y-3">
              {fields
                .sort((a, b) => a.order - b.order)
                .map((field) => (
                  <div
                    key={field.id}
                    onClick={() => setSelectedField(field)}
                    className={selectedField?.id === field.id ? "ring-2 ring-blue-500 rounded-lg" : ""}
                  >
                    {renderFieldPreview(field)}
                  </div>
                ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 右侧：字段配置 */}
      {selectedField ? (
        renderFieldConfig()
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">字段配置</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-muted-foreground">
              <Eye className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm">选择一个字段进行配置</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
