"use client"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  Settings, 
  User, 
  Users, 
  Clock, 
  Zap, 
  AlertTriangle,
  Plus,
  X
} from "lucide-react"

interface TemplateProcessConfigProps {
  data: {
    defaultAssignee: string
    defaultCollaborators: string[]
    slaPolicy: string
    automationRules: string[]
    defaultPriority: string
  }
  onChange: (data: any) => void
}

/**
 * 模板流程配置组件
 * 用于配置模板的默认处理流程、SLA策略和自动化规则
 */
export function TemplateProcessConfig({ data, onChange }: TemplateProcessConfigProps) {
  
  // 处理人选项
  const assigneeOptions = [
    "IT支持组",
    "网络管理组", 
    "系统管理员",
    "客服组",
    "采购部",
    "行政部",
    "人事部",
    "财务部"
  ]

  // 协办人选项
  const collaboratorOptions = [
    "质量部经理",
    "技术总监",
    "客服经理",
    "IT经理",
    "采购经理",
    "行政经理"
  ]

  // SLA策略选项
  const slaPolicyOptions = [
    "VIP客户-紧急问题SLA",
    "标准IT服务SLA", 
    "客户投诉处理SLA",
    "采购申请审批SLA",
    "设备维护SLA"
  ]

  // 自动化规则选项
  const automationRuleOptions = [
    "高优先级投诉自动通知总监",
    "VIP客户工单自动分派",
    "SLA超时自动升级",
    "工单完成自动回访"
  ]

  // 优先级选项
  const priorityOptions = [
    { value: "urgent", label: "紧急", color: "bg-red-500" },
    { value: "high", label: "高", color: "bg-orange-500" },
    { value: "medium", label: "中", color: "bg-yellow-500" },
    { value: "low", label: "低", color: "bg-green-500" }
  ]

  const updateField = (field: string, value: any) => {
    onChange({
      ...data,
      [field]: value
    })
  }

  const addCollaborator = (collaborator: string) => {
    if (!data.defaultCollaborators.includes(collaborator)) {
      updateField("defaultCollaborators", [...data.defaultCollaborators, collaborator])
    }
  }

  const removeCollaborator = (collaborator: string) => {
    updateField("defaultCollaborators", data.defaultCollaborators.filter(c => c !== collaborator))
  }

  const addAutomationRule = (rule: string) => {
    if (!data.automationRules.includes(rule)) {
      updateField("automationRules", [...data.automationRules, rule])
    }
  }

  const removeAutomationRule = (rule: string) => {
    updateField("automationRules", data.automationRules.filter(r => r !== rule))
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 左侧：处理人员配置 */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>处理人员</span>
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* 默认处理人 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">默认处理人</Label>
              <Select value={data.defaultAssignee} onValueChange={(value) => updateField("defaultAssignee", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择默认处理人" />
                </SelectTrigger>
                <SelectContent>
                  {assigneeOptions.map((assignee) => (
                    <SelectItem key={assignee} value={assignee}>
                      {assignee}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                使用此模板创建的工单将自动分配给指定的处理人
              </p>
            </div>

            {/* 默认协办人 */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">默认协办人</Label>
              
              {/* 已选择的协办人 */}
              {data.defaultCollaborators.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {data.defaultCollaborators.map((collaborator) => (
                    <Badge key={collaborator} variant="secondary" className="flex items-center space-x-1">
                      <span>{collaborator}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCollaborator(collaborator)}
                        className="h-auto p-0 ml-1"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
              
              {/* 添加协办人 */}
              <Select onValueChange={addCollaborator}>
                <SelectTrigger>
                  <SelectValue placeholder="添加协办人" />
                </SelectTrigger>
                <SelectContent>
                  {collaboratorOptions
                    .filter(option => !data.defaultCollaborators.includes(option))
                    .map((collaborator) => (
                      <SelectItem key={collaborator} value={collaborator}>
                        {collaborator}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                协办人将自动收到工单通知，可以参与处理过程
              </p>
            </div>

            {/* 默认优先级 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">默认优先级</Label>
              <Select value={data.defaultPriority} onValueChange={(value) => updateField("defaultPriority", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择默认优先级" />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map((priority) => (
                    <SelectItem key={priority.value} value={priority.value}>
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${priority.color}`}></div>
                        <span>{priority.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                使用此模板创建的工单将设置为指定的默认优先级
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 右侧：流程配置 */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>流程配置</span>
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* SLA策略 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">SLA策略</Label>
              <Select value={data.slaPolicy} onValueChange={(value) => updateField("slaPolicy", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择SLA策略" />
                </SelectTrigger>
                <SelectContent>
                  {slaPolicyOptions.map((policy) => (
                    <SelectItem key={policy} value={policy}>
                      {policy}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                选择适用的SLA策略，系统将自动应用相应的时效要求
              </p>
            </div>

            {/* 自动化规则 */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">自动化规则</Label>
              
              {/* 已选择的规则 */}
              {data.automationRules.length > 0 && (
                <div className="space-y-2">
                  {data.automationRules.map((rule) => (
                    <div key={rule} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center space-x-2">
                        <Zap className="h-4 w-4 text-blue-500" />
                        <span className="text-sm">{rule}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAutomationRule(rule)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
              
              {/* 添加规则 */}
              <Select onValueChange={addAutomationRule}>
                <SelectTrigger>
                  <SelectValue placeholder="添加自动化规则" />
                </SelectTrigger>
                <SelectContent>
                  {automationRuleOptions
                    .filter(option => !data.automationRules.includes(option))
                    .map((rule) => (
                      <SelectItem key={rule} value={rule}>
                        {rule}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                选择的自动化规则将在工单创建时自动触发执行
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 配置预览 */}
      <div className="lg:col-span-2">
        <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
          <CardHeader>
            <CardTitle className="text-lg text-green-800 dark:text-green-200">
              流程配置预览
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">处理人员配置</h4>
                <div className="space-y-1 text-green-700 dark:text-green-300">
                  <div>默认处理人: {data.defaultAssignee || "未设置"}</div>
                  <div>协办人数量: {data.defaultCollaborators.length} 人</div>
                  <div>默认优先级: {
                    priorityOptions.find(p => p.value === data.defaultPriority)?.label || "未设置"
                  }</div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">流程配置</h4>
                <div className="space-y-1 text-green-700 dark:text-green-300">
                  <div>SLA策略: {data.slaPolicy || "未设置"}</div>
                  <div>自动化规则: {data.automationRules.length} 个</div>
                </div>
              </div>
            </div>
            
            {(data.defaultAssignee || data.slaPolicy || data.automationRules.length > 0) && (
              <div className="mt-4 p-3 bg-white dark:bg-gray-900 border border-green-200 dark:border-green-800 rounded">
                <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">
                  工单创建时将自动执行：
                </h4>
                <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                  {data.defaultAssignee && (
                    <li>• 分配给：{data.defaultAssignee}</li>
                  )}
                  {data.defaultCollaborators.length > 0 && (
                    <li>• 抄送给：{data.defaultCollaborators.join("、")}</li>
                  )}
                  {data.defaultPriority && (
                    <li>• 设置优先级为：{priorityOptions.find(p => p.value === data.defaultPriority)?.label}</li>
                  )}
                  {data.slaPolicy && (
                    <li>• 应用SLA策略：{data.slaPolicy}</li>
                  )}
                  {data.automationRules.map((rule) => (
                    <li key={rule}>• 触发自动化规则：{rule}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
