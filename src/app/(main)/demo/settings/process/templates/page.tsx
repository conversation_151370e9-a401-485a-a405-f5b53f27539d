"use client"

import { useState } from "react"
import { TemplateListView } from "./_components/template-list-view"
import { TemplateBuilderView } from "./_components/template-builder-view"

/**
 * 工单模板管理页面
 * 支持模板列表视图和模板构建器视图两种模式
 */
export default function TemplateManagementPage() {
  const [currentView, setCurrentView] = useState<"list" | "builder">("list")
  const [editingTemplate, setEditingTemplate] = useState<any>(null)

  const handleCreateTemplate = () => {
    setEditingTemplate(null)
    setCurrentView("builder")
  }

  const handleEditTemplate = (template: any) => {
    setEditingTemplate(template)
    setCurrentView("builder")
  }

  const handleBackToList = () => {
    setCurrentView("list")
    setEditingTemplate(null)
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {currentView === "list" ? (
        <TemplateListView 
          onCreateTemplate={handleCreateTemplate}
          onEditTemplate={handleEditTemplate}
        />
      ) : (
        <TemplateBuilderView 
          editingTemplate={editingTemplate}
          onBackToList={handleBackToList}
        />
      )}
    </div>
  )
}
