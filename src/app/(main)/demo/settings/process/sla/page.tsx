"use client"

import { useState } from "react"
import { SLAPolicyListView } from "./_components/sla-policy-list-view"
import { SLAPolicyBuilderView } from "./_components/sla-policy-builder-view"

/**
 * SLA策略设置页面
 * 支持SLA策略列表视图和策略构建器视图两种模式
 */
export default function SLAPolicyPage() {
  const [currentView, setCurrentView] = useState<"list" | "builder">("list")
  const [editingPolicy, setEditingPolicy] = useState<any>(null)

  const handleCreatePolicy = () => {
    setEditingPolicy(null)
    setCurrentView("builder")
  }

  const handleEditPolicy = (policy: any) => {
    setEditingPolicy(policy)
    setCurrentView("builder")
  }

  const handleBackToList = () => {
    setCurrentView("list")
    setEditingPolicy(null)
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {currentView === "list" ? (
        <SLAPolicyListView 
          onCreatePolicy={handleCreatePolicy}
          onEditPolicy={handleEditPolicy}
        />
      ) : (
        <SLAPolicyBuilderView 
          editingPolicy={editingPolicy}
          onBackToList={handleBackToList}
        />
      )}
    </div>
  )
}
