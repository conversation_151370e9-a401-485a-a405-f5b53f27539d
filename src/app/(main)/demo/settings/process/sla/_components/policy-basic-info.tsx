"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Info } from "lucide-react"

interface PolicyBasicInfoProps {
  data: {
    name: string
    description: string
    isEnabled: boolean
    priority: number
  }
  onChange: (data: any) => void
  onNameChange: (name: string) => void
  onDescriptionChange: (description: string) => void
  onEnabledChange: (enabled: boolean) => void
  onPriorityChange: (priority: number) => void
}

/**
 * 策略基本信息组件
 * 用于设置SLA策略的名称、描述、状态和优先级
 */
export function PolicyBasicInfo({ 
  data, 
  onNameChange, 
  onDescriptionChange, 
  onEnabledChange, 
  onPriorityChange 
}: PolicyBasicInfoProps) {

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Info className="h-5 w-5" />
          <span>策略基本信息</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 策略名称 */}
        <div className="space-y-2">
          <Label htmlFor="policy-name" className="text-sm font-medium">
            策略名称 <span className="text-red-500">*</span>
          </Label>
          <Input
            id="policy-name"
            placeholder="例如：VIP客户-紧急问题SLA"
            value={data.name}
            onChange={(e) => onNameChange(e.target.value)}
            className="w-full"
          />
          <p className="text-xs text-muted-foreground">
            为策略起一个清晰易懂的名称，便于识别和管理
          </p>
        </div>

        {/* 策略描述 */}
        <div className="space-y-2">
          <Label htmlFor="policy-description" className="text-sm font-medium">
            策略描述
          </Label>
          <Textarea
            id="policy-description"
            placeholder="描述此策略的适用范围和目标..."
            value={data.description}
            onChange={(e) => onDescriptionChange(e.target.value)}
            rows={3}
            className="w-full"
          />
          <p className="text-xs text-muted-foreground">
            详细说明策略的用途、适用场景和服务承诺
          </p>
        </div>

        {/* 策略状态 */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">策略状态</Label>
          <div className="flex items-center space-x-3">
            <Switch
              checked={data.isEnabled}
              onCheckedChange={onEnabledChange}
            />
            <span className={`text-sm ${data.isEnabled ? 'text-green-600' : 'text-gray-500'}`}>
              {data.isEnabled ? '已启用' : '已停用'}
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            只有启用的策略才会应用到新创建的工单
          </p>
        </div>

        {/* 应用优先级 */}
        <div className="space-y-2">
          <Label htmlFor="policy-priority" className="text-sm font-medium">
            应用优先级
          </Label>
          <div className="flex items-center space-x-3">
            <Input
              id="policy-priority"
              type="number"
              min="1"
              max="100"
              value={data.priority}
              onChange={(e) => onPriorityChange(parseInt(e.target.value) || 1)}
              className="w-24"
            />
            <span className="text-sm text-muted-foreground">
              数字越小，优先级越高
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            当工单同时满足多个策略条件时，系统会应用优先级最高的策略
          </p>
        </div>

        {/* 优先级说明 */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            优先级应用规则
          </h4>
          <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
            <li>• 优先级 1：最高优先级，通常用于VIP客户或紧急问题</li>
            <li>• 优先级 2-5：高优先级，用于重要业务或特殊客户</li>
            <li>• 优先级 6-10：标准优先级，用于常规业务流程</li>
            <li>• 优先级 11+：低优先级，用于非关键业务或内部流程</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
