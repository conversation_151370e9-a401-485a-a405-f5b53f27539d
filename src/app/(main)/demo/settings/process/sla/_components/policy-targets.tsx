"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Plus, X, Target, ChevronDown, ChevronRight, Clock, AlertTriangle } from "lucide-react"

interface PolicyTargetsProps {
  data: Array<{
    id: string
    name: string
    type: string
    timeTargets: Array<{
      priority: string
      value: number
      unit: string
    }>
  }>
  onChange: (data: any) => void
}

/**
 * SLA目标设置组件
 * 用于设置不同优先级的响应时间和解决时间目标
 */
export function PolicyTargets({ data, onChange }: PolicyTargetsProps) {
  const [expandedTargets, setExpandedTargets] = useState<string[]>(["first_response"])

  // 目标类型
  const targetTypes = [
    { value: "first_response", label: "首次响应时间", description: "从工单创建到首次响应的时间" },
    { value: "resolution", label: "解决时间", description: "从工单创建到问题解决的时间" },
    { value: "next_reply", label: "更新时间", description: "处理人员之间沟通的最大间隔时间" }
  ]

  // 时间单位
  const timeUnits = [
    { value: "minutes", label: "分钟" },
    { value: "hours", label: "小时" },
    { value: "business_days", label: "个工作日" },
    { value: "calendar_days", label: "个自然日" }
  ]

  // 优先级
  const priorities = [
    { value: "urgent", label: "紧急", color: "bg-red-500" },
    { value: "high", label: "高", color: "bg-orange-500" },
    { value: "medium", label: "中", color: "bg-yellow-500" },
    { value: "low", label: "低", color: "bg-green-500" }
  ]

  const addTarget = () => {
    const newTarget = {
      id: `target_${Date.now()}`,
      name: "新SLA目标",
      type: "first_response",
      timeTargets: priorities.map(priority => ({
        priority: priority.value,
        value: 1,
        unit: "hours"
      }))
    }
    onChange([...data, newTarget])
    setExpandedTargets(prev => [...prev, newTarget.id])
  }

  const removeTarget = (targetId: string) => {
    onChange(data.filter(target => target.id !== targetId))
    setExpandedTargets(prev => prev.filter(id => id !== targetId))
  }

  const updateTarget = (targetId: string, field: string, value: any) => {
    onChange(data.map(target => 
      target.id === targetId 
        ? { ...target, [field]: value }
        : target
    ))
  }

  const updateTimeTarget = (targetId: string, priority: string, field: string, value: any) => {
    onChange(data.map(target => 
      target.id === targetId 
        ? {
            ...target,
            timeTargets: target.timeTargets.map(timeTarget =>
              timeTarget.priority === priority
                ? { ...timeTarget, [field]: value }
                : timeTarget
            )
          }
        : target
    ))
  }

  const toggleExpanded = (targetId: string) => {
    setExpandedTargets(prev => 
      prev.includes(targetId)
        ? prev.filter(id => id !== targetId)
        : [...prev, targetId]
    )
  }

  const getTargetTypeLabel = (type: string) => {
    return targetTypes.find(t => t.value === type)?.label || type
  }

  const getTimeUnitLabel = (unit: string) => {
    return timeUnits.find(u => u.value === unit)?.label || unit
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Target className="h-5 w-5" />
          <span>SLA目标</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 添加目标按钮 */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium">时间目标设定</h3>
            <p className="text-xs text-muted-foreground mt-1">
              为不同优先级设置具体的服务承诺时间
            </p>
          </div>
          <Button onClick={addTarget} size="sm" variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            添加目标
          </Button>
        </div>

        {/* 目标列表 */}
        {data.length === 0 ? (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
            <Clock className="h-8 w-8 mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-muted-foreground mb-4">
              还没有设置任何SLA目标
            </p>
            <Button onClick={addTarget} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              添加第一个目标
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {data.map((target, index) => {
              const isExpanded = expandedTargets.includes(target.id)
              
              return (
                <Collapsible key={target.id} open={isExpanded} onOpenChange={() => toggleExpanded(target.id)}>
                  <div className="border rounded-lg">
                    <CollapsibleTrigger className="w-full p-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800">
                      <div className="flex items-center space-x-3">
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                        <Badge variant="secondary" className="text-xs">
                          目标 {index + 1}
                        </Badge>
                        <span className="font-medium">{target.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {getTargetTypeLabel(target.type)}
                        </Badge>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          removeTarget(target.id)
                        }}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </CollapsibleTrigger>
                    
                    <CollapsibleContent>
                      <div className="p-4 border-t bg-gray-50 dark:bg-gray-800 space-y-4">
                        {/* 目标基本信息 */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <label className="text-sm font-medium">目标名称</label>
                            <Input
                              value={target.name}
                              onChange={(e) => updateTarget(target.id, "name", e.target.value)}
                              placeholder="输入目标名称"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <label className="text-sm font-medium">目标类型</label>
                            <Select
                              value={target.type}
                              onValueChange={(value) => updateTarget(target.id, "type", value)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {targetTypes.map((type) => (
                                  <SelectItem key={type.value} value={type.value}>
                                    <div>
                                      <div className="font-medium">{type.label}</div>
                                      <div className="text-xs text-muted-foreground">{type.description}</div>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        {/* 优先级时间设置 */}
                        <div className="space-y-3">
                          <h4 className="text-sm font-medium">基于优先级的时间目标</h4>
                          <div className="space-y-3">
                            {priorities.map((priority) => {
                              const timeTarget = target.timeTargets.find(t => t.priority === priority.value)
                              
                              return (
                                <div key={priority.value} className="flex items-center space-x-3 p-3 border rounded bg-white dark:bg-gray-900">
                                  <div className={`w-3 h-3 rounded-full ${priority.color}`}></div>
                                  <span className="text-sm font-medium w-16">{priority.label}</span>
                                  
                                  <Input
                                    type="number"
                                    min="1"
                                    value={timeTarget?.value || 1}
                                    onChange={(e) => updateTimeTarget(target.id, priority.value, "value", parseInt(e.target.value) || 1)}
                                    className="w-20"
                                  />
                                  
                                  <Select
                                    value={timeTarget?.unit || "hours"}
                                    onValueChange={(value) => updateTimeTarget(target.id, priority.value, "unit", value)}
                                  >
                                    <SelectTrigger className="w-32">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {timeUnits.map((unit) => (
                                        <SelectItem key={unit.value} value={unit.value}>
                                          {unit.label}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              )
                            })}
                          </div>
                        </div>
                      </div>
                    </CollapsibleContent>
                  </div>
                </Collapsible>
              )
            })}
          </div>
        )}

        {/* SLA计时器控制说明 */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                SLA计时器控制规则
              </h4>
              <ul className="text-xs text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• 计时开始于：工单状态为"待处理"时</li>
                <li>• 计时暂停于：工单状态为"已挂起"或"等待客户回复"时</li>
                <li>• 计时结束于：工单状态为"已办结"时</li>
                <li>• 工作日计算会排除非工作时间和节假日</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 目标预览 */}
        {data.length > 0 && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-3">
              SLA目标预览
            </h4>
            <div className="space-y-3">
              {data.map((target, index) => (
                <div key={target.id} className="text-sm">
                  <div className="font-medium text-green-800 dark:text-green-200 mb-1">
                    {index + 1}. {target.name} ({getTargetTypeLabel(target.type)})
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-green-700 dark:text-green-300">
                    {target.timeTargets.map((timeTarget) => {
                      const priority = priorities.find(p => p.value === timeTarget.priority)
                      return (
                        <div key={timeTarget.priority} className="flex items-center space-x-1">
                          <div className={`w-2 h-2 rounded-full ${priority?.color}`}></div>
                          <span>{priority?.label}: {timeTarget.value} {getTimeUnitLabel(timeTarget.unit)}</span>
                        </div>
                      )
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
