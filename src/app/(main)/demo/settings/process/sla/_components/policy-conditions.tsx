"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Plus, X, Filter, AlertCircle } from "lucide-react"

interface PolicyConditionsProps {
  data: {
    logic: "AND" | "OR"
    rules: Array<{
      field: string
      operator: string
      value: string
    }>
  }
  onChange: (data: any) => void
}

/**
 * 策略应用条件组件
 * 用于设置SLA策略的应用条件，支持多条件组合
 */
export function PolicyConditions({ data, onChange }: PolicyConditionsProps) {
  
  // 可用字段
  const fields = [
    { value: "ticket_type", label: "工单类型" },
    { value: "priority", label: "优先级" },
    { value: "customer_level", label: "客户级别" },
    { value: "department", label: "处理部门" },
    { value: "category", label: "工单分类" },
    { value: "source", label: "工单来源" },
    { value: "assignee", label: "处理人" },
    { value: "customer_company", label: "客户公司" }
  ]

  // 操作符
  const operators = [
    { value: "equals", label: "等于" },
    { value: "not_equals", label: "不等于" },
    { value: "contains", label: "包含" },
    { value: "not_contains", label: "不包含" },
    { value: "in", label: "属于...之一" },
    { value: "not_in", label: "不属于...之一" },
    { value: "starts_with", label: "开始于" },
    { value: "ends_with", label: "结束于" }
  ]

  // 根据字段获取可选值
  const getFieldValues = (field: string) => {
    const values = {
      ticket_type: ["IT服务", "客户投诉", "采购申请", "售后维修", "系统故障"],
      priority: ["紧急", "高", "中", "低"],
      customer_level: ["VIP", "企业", "标准", "试用"],
      department: ["IT支持部", "网络部", "安全部", "开发部"],
      category: ["硬件问题", "软件问题", "网络问题", "权限申请", "其他"],
      source: ["电话", "邮件", "在线提交", "微信", "现场"],
      customer_company: ["阿里巴巴", "腾讯", "百度", "字节跳动", "美团"]
    }
    return values[field] || []
  }

  const addCondition = () => {
    const newRule = {
      field: "",
      operator: "equals",
      value: ""
    }
    onChange({
      ...data,
      rules: [...data.rules, newRule]
    })
  }

  const removeCondition = (index: number) => {
    onChange({
      ...data,
      rules: data.rules.filter((_, i) => i !== index)
    })
  }

  const updateCondition = (index: number, field: string, value: any) => {
    const newRules = [...data.rules]
    newRules[index] = {
      ...newRules[index],
      [field]: value
    }
    onChange({
      ...data,
      rules: newRules
    })
  }

  const updateLogic = (logic: "AND" | "OR") => {
    onChange({
      ...data,
      logic
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Filter className="h-5 w-5" />
          <span>应用条件</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 条件逻辑选择 */}
        <div className="space-y-2">
          <label className="text-sm font-medium">条件逻辑</label>
          <div className="flex items-center space-x-4">
            <Button
              variant={data.logic === "AND" ? "default" : "outline"}
              size="sm"
              onClick={() => updateLogic("AND")}
            >
              满足所有条件 (AND)
            </Button>
            <Button
              variant={data.logic === "OR" ? "default" : "outline"}
              size="sm"
              onClick={() => updateLogic("OR")}
            >
              满足任意条件 (OR)
            </Button>
          </div>
          <p className="text-xs text-muted-foreground">
            选择多个条件之间的逻辑关系
          </p>
        </div>

        {/* 条件列表 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">
              条件规则 <span className="text-red-500">*</span>
            </label>
            <Button onClick={addCondition} size="sm" variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              添加条件
            </Button>
          </div>

          {data.rules.length === 0 ? (
            <div className="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
              <AlertCircle className="h-8 w-8 mx-auto text-gray-400 mb-2" />
              <p className="text-sm text-muted-foreground mb-4">
                还没有添加任何条件
              </p>
              <Button onClick={addCondition} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                添加第一个条件
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {data.rules.map((rule, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg bg-gray-50 dark:bg-gray-800">
                  {/* 条件序号 */}
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="text-xs">
                      {index + 1}
                    </Badge>
                    {index > 0 && (
                      <span className="text-xs font-medium text-blue-600">
                        {data.logic}
                      </span>
                    )}
                  </div>

                  {/* 字段选择 */}
                  <Select
                    value={rule.field}
                    onValueChange={(value) => updateCondition(index, "field", value)}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="选择字段" />
                    </SelectTrigger>
                    <SelectContent>
                      {fields.map((field) => (
                        <SelectItem key={field.value} value={field.value}>
                          {field.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {/* 操作符选择 */}
                  <Select
                    value={rule.operator}
                    onValueChange={(value) => updateCondition(index, "operator", value)}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="操作符" />
                    </SelectTrigger>
                    <SelectContent>
                      {operators.map((op) => (
                        <SelectItem key={op.value} value={op.value}>
                          {op.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {/* 值输入 */}
                  {rule.field && getFieldValues(rule.field).length > 0 ? (
                    <Select
                      value={rule.value}
                      onValueChange={(value) => updateCondition(index, "value", value)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="选择值" />
                      </SelectTrigger>
                      <SelectContent>
                        {getFieldValues(rule.field).map((value) => (
                          <SelectItem key={value} value={value}>
                            {value}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <Input
                      placeholder="输入值"
                      value={rule.value}
                      onChange={(e) => updateCondition(index, "value", e.target.value)}
                      className="w-32"
                    />
                  )}

                  {/* 删除按钮 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeCondition(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 条件预览 */}
        {data.rules.length > 0 && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              条件预览
            </h4>
            <div className="text-sm text-blue-700 dark:text-blue-300">
              当工单满足以下条件时应用此策略：
              <div className="mt-2 space-y-1">
                {data.rules.map((rule, index) => {
                  const field = fields.find(f => f.value === rule.field)
                  const operator = operators.find(o => o.value === rule.operator)
                  
                  return (
                    <div key={index} className="flex items-center space-x-2">
                      {index > 0 && (
                        <span className="font-medium">{data.logic}</span>
                      )}
                      <span>
                        {field?.label || rule.field} {operator?.label || rule.operator} "{rule.value}"
                      </span>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        )}

        {/* 使用说明 */}
        <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">
            条件设置说明
          </h4>
          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <li>• 至少需要设置一个条件才能保存策略</li>
            <li>• AND逻辑：工单必须同时满足所有条件</li>
            <li>• OR逻辑：工单满足任意一个条件即可</li>
            <li>• 可以设置多个条件来精确控制策略的应用范围</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
