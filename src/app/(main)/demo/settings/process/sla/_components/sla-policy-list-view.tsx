"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Copy, 
  Trash2,
  Clock,
  Shield,
  GripVertical,
  CheckCircle,
  XCircle
} from "lucide-react"

interface SLAPolicyListViewProps {
  onCreatePolicy: () => void
  onEditPolicy: (policy: any) => void
}

/**
 * SLA策略列表视图组件
 * 展示所有已创建的SLA策略，支持搜索、排序、启用/停用等操作
 */
export function SLAPolicyListView({ onCreatePolicy, onEditPolicy }: SLAPolicyListViewProps) {
  const [searchQuery, setSearchQuery] = useState("")

  // 模拟SLA策略数据
  const [policies, setPolicies] = useState([
    {
      id: "1",
      name: "VIP客户-紧急问题SLA",
      description: "针对VIP客户的紧急问题，提供最高级别的服务保障",
      isEnabled: true,
      priority: 1,
      creator: "系统管理员",
      updatedAt: "2024-01-15 14:30:00",
      conditions: ["客户级别 = VIP", "优先级 = 紧急"],
      targets: {
        firstResponse: "15分钟",
        resolution: "4小时"
      }
    },
    {
      id: "2",
      name: "标准IT服务SLA",
      description: "适用于一般IT服务请求的标准服务级别协议",
      isEnabled: true,
      priority: 2,
      creator: "张三",
      updatedAt: "2024-01-12 09:20:00",
      conditions: ["工单类型 = IT服务"],
      targets: {
        firstResponse: "1小时",
        resolution: "1个工作日"
      }
    },
    {
      id: "3",
      name: "客户投诉处理SLA",
      description: "客户投诉类工单的处理时效要求",
      isEnabled: false,
      priority: 3,
      creator: "李四",
      updatedAt: "2024-01-10 16:45:00",
      conditions: ["工单类型 = 客户投诉"],
      targets: {
        firstResponse: "30分钟",
        resolution: "2个工作日"
      }
    },
    {
      id: "4",
      name: "采购申请审批SLA",
      description: "采购申请类工单的审批时效标准",
      isEnabled: true,
      priority: 4,
      creator: "王五",
      updatedAt: "2024-01-08 11:15:00",
      conditions: ["工单类型 = 采购申请"],
      targets: {
        firstResponse: "2小时",
        resolution: "5个工作日"
      }
    }
  ])

  const filteredPolicies = policies.filter(policy =>
    policy.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    policy.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleTogglePolicy = (policyId: string) => {
    setPolicies(prev => prev.map(policy => 
      policy.id === policyId 
        ? { ...policy, isEnabled: !policy.isEnabled }
        : policy
    ))
  }

  const handleCopyPolicy = (policy: any) => {
    console.log("复制策略:", policy.name)
  }

  const handleDeletePolicy = (policy: any) => {
    console.log("删除策略:", policy.name)
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            SLA策略设置
          </h1>
          <p className="text-muted-foreground mt-2">
            定义和管理服务级别协议策略，确保工单处理符合时效要求
          </p>
        </div>
        
        <Button onClick={onCreatePolicy} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          新建SLA策略
        </Button>
      </div>

      {/* 搜索和统计 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="搜索策略名称或描述..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>已启用: {policies.filter(p => p.isEnabled).length}</span>
              </div>
              <div className="flex items-center space-x-2">
                <XCircle className="h-4 w-4 text-gray-400" />
                <span>已停用: {policies.filter(p => !p.isEnabled).length}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-blue-500" />
                <span>总计: {policies.length}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 策略列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>SLA策略列表</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">排序</TableHead>
                <TableHead>策略名称</TableHead>
                <TableHead>应用条件</TableHead>
                <TableHead>SLA目标</TableHead>
                <TableHead>优先级</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建人</TableHead>
                <TableHead>最后修改</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPolicies.map((policy) => (
                <TableRow key={policy.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <TableCell>
                    <div className="flex items-center justify-center">
                      <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {policy.name}
                      </p>
                      <p className="text-sm text-muted-foreground mt-1">
                        {policy.description}
                      </p>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      {policy.conditions.map((condition, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {condition}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1 text-sm">
                      <div className="flex items-center space-x-2">
                        <span className="text-muted-foreground">响应:</span>
                        <span className="font-medium">{policy.targets.firstResponse}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-muted-foreground">解决:</span>
                        <span className="font-medium">{policy.targets.resolution}</span>
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Badge variant="secondary" className="font-mono">
                      {policy.priority}
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={policy.isEnabled}
                        onCheckedChange={() => handleTogglePolicy(policy.id)}
                      />
                      <span className={`text-sm ${
                        policy.isEnabled ? 'text-green-600' : 'text-gray-500'
                      }`}>
                        {policy.isEnabled ? '已启用' : '已停用'}
                      </span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <span className="text-sm">{policy.creator}</span>
                  </TableCell>
                  
                  <TableCell>
                    <span className="text-sm text-muted-foreground">
                      {new Date(policy.updatedAt).toLocaleDateString()}
                    </span>
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEditPolicy(policy)}>
                          <Edit className="h-4 w-4 mr-2" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyPolicy(policy)}>
                          <Copy className="h-4 w-4 mr-2" />
                          复制
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeletePolicy(policy)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredPolicies.length === 0 && (
            <div className="text-center py-12">
              <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                {searchQuery ? "没有找到匹配的SLA策略" : "还没有创建任何SLA策略"}
              </p>
              {!searchQuery && (
                <Button onClick={onCreatePolicy} className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  创建第一个SLA策略
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
        <CardContent className="p-4">
          <div className="flex items-start space-x-2">
            <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                SLA策略应用规则
              </h4>
              <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• 策略按优先级顺序应用，数字越小优先级越高</li>
                <li>• 工单创建时会自动匹配第一个符合条件的启用策略</li>
                <li>• 可以通过拖拽调整策略的优先级顺序</li>
                <li>• 停用的策略不会应用到新工单，但不影响已应用的工单</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
