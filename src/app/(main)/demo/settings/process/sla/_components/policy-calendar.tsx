"use client"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, Globe } from "lucide-react"

interface PolicyCalendarProps {
  data: {
    type: string
    timezone: string
  }
  onChange: (data: any) => void
}

/**
 * 工作日历设置组件
 * 用于设置SLA计时的工作时间和时区
 */
export function PolicyCalendar({ data, onChange }: PolicyCalendarProps) {
  
  // 日历类型
  const calendarTypes = [
    {
      value: "24x7",
      label: "7×24小时",
      description: "全天候服务，包括周末和节假日",
      schedule: "每天 00:00 - 23:59"
    },
    {
      value: "business_hours",
      label: "工作时间",
      description: "仅在工作日的工作时间内计时",
      schedule: "周一至周五 09:00 - 18:00"
    },
    {
      value: "extended_hours",
      label: "延长工作时间",
      description: "工作日延长时间，包含部分周末",
      schedule: "周一至周六 08:00 - 20:00"
    },
    {
      value: "custom",
      label: "自定义日历",
      description: "自定义工作时间和节假日设置",
      schedule: "点击设置自定义规则"
    }
  ]

  // 时区选项
  const timezones = [
    { value: "Asia/Shanghai", label: "中国标准时间 (UTC+8)", city: "北京/上海" },
    { value: "Asia/Hong_Kong", label: "香港时间 (UTC+8)", city: "香港" },
    { value: "Asia/Tokyo", label: "日本标准时间 (UTC+9)", city: "东京" },
    { value: "Asia/Seoul", label: "韩国标准时间 (UTC+9)", city: "首尔" },
    { value: "Asia/Singapore", label: "新加坡时间 (UTC+8)", city: "新加坡" },
    { value: "America/New_York", label: "美国东部时间 (UTC-5)", city: "纽约" },
    { value: "America/Los_Angeles", label: "美国西部时间 (UTC-8)", city: "洛杉矶" },
    { value: "Europe/London", label: "格林威治时间 (UTC+0)", city: "伦敦" },
    { value: "UTC", label: "协调世界时 (UTC+0)", city: "UTC" }
  ]

  const updateCalendarType = (type: string) => {
    onChange({
      ...data,
      type
    })
  }

  const updateTimezone = (timezone: string) => {
    onChange({
      ...data,
      timezone
    })
  }

  const getCalendarTypeInfo = (type: string) => {
    return calendarTypes.find(t => t.value === type)
  }

  const getTimezoneInfo = (timezone: string) => {
    return timezones.find(t => t.value === timezone)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="h-5 w-5" />
          <span>工作日历</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 日历类型选择 */}
        <div className="space-y-3">
          <label className="text-sm font-medium">日历类型</label>
          <Select value={data.type} onValueChange={updateCalendarType}>
            <SelectTrigger>
              <SelectValue placeholder="选择日历类型" />
            </SelectTrigger>
            <SelectContent>
              {calendarTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  <div className="py-1">
                    <div className="font-medium">{type.label}</div>
                    <div className="text-xs text-muted-foreground">{type.description}</div>
                    <div className="text-xs text-blue-600 mt-1">{type.schedule}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            选择SLA计时器的工作时间规则
          </p>
        </div>

        {/* 时区设置 */}
        <div className="space-y-3">
          <label className="text-sm font-medium">时区设置</label>
          <Select value={data.timezone} onValueChange={updateTimezone}>
            <SelectTrigger>
              <SelectValue placeholder="选择时区" />
            </SelectTrigger>
            <SelectContent>
              {timezones.map((timezone) => (
                <SelectItem key={timezone.value} value={timezone.value}>
                  <div className="py-1">
                    <div className="font-medium">{timezone.label}</div>
                    <div className="text-xs text-muted-foreground">{timezone.city}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            设置SLA计时所依据的时区
          </p>
        </div>

        {/* 当前设置预览 */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-3">
            当前日历设置
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Clock className="h-4 w-4 text-blue-600" />
              <div>
                <div className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  {getCalendarTypeInfo(data.type)?.label}
                </div>
                <div className="text-xs text-blue-700 dark:text-blue-300">
                  {getCalendarTypeInfo(data.type)?.schedule}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Globe className="h-4 w-4 text-blue-600" />
              <div>
                <div className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  {getTimezoneInfo(data.timezone)?.label}
                </div>
                <div className="text-xs text-blue-700 dark:text-blue-300">
                  {getTimezoneInfo(data.timezone)?.city}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 自定义日历提示 */}
        {data.type === "custom" && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
              自定义日历设置
            </h4>
            <p className="text-xs text-yellow-700 dark:text-yellow-300 mb-3">
              您选择了自定义日历，可以设置具体的工作日、工作时间和节假日。
            </p>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-yellow-700 dark:text-yellow-300">工作日设置</span>
                <Badge variant="outline" className="text-yellow-700 border-yellow-300">
                  周一至周五
                </Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-yellow-700 dark:text-yellow-300">工作时间</span>
                <Badge variant="outline" className="text-yellow-700 border-yellow-300">
                  09:00 - 18:00
                </Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-yellow-700 dark:text-yellow-300">节假日</span>
                <Badge variant="outline" className="text-yellow-700 border-yellow-300">
                  中国法定节假日
                </Badge>
              </div>
            </div>
          </div>
        )}

        {/* 计时规则说明 */}
        <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">
            计时规则说明
          </h4>
          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <li>• <strong>7×24小时</strong>：SLA计时器连续运行，不受工作时间限制</li>
            <li>• <strong>工作时间</strong>：仅在工作日的工作时间内计时，自动排除周末和节假日</li>
            <li>• <strong>延长工作时间</strong>：包含周六的延长工作时间，适用于需要周末支持的业务</li>
            <li>• <strong>自定义日历</strong>：完全自定义的工作时间规则，可精确控制计时逻辑</li>
          </ul>
        </div>

        {/* 时区影响说明 */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">
            时区设置影响
          </h4>
          <ul className="text-xs text-green-700 dark:text-green-300 space-y-1">
            <li>• 所有SLA时间计算都基于所选时区进行</li>
            <li>• 工作时间的开始和结束时间按照所选时区执行</li>
            <li>• 建议选择与主要用户群体相同的时区</li>
            <li>• 时区变更会影响正在进行的SLA计时</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
