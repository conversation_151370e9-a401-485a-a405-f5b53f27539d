"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, Save, X } from "lucide-react"
import { PolicyBasicInfo } from "./policy-basic-info"
import { PolicyConditions } from "./policy-conditions"
import { PolicyTargets } from "./policy-targets"
import { PolicyCalendar } from "./policy-calendar"

interface SLAPolicyBuilderViewProps {
  editingPolicy: any
  onBackToList: () => void
}

/**
 * SLA策略构建器视图组件
 * 提供创建和编辑SLA策略的完整界面
 */
export function SLAPolicyBuilderView({ editingPolicy, onBackToList }: SLAPolicyBuilderViewProps) {
  const [policyData, setPolicyData] = useState({
    name: "",
    description: "",
    isEnabled: true,
    priority: 1,
    conditions: {
      logic: "AND" as "AND" | "OR",
      rules: []
    },
    targets: [],
    calendar: {
      type: "24x7",
      timezone: "Asia/Shanghai"
    }
  })

  // 根据编辑的策略加载数据
  useEffect(() => {
    if (editingPolicy) {
      setPolicyData({
        name: editingPolicy.name,
        description: editingPolicy.description,
        isEnabled: editingPolicy.isEnabled,
        priority: editingPolicy.priority,
        conditions: {
          logic: "AND",
          rules: [
            { field: "customer_level", operator: "equals", value: "VIP" },
            { field: "priority", operator: "equals", value: "urgent" }
          ]
        },
        targets: [
          {
            id: "first_response",
            name: "首次响应时间",
            type: "first_response",
            timeTargets: [
              { priority: "urgent", value: 15, unit: "minutes" },
              { priority: "high", value: 30, unit: "minutes" },
              { priority: "medium", value: 1, unit: "hours" },
              { priority: "low", value: 4, unit: "hours" }
            ]
          },
          {
            id: "resolution",
            name: "解决时间",
            type: "resolution",
            timeTargets: [
              { priority: "urgent", value: 4, unit: "hours" },
              { priority: "high", value: 8, unit: "hours" },
              { priority: "medium", value: 3, unit: "business_days" },
              { priority: "low", value: 7, unit: "business_days" }
            ]
          }
        ],
        calendar: {
          type: "business_hours",
          timezone: "Asia/Shanghai"
        }
      })
    } else {
      // 新建策略时重置数据
      setPolicyData({
        name: "",
        description: "",
        isEnabled: true,
        priority: 1,
        conditions: {
          logic: "AND",
          rules: []
        },
        targets: [],
        calendar: {
          type: "24x7",
          timezone: "Asia/Shanghai"
        }
      })
    }
  }, [editingPolicy])

  const handleSave = () => {
    console.log("保存SLA策略:", policyData)
    // 这里可以添加保存逻辑
    onBackToList()
  }

  const handleCancel = () => {
    onBackToList()
  }

  const updatePolicyData = (section: string, data: any) => {
    setPolicyData(prev => ({
      ...prev,
      [section]: data
    }))
  }

  const isFormValid = () => {
    return policyData.name.trim() !== "" && 
           policyData.conditions.rules.length > 0 && 
           policyData.targets.length > 0
  }

  return (
    <div className="space-y-6">
      {/* 顶部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBackToList}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回列表
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold">
              {editingPolicy ? "编辑SLA策略" : "新建SLA策略"}
            </h1>
            <p className="text-sm text-muted-foreground mt-1">
              定义工单的服务级别协议和时效要求
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {editingPolicy && (
            <Badge variant="outline" className="text-xs">
              编辑模式
            </Badge>
          )}
          <Button variant="outline" onClick={handleCancel}>
            <X className="h-4 w-4 mr-2" />
            取消
          </Button>
          <Button onClick={handleSave} disabled={!isFormValid()}>
            <Save className="h-4 w-4 mr-2" />
            保存策略
          </Button>
        </div>
      </div>

      {/* 策略构建区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：基本信息和应用条件 */}
        <div className="space-y-6">
          <PolicyBasicInfo
            data={{
              name: policyData.name,
              description: policyData.description,
              isEnabled: policyData.isEnabled,
              priority: policyData.priority
            }}
            onChange={(data) => updatePolicyData("basic", data)}
            onNameChange={(name) => setPolicyData(prev => ({ ...prev, name }))}
            onDescriptionChange={(description) => setPolicyData(prev => ({ ...prev, description }))}
            onEnabledChange={(isEnabled) => setPolicyData(prev => ({ ...prev, isEnabled }))}
            onPriorityChange={(priority) => setPolicyData(prev => ({ ...prev, priority }))}
          />
          
          <PolicyConditions
            data={policyData.conditions}
            onChange={(conditions) => updatePolicyData("conditions", conditions)}
          />
        </div>
        
        {/* 右侧：SLA目标和工作日历 */}
        <div className="space-y-6">
          <PolicyTargets
            data={policyData.targets}
            onChange={(targets) => updatePolicyData("targets", targets)}
          />
          
          <PolicyCalendar
            data={policyData.calendar}
            onChange={(calendar) => updatePolicyData("calendar", calendar)}
          />
        </div>
      </div>

      {/* 预览和验证 */}
      <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
        <CardHeader>
          <CardTitle className="text-lg text-green-800 dark:text-green-200">
            策略预览
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">基本信息</h4>
              <div className="space-y-1 text-green-700 dark:text-green-300">
                <div>策略名称: {policyData.name || "未设置"}</div>
                <div>优先级: {policyData.priority}</div>
                <div>状态: {policyData.isEnabled ? "启用" : "停用"}</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">应用条件</h4>
              <div className="space-y-1 text-green-700 dark:text-green-300">
                <div>条件逻辑: {policyData.conditions.logic}</div>
                <div>条件数量: {policyData.conditions.rules.length} 个</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">SLA目标</h4>
              <div className="space-y-1 text-green-700 dark:text-green-300">
                <div>目标数量: {policyData.targets.length} 个</div>
                {policyData.targets.map((target, index) => (
                  <div key={index}>• {target.name}</div>
                ))}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">工作日历</h4>
              <div className="space-y-1 text-green-700 dark:text-green-300">
                <div>日历类型: {
                  policyData.calendar.type === "24x7" ? "7×24小时" :
                  policyData.calendar.type === "business_hours" ? "工作时间" : "自定义"
                }</div>
                <div>时区: {policyData.calendar.timezone}</div>
              </div>
            </div>
          </div>
          
          {!isFormValid() && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded p-3">
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                请完成以下必填项：
              </h4>
              <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                {!policyData.name.trim() && <li>• 策略名称</li>}
                {policyData.conditions.rules.length === 0 && <li>• 至少添加一个应用条件</li>}
                {policyData.targets.length === 0 && <li>• 至少添加一个SLA目标</li>}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
