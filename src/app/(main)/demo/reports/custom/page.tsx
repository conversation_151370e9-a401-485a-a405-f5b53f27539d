"use client"

import { useState } from "react"
import { ReportListView } from "./_components/report-list-view"
import { ReportBuilderView } from "./_components/report-builder-view"
import { ReportViewerView } from "./_components/report-viewer-view"

/**
 * 自定义报表页面
 * 支持报表管理列表视图、报表构建器视图和报表查看视图三种模式
 */
export default function CustomReportsPage() {
  const [currentView, setCurrentView] = useState<"list" | "builder" | "viewer">("list")
  const [editingReport, setEditingReport] = useState<any>(null)
  const [viewingReport, setViewingReport] = useState<any>(null)

  const handleCreateReport = () => {
    setEditingReport(null)
    setViewingReport(null)
    setCurrentView("builder")
  }

  const handleEditReport = (report: any) => {
    setEditingReport(report)
    setViewingReport(null)
    setCurrentView("builder")
  }

  const handleViewReport = (report: any) => {
    setViewingReport(report)
    setEditingReport(null)
    setCurrentView("viewer")
  }

  const handleBackToList = () => {
    setCurrentView("list")
    setEditingReport(null)
    setViewingReport(null)
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {currentView === "list" ? (
        <ReportListView
          onCreateReport={handleCreateReport}
          onEditReport={handleEditReport}
          onViewReport={handleViewReport}
        />
      ) : currentView === "builder" ? (
        <ReportBuilderView
          editingReport={editingReport}
          onBackToList={handleBackToList}
        />
      ) : (
        <ReportViewerView
          viewingReport={viewingReport}
          onBackToList={handleBackToList}
          onEditReport={handleEditReport}
        />
      )}
    </div>
  )
}
