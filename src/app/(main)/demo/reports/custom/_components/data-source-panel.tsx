"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { 
  Database, 
  Search, 
  ChevronDown, 
  ChevronRight,
  Hash,
  Calendar,
  Type,
  BarChart3,
  Clock,
  User
} from "lucide-react"

interface DataSourcePanelProps {
  selectedDataSource: string
  onDataSourceChange: (dataSource: string) => void
}

/**
 * 数据源与字段选择面板组件
 * 提供数据模型选择和字段拖拽功能
 */
export function DataSourcePanel({ selectedDataSource, onDataSourceChange }: DataSourcePanelProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [expandedCategories, setExpandedCategories] = useState<string[]>(["basic", "metrics"])

  // 数据源选项
  const dataSources = [
    { value: "workorders", label: "工单数据", description: "包含工单的基本信息、处理过程和结果" },
    { value: "sla", label: "SLA数据", description: "服务级别协议相关的时间和达成情况" },
    { value: "satisfaction", label: "满意度数据", description: "客户满意度评价和反馈信息" },
    { value: "users", label: "用户数据", description: "处理人、客户等用户相关信息" }
  ]

  // 字段定义
  const fieldCategories = {
    basic: {
      title: "基础信息",
      icon: Type,
      fields: [
        { id: "ticket_id", name: "工单ID", type: "dimension", dataType: "text", description: "唯一标识工单的编号" },
        { id: "title", name: "工单标题", type: "dimension", dataType: "text", description: "工单的标题描述" },
        { id: "type", name: "工单类型", type: "dimension", dataType: "text", description: "IT服务、客户投诉等类型" },
        { id: "priority", name: "优先级", type: "dimension", dataType: "text", description: "紧急、高、中、低" },
        { id: "status", name: "工单状态", type: "dimension", dataType: "text", description: "待处理、处理中、已关闭等" },
        { id: "customer_name", name: "客户姓名", type: "dimension", dataType: "text", description: "提交工单的客户" },
        { id: "customer_company", name: "客户公司", type: "dimension", dataType: "text", description: "客户所属公司" }
      ]
    },
    time: {
      title: "时间维度",
      icon: Calendar,
      fields: [
        { id: "created_date", name: "创建日期", type: "dimension", dataType: "date", description: "工单创建的日期" },
        { id: "created_month", name: "创建月份", type: "dimension", dataType: "date", description: "按月分组的创建时间" },
        { id: "created_week", name: "创建周", type: "dimension", dataType: "date", description: "按周分组的创建时间" },
        { id: "completed_date", name: "完成日期", type: "dimension", dataType: "date", description: "工单完成的日期" },
        { id: "sla_deadline", name: "SLA截止时间", type: "dimension", dataType: "datetime", description: "服务级别协议的截止时间" }
      ]
    },
    people: {
      title: "人员信息",
      icon: User,
      fields: [
        { id: "creator", name: "创建人", type: "dimension", dataType: "text", description: "创建工单的人员" },
        { id: "assignee", name: "处理人", type: "dimension", dataType: "text", description: "当前负责处理的人员" },
        { id: "department", name: "处理部门", type: "dimension", dataType: "text", description: "负责处理的部门" },
        { id: "reviewer", name: "回访员", type: "dimension", dataType: "text", description: "执行回访的人员" }
      ]
    },
    metrics: {
      title: "指标数据",
      icon: BarChart3,
      fields: [
        { id: "ticket_count", name: "工单数量", type: "metric", dataType: "number", aggregation: "count", description: "统计工单的数量" },
        { id: "resolution_time", name: "解决时长", type: "metric", dataType: "number", aggregation: "avg", description: "从创建到解决的平均时间" },
        { id: "response_time", name: "响应时长", type: "metric", dataType: "number", aggregation: "avg", description: "从创建到首次响应的时间" },
        { id: "satisfaction_score", name: "满意度评分", type: "metric", dataType: "number", aggregation: "avg", description: "客户满意度的平均分数" },
        { id: "sla_compliance", name: "SLA达成率", type: "metric", dataType: "number", aggregation: "percentage", description: "在SLA时间内完成的比例" },
        { id: "points", name: "工单积分", type: "metric", dataType: "number", aggregation: "sum", description: "工单的积分总和" }
      ]
    }
  }

  const getFieldIcon = (field: any) => {
    if (field.type === "metric") return <Hash className="h-4 w-4 text-blue-500" />
    if (field.dataType === "date" || field.dataType === "datetime") return <Calendar className="h-4 w-4 text-green-500" />
    return <Type className="h-4 w-4 text-gray-500" />
  }

  const getFieldTypeColor = (type: string) => {
    return type === "metric" ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300" : 
           "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
  }

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    )
  }

  const handleDragStart = (e: React.DragEvent, field: any) => {
    e.dataTransfer.setData("application/json", JSON.stringify(field))
    e.dataTransfer.effectAllowed = "copy"
  }

  // 过滤字段
  const filteredCategories = Object.entries(fieldCategories).map(([categoryId, category]) => ({
    id: categoryId,
    ...category,
    fields: category.fields.filter(field => 
      field.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      field.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.fields.length > 0)

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Database className="h-5 w-5" />
          <span>数据源与字段</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 数据源选择 */}
        <div className="space-y-2">
          <label className="text-sm font-medium">数据模型</label>
          <Select value={selectedDataSource} onValueChange={onDataSourceChange}>
            <SelectTrigger>
              <SelectValue placeholder="选择数据源" />
            </SelectTrigger>
            <SelectContent>
              {dataSources.map((source) => (
                <SelectItem key={source.value} value={source.value}>
                  <div>
                    <div className="font-medium">{source.label}</div>
                    <div className="text-xs text-muted-foreground">{source.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 字段搜索 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="搜索字段..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* 字段列表 */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {filteredCategories.map((category) => {
            const IconComponent = category.icon
            const isExpanded = expandedCategories.includes(category.id)
            
            return (
              <Collapsible key={category.id} open={isExpanded} onOpenChange={() => toggleCategory(category.id)}>
                <CollapsibleTrigger className="flex items-center justify-between w-full p-2 text-sm font-medium text-left bg-gray-50 dark:bg-gray-800 rounded hover:bg-gray-100 dark:hover:bg-gray-700">
                  <div className="flex items-center space-x-2">
                    <IconComponent className="h-4 w-4" />
                    <span>{category.title}</span>
                    <Badge variant="secondary" className="text-xs">
                      {category.fields.length}
                    </Badge>
                  </div>
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </CollapsibleTrigger>
                
                <CollapsibleContent className="space-y-1 mt-1">
                  {category.fields.map((field) => (
                    <div
                      key={field.id}
                      draggable
                      onDragStart={(e) => handleDragStart(e, field)}
                      className="flex items-center space-x-2 p-2 text-sm border rounded cursor-move hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                      title={field.description}
                    >
                      {getFieldIcon(field)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium truncate">{field.name}</span>
                          <Badge className={`text-xs ${getFieldTypeColor(field.type)}`}>
                            {field.type === "metric" ? "指标" : "维度"}
                          </Badge>
                        </div>
                        {field.aggregation && (
                          <div className="text-xs text-muted-foreground">
                            {field.aggregation === "count" && "计数"}
                            {field.aggregation === "sum" && "求和"}
                            {field.aggregation === "avg" && "平均值"}
                            {field.aggregation === "percentage" && "百分比"}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CollapsibleContent>
              </Collapsible>
            )
          })}
        </div>

        {/* 使用说明 */}
        <div className="text-xs text-muted-foreground bg-blue-50 dark:bg-blue-900/20 p-3 rounded border border-blue-200 dark:border-blue-800">
          <p className="font-medium text-blue-800 dark:text-blue-200 mb-1">使用提示：</p>
          <ul className="space-y-1 text-blue-700 dark:text-blue-300">
            <li>• 将字段拖拽到右侧配置区域</li>
            <li>• 维度用于分组和分类</li>
            <li>• 指标用于计算和衡量</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
