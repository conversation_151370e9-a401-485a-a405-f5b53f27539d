"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart3,
  LineChart,
  Pie<PERSON>hart,
  TrendingUp
} from "lucide-react"

interface ReportChartProps {
  chartType: string
  configuration: any
  dataSource: string
  filters?: Record<string, string>
  isViewMode?: boolean
}

/**
 * 报表图表组件
 * 用于在查看模式下显示完整的图表，比预览模式更大更详细
 */
export function ReportChart({ chartType, configuration, dataSource, filters = {}, isViewMode = false }: ReportChartProps) {
  
  // 生成模拟数据（在实际应用中这里会调用API获取真实数据）
  const generateChartData = () => {
    if (chartType === "table") {
      return [
        { "处理人": "张三", "IT服务": 15, "客户投诉": 3, "采购申请": 2, "工单数量": 20, "解决时长": "2.3小时", "满意度评分": 4.8 },
        { "处理人": "李四", "IT服务": 12, "客户投诉": 5, "采购申请": 1, "工单数量": 18, "解决时长": "1.8小时", "满意度评分": 4.9 },
        { "处理人": "王五", "IT服务": 8, "客户投诉": 2, "采购申请": 4, "工单数量": 14, "解决时长": "3.1小时", "满意度评分": 4.6 },
        { "处理人": "赵六", "IT服务": 6, "客户投诉": 1, "采购申请": 8, "工单数量": 15, "解决时长": "2.8小时", "满意度评分": 4.7 },
        { "处理人": "孙七", "IT服务": 10, "客户投诉": 4, "采购申请": 3, "工单数量": 17, "解决时长": "2.5小时", "满意度评分": 4.5 },
        { "处理人": "周八", "IT服务": 9, "客户投诉": 2, "采购申请": 5, "工单数量": 16, "解决时长": "2.9小时", "满意度评分": 4.4 }
      ]
    } else if (chartType === "line") {
      return [
        { month: "1月", value: 2.5, department: "IT支持部" },
        { month: "2月", value: 2.2, department: "IT支持部" },
        { month: "3月", value: 1.8, department: "IT支持部" },
        { month: "4月", value: 2.1, department: "IT支持部" },
        { month: "5月", value: 1.9, department: "IT支持部" },
        { month: "6月", value: 2.0, department: "IT支持部" },
        { month: "1月", value: 3.2, department: "网络部" },
        { month: "2月", value: 2.8, department: "网络部" },
        { month: "3月", value: 2.4, department: "网络部" },
        { month: "4月", value: 2.6, department: "网络部" },
        { month: "5月", value: 2.3, department: "网络部" },
        { month: "6月", value: 2.5, department: "网络部" }
      ]
    } else if (chartType === "bar") {
      return [
        { category: "IT服务", count: 45, month: "本月" },
        { category: "客户投诉", count: 12, month: "本月" },
        { category: "采购申请", count: 18, month: "本月" },
        { category: "售后维修", count: 8, month: "本月" },
        { category: "系统故障", count: 15, month: "本月" },
        { category: "IT服务", count: 38, month: "上月" },
        { category: "客户投诉", count: 15, month: "上月" },
        { category: "采购申请", count: 22, month: "上月" },
        { category: "售后维修", count: 6, month: "上月" },
        { category: "系统故障", count: 12, month: "上月" }
      ]
    } else if (chartType === "pie") {
      return [
        { category: "非常满意", value: 45, percentage: "45%" },
        { category: "满意", value: 32, percentage: "32%" },
        { category: "一般", value: 15, percentage: "15%" },
        { category: "不满意", value: 6, percentage: "6%" },
        { category: "非常不满意", value: 2, percentage: "2%" }
      ]
    }
    return []
  }

  const data = generateChartData()
  const chartHeight = isViewMode ? "h-96" : "h-64"

  const renderTable = () => {
    if (data.length === 0) return null

    const columns = Object.keys(data[0])
    
    return (
      <div className="bg-white dark:bg-gray-900 rounded-lg border">
        <div className="p-4 border-b bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">处理人绩效明细表</h3>
            <Badge variant="outline">
              共 {data.length} 条记录
            </Badge>
          </div>
        </div>
        <div className="overflow-auto max-h-80">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead key={column} className="font-semibold">
                    {column}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((row, index) => (
                <TableRow key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  {columns.map((column) => (
                    <TableCell key={column}>
                      {row[column] || "-"}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    )
  }

  const renderLineChart = () => {
    const departments = [...new Set(data.map(d => d.department))]
    const months = [...new Set(data.map(d => d.month))]
    
    return (
      <div className={`${chartHeight} p-6 bg-white dark:bg-gray-900 rounded-lg border`}>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">月度工单处理效率趋势</h3>
          <div className="flex items-center space-x-4">
            {departments.map((dept, index) => (
              <div key={dept} className="flex items-center space-x-2">
                <div className={`w-4 h-4 rounded ${index === 0 ? 'bg-blue-500' : 'bg-red-500'}`}></div>
                <span className="text-sm text-gray-600">{dept}</span>
              </div>
            ))}
          </div>
        </div>
        
        <div className="relative h-80">
          {/* Y轴标签 */}
          <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-sm text-gray-500">
            <span>3.5h</span>
            <span>3.0h</span>
            <span>2.5h</span>
            <span>2.0h</span>
            <span>1.5h</span>
          </div>
          
          {/* 图表区域 */}
          <div className="ml-12 h-full relative">
            {/* 网格线 */}
            <div className="absolute inset-0 flex flex-col justify-between">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="border-t border-gray-200 dark:border-gray-700"></div>
              ))}
            </div>
            
            {/* 折线 */}
            <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              {departments.map((dept, deptIndex) => {
                const deptData = data.filter(d => d.department === dept)
                const color = deptIndex === 0 ? "#3b82f6" : "#ef4444"
                
                return (
                  <g key={dept}>
                    <polyline
                      fill="none"
                      stroke={color}
                      strokeWidth="0.5"
                      points={deptData.map((d, i) => 
                        `${(i * 100) / (deptData.length - 1)},${100 - ((d.value - 1.5) / 2) * 100}`
                      ).join(" ")}
                      vectorEffect="non-scaling-stroke"
                    />
                    {deptData.map((d, i) => (
                      <circle
                        key={i}
                        cx={(i * 100) / (deptData.length - 1)}
                        cy={100 - ((d.value - 1.5) / 2) * 100}
                        r="1"
                        fill={color}
                        vectorEffect="non-scaling-stroke"
                      />
                    ))}
                  </g>
                )
              })}
            </svg>
            
            {/* X轴标签 */}
            <div className="absolute -bottom-8 left-0 right-0 flex justify-between text-sm text-gray-500">
              {months.map(month => (
                <span key={month}>{month}</span>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  const renderBarChart = () => {
    const categories = [...new Set(data.map(d => d.category))]
    const maxValue = Math.max(...data.map(d => d.count))
    
    return (
      <div className={`${chartHeight} p-6 bg-white dark:bg-gray-900 rounded-lg border`}>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">工单类型对比分析</h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded"></div>
              <span className="text-sm text-gray-600">本月</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-300 rounded"></div>
              <span className="text-sm text-gray-600">上月</span>
            </div>
          </div>
        </div>
        
        <div className="relative h-80">
          {/* Y轴标签 */}
          <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-sm text-gray-500">
            <span>{maxValue}</span>
            <span>{Math.round(maxValue * 0.75)}</span>
            <span>{Math.round(maxValue * 0.5)}</span>
            <span>{Math.round(maxValue * 0.25)}</span>
            <span>0</span>
          </div>
          
          {/* 柱状图 */}
          <div className="ml-12 h-full flex items-end justify-between space-x-4">
            {categories.map((category, index) => {
              const categoryData = data.filter(d => d.category === category)
              return (
                <div key={category} className="flex-1 flex items-end space-x-2">
                  {categoryData.map((d, i) => (
                    <div
                      key={i}
                      className={`flex-1 ${i === 0 ? 'bg-blue-500' : 'bg-blue-300'} rounded-t hover:opacity-80 transition-opacity`}
                      style={{ height: `${(d.count / maxValue) * 100}%` }}
                      title={`${d.month}: ${d.count}`}
                    ></div>
                  ))}
                </div>
              )
            })}
          </div>
          
          {/* X轴标签 */}
          <div className="absolute -bottom-8 left-12 right-0 flex justify-between text-sm text-gray-500">
            {categories.map(category => (
              <span key={category} className="text-center">{category}</span>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const renderPieChart = () => {
    const total = data.reduce((sum, d) => sum + d.value, 0)
    let currentAngle = 0
    
    const colors = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6"]
    
    return (
      <div className={`${chartHeight} p-6 bg-white dark:bg-gray-900 rounded-lg border`}>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">客户满意度分布</h3>
          <Badge variant="outline">
            总评价数: {total}
          </Badge>
        </div>
        
        <div className="flex items-center justify-center h-80">
          <div className="relative">
            <svg width="240" height="240" className="transform -rotate-90">
              {data.map((d, index) => {
                const angle = (d.value / total) * 360
                const startAngle = currentAngle
                const endAngle = currentAngle + angle
                currentAngle += angle
                
                const startAngleRad = (startAngle * Math.PI) / 180
                const endAngleRad = (endAngle * Math.PI) / 180
                
                const largeArcFlag = angle > 180 ? 1 : 0
                const x1 = 120 + 100 * Math.cos(startAngleRad)
                const y1 = 120 + 100 * Math.sin(startAngleRad)
                const x2 = 120 + 100 * Math.cos(endAngleRad)
                const y2 = 120 + 100 * Math.sin(endAngleRad)
                
                const pathData = [
                  `M 120 120`,
                  `L ${x1} ${y1}`,
                  `A 100 100 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                  `Z`
                ].join(' ')
                
                return (
                  <path
                    key={index}
                    d={pathData}
                    fill={colors[index]}
                    stroke="white"
                    strokeWidth="3"
                    className="hover:opacity-80 transition-opacity cursor-pointer"
                  />
                )
              })}
            </svg>
            
            {/* 中心文字 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold">{total}</div>
                <div className="text-sm text-gray-500">总评价</div>
              </div>
            </div>
          </div>
          
          {/* 图例 */}
          <div className="ml-8 space-y-3">
            {data.map((d, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div 
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: colors[index] }}
                ></div>
                <div className="flex-1">
                  <div className="font-medium">{d.category}</div>
                  <div className="text-sm text-gray-500">{d.value} 个 ({d.percentage})</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // 根据图表类型渲染相应的图表
  switch (chartType) {
    case "table":
      return renderTable()
    case "line":
      return renderLineChart()
    case "bar":
      return renderBarChart()
    case "pie":
      return renderPieChart()
    default:
      return (
        <div className="flex flex-col items-center justify-center h-64 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
          <BarChart3 className="h-16 w-16 text-gray-400 mb-4" />
          <p className="text-lg font-medium text-gray-600 dark:text-gray-300">
            暂不支持此图表类型
          </p>
        </div>
      )
  }
}
