"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Save, Download, Copy } from "lucide-react"
import { DataSourcePanel } from "./data-source-panel"
import { ConfigurationPanel } from "./configuration-panel"
import { PreviewPanel } from "./preview-panel"

interface ReportBuilderViewProps {
  editingReport: any
  onBackToList: () => void
}

/**
 * 报表构建器视图组件
 * 提供拖拽式报表构建功能，包含数据源选择、配置面板和实时预览
 */
export function ReportBuilderView({ editingReport, onBackToList }: ReportBuilderViewProps) {
  const [reportName, setReportName] = useState("")
  const [selectedDataSource, setSelectedDataSource] = useState("workorders")
  const [chartType, setChartType] = useState("table")
  const [configuration, setConfiguration] = useState({
    rows: [],
    columns: [],
    values: [],
    filters: []
  })

  // 根据编辑的报表加载示例配置数据
  useEffect(() => {
    if (editingReport) {
      setReportName(editingReport.name)

      // 根据报表类型设置相应的配置
      const reportConfig = getReportConfiguration(editingReport)
      setSelectedDataSource(reportConfig.dataSource)
      setChartType(reportConfig.chartType)
      setConfiguration(reportConfig.configuration)
    } else {
      // 新建报表时重置所有状态
      setReportName("")
      setSelectedDataSource("workorders")
      setChartType("table")
      setConfiguration({
        rows: [],
        columns: [],
        values: [],
        filters: []
      })
    }
  }, [editingReport])

  // 根据报表生成示例配置
  const getReportConfiguration = (report: any) => {
    const configurations = {
      "月度工单处理效率分析": {
        dataSource: "workorders",
        chartType: "line",
        configuration: {
          x_axis: [{ id: "created_month", name: "创建月份", type: "dimension", dataType: "date" }],
          y_axis: [{ id: "resolution_time", name: "解决时长", type: "metric", dataType: "number", aggregation: "avg" }],
          breakdown: [{ id: "department", name: "处理部门", type: "dimension", dataType: "text" }],
          filters: []
        }
      },
      "客户满意度分布报表": {
        dataSource: "satisfaction",
        chartType: "pie",
        configuration: {
          category: [{ id: "satisfaction_score", name: "满意度评分", type: "dimension", dataType: "text" }],
          value: [{ id: "ticket_count", name: "工单数量", type: "metric", dataType: "number", aggregation: "count" }],
          filters: []
        }
      },
      "工单类型趋势对比": {
        dataSource: "workorders",
        chartType: "bar",
        configuration: {
          x_axis: [{ id: "type", name: "工单类型", type: "dimension", dataType: "text" }],
          y_axis: [{ id: "ticket_count", name: "工单数量", type: "metric", dataType: "number", aggregation: "count" }],
          breakdown: [{ id: "created_month", name: "创建月份", type: "dimension", dataType: "date" }],
          filters: []
        }
      },
      "处理人绩效明细表": {
        dataSource: "users",
        chartType: "table",
        configuration: {
          rows: [{ id: "assignee", name: "处理人", type: "dimension", dataType: "text" }],
          columns: [{ id: "type", name: "工单类型", type: "dimension", dataType: "text" }],
          values: [
            { id: "ticket_count", name: "工单数量", type: "metric", dataType: "number", aggregation: "count" },
            { id: "resolution_time", name: "解决时长", type: "metric", dataType: "number", aggregation: "avg" },
            { id: "satisfaction_score", name: "满意度评分", type: "metric", dataType: "number", aggregation: "avg" }
          ],
          filters: []
        }
      }
    }

    return configurations[report.name] || {
      dataSource: "workorders",
      chartType: "table",
      configuration: {
        rows: [],
        columns: [],
        values: [],
        filters: []
      }
    }
  }

  const handleSave = () => {
    const reportData = {
      name: reportName,
      dataSource: selectedDataSource,
      chartType,
      configuration
    }

    if (editingReport) {
      console.log("更新报表:", { ...reportData, id: editingReport.id })
    } else {
      console.log("创建新报表:", reportData)
    }
    // 这里可以添加保存逻辑
  }

  const handleSaveAs = () => {
    console.log("另存为报表:", {
      name: reportName + " (副本)",
      dataSource: selectedDataSource,
      chartType,
      configuration
    })
    // 这里可以添加另存为逻辑
  }

  const handleExport = () => {
    console.log("导出报表:", reportName)
    // 这里可以添加导出逻辑
  }

  return (
    <div className="space-y-6">
      {/* 顶部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBackToList}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回列表
          </Button>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              {editingReport ? "编辑报表：" : "新建报表："}
            </span>
            <Input
              placeholder="请输入报表名称"
              value={reportName}
              onChange={(e) => setReportName(e.target.value)}
              className="w-64"
            />
            {editingReport && (
              <Badge variant="outline" className="text-xs">
                编辑模式
              </Badge>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleSaveAs}>
            <Copy className="h-4 w-4 mr-2" />
            另存为
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button onClick={handleSave} disabled={!reportName.trim()}>
            <Save className="h-4 w-4 mr-2" />
            {editingReport ? "更新" : "保存"}
          </Button>
        </div>
      </div>

      {/* 主要构建区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 min-h-[600px]">
        {/* 左侧：数据源与字段选择 */}
        <div className="lg:col-span-1">
          <DataSourcePanel
            selectedDataSource={selectedDataSource}
            onDataSourceChange={setSelectedDataSource}
          />
        </div>
        
        {/* 中间：配置面板 */}
        <div className="lg:col-span-1">
          <ConfigurationPanel
            chartType={chartType}
            onChartTypeChange={setChartType}
            configuration={configuration}
            onConfigurationChange={setConfiguration}
          />
        </div>
        
        {/* 右侧：实时预览 */}
        <div className="lg:col-span-2">
          <PreviewPanel
            chartType={chartType}
            configuration={configuration}
            dataSource={selectedDataSource}
          />
        </div>
      </div>
    </div>
  )
}
