"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Eye, 
  RefreshCw, 
  AlertTriangle,
  BarChart3,
  LineChart,
  PieChart,
  Table as TableIcon
} from "lucide-react"

interface PreviewPanelProps {
  chartType: string
  configuration: any
  dataSource: string
}

/**
 * 实时预览面板组件
 * 根据当前配置实时渲染报表预览效果
 */
export function PreviewPanel({ chartType, configuration, dataSource }: PreviewPanelProps) {
  
  // 模拟数据生成
  const generateMockData = () => {
    // 根据配置和图表类型生成模拟数据
    if (chartType === "table") {
      // 表格数据
      if (configuration.rows?.length > 0 && configuration.values?.length > 0) {
        return [
          { "处理人": "张三", "IT服务": 15, "客户投诉": 3, "采购申请": 2, "工单数量": 20, "解决时长": "2.3小时", "满意度评分": 4.8 },
          { "处理人": "李四", "IT服务": 12, "客户投诉": 5, "采购申请": 1, "工单数量": 18, "解决时长": "1.8小时", "满意度评分": 4.9 },
          { "处理人": "王五", "IT服务": 8, "客户投诉": 2, "采购申请": 4, "工单数量": 14, "解决时长": "3.1小时", "满意度评分": 4.6 },
          { "处理人": "赵六", "IT服务": 6, "客户投诉": 1, "采购申请": 8, "工单数量": 15, "解决时长": "2.8小时", "满意度评分": 4.7 }
        ]
      }
    } else if (chartType === "line") {
      // 折线图数据
      return [
        { month: "1月", value: 2.5, department: "IT支持部" },
        { month: "2月", value: 2.2, department: "IT支持部" },
        { month: "3月", value: 1.8, department: "IT支持部" },
        { month: "4月", value: 2.1, department: "IT支持部" },
        { month: "1月", value: 3.2, department: "网络部" },
        { month: "2月", value: 2.8, department: "网络部" },
        { month: "3月", value: 2.4, department: "网络部" },
        { month: "4月", value: 2.6, department: "网络部" }
      ]
    } else if (chartType === "bar") {
      // 柱状图数据
      return [
        { category: "IT服务", count: 45, month: "本月" },
        { category: "客户投诉", count: 12, month: "本月" },
        { category: "采购申请", count: 18, month: "本月" },
        { category: "售后维修", count: 8, month: "本月" },
        { category: "IT服务", count: 38, month: "上月" },
        { category: "客户投诉", count: 15, month: "上月" },
        { category: "采购申请", count: 22, month: "上月" },
        { category: "售后维修", count: 6, month: "上月" }
      ]
    } else if (chartType === "pie") {
      // 饼图数据
      return [
        { category: "非常满意", value: 45, percentage: "45%" },
        { category: "满意", value: 32, percentage: "32%" },
        { category: "一般", value: 15, percentage: "15%" },
        { category: "不满意", value: 6, percentage: "6%" },
        { category: "非常不满意", value: 2, percentage: "2%" }
      ]
    }
    return []
  }

  const mockData = generateMockData()

  // 检查配置是否完整
  const isConfigurationValid = () => {
    switch (chartType) {
      case "table":
        return configuration.values?.length > 0
      case "bar":
      case "line":
        return configuration.x_axis?.length > 0 && configuration.y_axis?.length > 0
      case "pie":
        return configuration.category?.length > 0 && configuration.value?.length > 0
      case "area":
        return configuration.x_axis?.length > 0 && configuration.y_axis?.length > 0
      case "scatter":
        return configuration.x_axis?.length > 0 && configuration.y_axis?.length > 0
      default:
        return false
    }
  }

  const renderTablePreview = () => {
    if (mockData.length === 0) return null

    // 根据配置动态生成列
    const getTableColumns = () => {
      const columns = []

      // 添加行维度
      if (configuration.rows?.length > 0) {
        configuration.rows.forEach(row => columns.push(row.name))
      }

      // 添加列维度作为数据列
      if (configuration.columns?.length > 0) {
        const columnDimension = configuration.columns[0]
        const uniqueValues = ["IT服务", "客户投诉", "采购申请"] // 模拟列值
        uniqueValues.forEach(value => columns.push(value))
      }

      // 添加指标列
      if (configuration.values?.length > 0) {
        configuration.values.forEach(value => {
          if (!configuration.columns?.length) {
            columns.push(value.name)
          }
        })
      }

      return columns.length > 0 ? columns : Object.keys(mockData[0])
    }

    const columns = getTableColumns()

    return (
      <div className="bg-white dark:bg-gray-900 rounded-lg border">
        <div className="p-4 border-b">
          <h3 className="text-sm font-medium">处理人绩效明细表</h3>
        </div>
        <div className="overflow-auto max-h-48">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead key={column} className="text-xs font-medium">
                    {column}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockData.slice(0, 4).map((row, index) => (
                <TableRow key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  {columns.map((column) => (
                    <TableCell key={column} className="text-xs">
                      {row[column] || "-"}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="p-2 border-t bg-gray-50 dark:bg-gray-800 text-xs text-gray-500 text-center">
          显示 4 行数据，共 {mockData.length} 行
        </div>
      </div>
    )
  }

  const renderLineChart = () => {
    const data = generateMockData()
    const departments = [...new Set(data.map(d => d.department))]
    const months = [...new Set(data.map(d => d.month))]

    return (
      <div className="h-64 p-4 bg-white dark:bg-gray-900 rounded-lg border">
        <h3 className="text-sm font-medium mb-4">月度工单处理效率趋势</h3>
        <div className="relative h-48">
          {/* Y轴标签 */}
          <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500">
            <span>3.5h</span>
            <span>3.0h</span>
            <span>2.5h</span>
            <span>2.0h</span>
            <span>1.5h</span>
          </div>

          {/* 图表区域 */}
          <div className="ml-8 h-full relative">
            {/* 网格线 */}
            <div className="absolute inset-0 flex flex-col justify-between">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="border-t border-gray-200 dark:border-gray-700"></div>
              ))}
            </div>

            {/* 折线 */}
            <svg className="absolute inset-0 w-full h-full">
              {departments.map((dept, deptIndex) => {
                const deptData = data.filter(d => d.department === dept)
                const color = deptIndex === 0 ? "#3b82f6" : "#ef4444"

                return (
                  <g key={dept}>
                    <polyline
                      fill="none"
                      stroke={color}
                      strokeWidth="2"
                      points={deptData.map((d, i) =>
                        `${(i * 100) / (deptData.length - 1)},${100 - ((d.value - 1.5) / 2) * 100}`
                      ).join(" ")}
                    />
                    {deptData.map((d, i) => (
                      <circle
                        key={i}
                        cx={(i * 100) / (deptData.length - 1)}
                        cy={100 - ((d.value - 1.5) / 2) * 100}
                        r="3"
                        fill={color}
                      />
                    ))}
                  </g>
                )
              })}
            </svg>

            {/* X轴标签 */}
            <div className="absolute -bottom-6 left-0 right-0 flex justify-between text-xs text-gray-500">
              {months.map(month => (
                <span key={month}>{month}</span>
              ))}
            </div>
          </div>
        </div>

        {/* 图例 */}
        <div className="flex justify-center space-x-4 mt-4">
          {departments.map((dept, index) => (
            <div key={dept} className="flex items-center space-x-1">
              <div className={`w-3 h-3 rounded ${index === 0 ? 'bg-blue-500' : 'bg-red-500'}`}></div>
              <span className="text-xs text-gray-600">{dept}</span>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const renderBarChart = () => {
    const data = generateMockData()
    const categories = [...new Set(data.map(d => d.category))]
    const maxValue = Math.max(...data.map(d => d.count))

    return (
      <div className="h-64 p-4 bg-white dark:bg-gray-900 rounded-lg border">
        <h3 className="text-sm font-medium mb-4">工单类型对比分析</h3>
        <div className="relative h-48">
          {/* Y轴标签 */}
          <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500">
            <span>{maxValue}</span>
            <span>{Math.round(maxValue * 0.75)}</span>
            <span>{Math.round(maxValue * 0.5)}</span>
            <span>{Math.round(maxValue * 0.25)}</span>
            <span>0</span>
          </div>

          {/* 柱状图 */}
          <div className="ml-8 h-full flex items-end justify-between space-x-2">
            {categories.map((category, index) => {
              const categoryData = data.filter(d => d.category === category)
              return (
                <div key={category} className="flex-1 flex items-end space-x-1">
                  {categoryData.map((d, i) => (
                    <div
                      key={i}
                      className={`flex-1 ${i === 0 ? 'bg-blue-500' : 'bg-blue-300'} rounded-t`}
                      style={{ height: `${(d.count / maxValue) * 100}%` }}
                      title={`${d.month}: ${d.count}`}
                    ></div>
                  ))}
                </div>
              )
            })}
          </div>

          {/* X轴标签 */}
          <div className="absolute -bottom-6 left-8 right-0 flex justify-between text-xs text-gray-500">
            {categories.map(category => (
              <span key={category} className="text-center">{category}</span>
            ))}
          </div>
        </div>

        {/* 图例 */}
        <div className="flex justify-center space-x-4 mt-4">
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span className="text-xs text-gray-600">本月</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-blue-300 rounded"></div>
            <span className="text-xs text-gray-600">上月</span>
          </div>
        </div>
      </div>
    )
  }

  const renderPieChart = () => {
    const data = generateMockData()
    const total = data.reduce((sum, d) => sum + d.value, 0)
    let currentAngle = 0

    const colors = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6"]

    return (
      <div className="h-64 p-4 bg-white dark:bg-gray-900 rounded-lg border">
        <h3 className="text-sm font-medium mb-4">客户满意度分布</h3>
        <div className="flex items-center justify-center h-48">
          <div className="relative">
            <svg width="160" height="160" className="transform -rotate-90">
              {data.map((d, index) => {
                const angle = (d.value / total) * 360
                const startAngle = currentAngle
                const endAngle = currentAngle + angle
                currentAngle += angle

                const startAngleRad = (startAngle * Math.PI) / 180
                const endAngleRad = (endAngle * Math.PI) / 180

                const largeArcFlag = angle > 180 ? 1 : 0
                const x1 = 80 + 70 * Math.cos(startAngleRad)
                const y1 = 80 + 70 * Math.sin(startAngleRad)
                const x2 = 80 + 70 * Math.cos(endAngleRad)
                const y2 = 80 + 70 * Math.sin(endAngleRad)

                const pathData = [
                  `M 80 80`,
                  `L ${x1} ${y1}`,
                  `A 70 70 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                  `Z`
                ].join(' ')

                return (
                  <path
                    key={index}
                    d={pathData}
                    fill={colors[index]}
                    stroke="white"
                    strokeWidth="2"
                  />
                )
              })}
            </svg>

            {/* 中心文字 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-lg font-bold">{total}</div>
                <div className="text-xs text-gray-500">总评价</div>
              </div>
            </div>
          </div>

          {/* 图例 */}
          <div className="ml-6 space-y-2">
            {data.map((d, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded"
                  style={{ backgroundColor: colors[index] }}
                ></div>
                <span className="text-xs">{d.category}</span>
                <span className="text-xs text-gray-500">{d.percentage}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const renderChartPreview = () => {
    switch (chartType) {
      case "line":
        return renderLineChart()
      case "bar":
        return renderBarChart()
      case "pie":
        return renderPieChart()
      case "area":
      case "scatter":
      default:
        const chartIcons = {
          area: LineChart,
          scatter: BarChart3
        }

        const IconComponent = chartIcons[chartType as keyof typeof chartIcons] || BarChart3

        return (
          <div className="flex flex-col items-center justify-center h-64 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
            <IconComponent className="h-16 w-16 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
              {chartType === "area" && "面积图预览"}
              {chartType === "scatter" && "散点图预览"}
            </p>
            <p className="text-sm text-gray-500 text-center">
              {chartType === "area" ? "面积图" : "散点图"}图表预览功能开发中
            </p>
          </div>
        )
    }
  }

  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-64 text-center">
      <AlertTriangle className="h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">
        配置不完整
      </h3>
      <p className="text-sm text-gray-500 mb-4">
        请完成以下配置以查看预览：
      </p>
      <div className="space-y-1 text-sm text-gray-500">
        {chartType === "table" && !configuration.values?.length && (
          <div>• 请添加至少一个指标字段到"值"货架</div>
        )}
        {(chartType === "bar" || chartType === "line" || chartType === "area") && (
          <>
            {!configuration.x_axis?.length && <div>• 请添加字段到"X轴"货架</div>}
            {!configuration.y_axis?.length && <div>• 请添加字段到"Y轴"货架</div>}
          </>
        )}
        {chartType === "pie" && (
          <>
            {!configuration.category?.length && <div>• 请添加字段到"分类"货架</div>}
            {!configuration.value?.length && <div>• 请添加字段到"数值"货架</div>}
          </>
        )}
        {chartType === "scatter" && (
          <>
            {!configuration.x_axis?.length && <div>• 请添加字段到"X轴"货架</div>}
            {!configuration.y_axis?.length && <div>• 请添加字段到"Y轴"货架</div>}
          </>
        )}
      </div>
    </div>
  )

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <span>实时预览</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {dataSource === "workorders" && "工单数据"}
              {dataSource === "sla" && "SLA数据"}
              {dataSource === "satisfaction" && "满意度数据"}
              {dataSource === "users" && "用户数据"}
            </Badge>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {!isConfigurationValid() ? (
          renderEmptyState()
        ) : chartType === "table" ? (
          <div className="overflow-auto max-h-96">
            {renderTablePreview()}
          </div>
        ) : (
          renderChartPreview()
        )}
        
        {/* 配置摘要 */}
        {isConfigurationValid() && (
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              当前配置摘要
            </h4>
            <div className="space-y-1 text-xs text-blue-700 dark:text-blue-300">
              <div>图表类型: {
                chartType === "table" ? "表格" :
                chartType === "bar" ? "柱状图" :
                chartType === "line" ? "折线图" :
                chartType === "pie" ? "饼图" :
                chartType === "area" ? "面积图" :
                chartType === "scatter" ? "散点图" : chartType
              }</div>
              <div>数据源: {
                dataSource === "workorders" ? "工单数据" :
                dataSource === "sla" ? "SLA数据" :
                dataSource === "satisfaction" ? "满意度数据" :
                dataSource === "users" ? "用户数据" : dataSource
              }</div>
              {Object.entries(configuration).map(([key, value]: [string, any]) => {
                if (value && value.length > 0) {
                  return (
                    <div key={key}>
                      {key}: {value.map((field: any) => field.name).join(", ")}
                    </div>
                  )
                }
                return null
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
