"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Settings,
  Table,
  BarChart3,
  LineChart,
  Pie<PERSON>hart,
  TrendingUp,
  X,
  Plus
} from "lucide-react"

interface ConfigurationPanelProps {
  chartType: string
  onChartTypeChange: (type: string) => void
  configuration: any
  onConfigurationChange: (config: any) => void
}

/**
 * 配置面板组件
 * 提供图表类型选择和配置"货架"拖拽区域
 */
export function ConfigurationPanel({ 
  chartType, 
  onChartTypeChange, 
  configuration, 
  onConfigurationChange 
}: ConfigurationPanelProps) {
  
  // 图表类型选项
  const chartTypes = [
    { id: "table", name: "表格", icon: Table, description: "数据表格展示" },
    { id: "bar", name: "柱状图", icon: BarChart3, description: "比较不同类别的数据" },
    { id: "line", name: "折线图", icon: Line<PERSON>hart, description: "展示趋势变化" },
    { id: "pie", name: "饼图", icon: PieChart, description: "展示占比分布" },
    { id: "area", name: "面积图", icon: TrendingUp, description: "强调数量变化" },
    { id: "scatter", name: "散点图", icon: BarChart3, description: "展示相关性" }
  ]

  // 根据图表类型获取配置货架
  const getConfigurationShelves = () => {
    switch (chartType) {
      case "table":
        return [
          { id: "rows", name: "行", description: "拖入维度字段作为表格行", allowedTypes: ["dimension"] },
          { id: "columns", name: "列", description: "拖入维度字段作为表格列", allowedTypes: ["dimension"] },
          { id: "values", name: "值", description: "拖入指标字段作为数值", allowedTypes: ["metric"] }
        ]
      case "bar":
      case "line":
        return [
          { id: "x_axis", name: "X轴", description: "拖入维度字段", allowedTypes: ["dimension"] },
          { id: "y_axis", name: "Y轴", description: "拖入指标字段", allowedTypes: ["metric"] },
          { id: "breakdown", name: "分解/图例", description: "拖入维度字段进行分组", allowedTypes: ["dimension"] }
        ]
      case "pie":
        return [
          { id: "category", name: "分类", description: "拖入维度字段", allowedTypes: ["dimension"] },
          { id: "value", name: "数值", description: "拖入指标字段", allowedTypes: ["metric"] }
        ]
      case "area":
        return [
          { id: "x_axis", name: "X轴", description: "拖入时间维度", allowedTypes: ["dimension"] },
          { id: "y_axis", name: "Y轴", description: "拖入指标字段", allowedTypes: ["metric"] },
          { id: "stack", name: "堆叠", description: "拖入维度字段进行堆叠", allowedTypes: ["dimension"] }
        ]
      case "scatter":
        return [
          { id: "x_axis", name: "X轴", description: "拖入指标字段", allowedTypes: ["metric"] },
          { id: "y_axis", name: "Y轴", description: "拖入指标字段", allowedTypes: ["metric"] },
          { id: "size", name: "大小", description: "拖入指标字段控制点大小", allowedTypes: ["metric"] },
          { id: "color", name: "颜色", description: "拖入维度字段进行分组", allowedTypes: ["dimension"] }
        ]
      default:
        return []
    }
  }

  const handleDrop = (e: React.DragEvent, shelfId: string) => {
    e.preventDefault()
    try {
      const fieldData = JSON.parse(e.dataTransfer.getData("application/json"))
      const shelf = getConfigurationShelves().find(s => s.id === shelfId)
      
      // 检查字段类型是否允许
      if (shelf && shelf.allowedTypes.includes(fieldData.type)) {
        const newConfig = { ...configuration }
        
        // 根据货架类型处理字段
        if (shelfId === "values" || shelfId === "y_axis" || shelfId === "value" || shelfId === "size") {
          // 这些货架可以有多个字段
          if (!newConfig[shelfId]) newConfig[shelfId] = []
          newConfig[shelfId] = [...newConfig[shelfId], fieldData]
        } else {
          // 其他货架只能有一个字段
          newConfig[shelfId] = [fieldData]
        }
        
        onConfigurationChange(newConfig)
      }
    } catch (error) {
      console.error("拖拽处理失败:", error)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const removeField = (shelfId: string, fieldIndex: number) => {
    const newConfig = { ...configuration }
    if (newConfig[shelfId]) {
      newConfig[shelfId] = newConfig[shelfId].filter((_: any, index: number) => index !== fieldIndex)
    }
    onConfigurationChange(newConfig)
  }

  const shelves = getConfigurationShelves()

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>报表配置</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 图表类型选择 */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">图表类型</h3>
          <div className="grid grid-cols-2 gap-2">
            {chartTypes.map((type) => {
              const IconComponent = type.icon
              const isSelected = chartType === type.id
              
              return (
                <Button
                  key={type.id}
                  variant={isSelected ? "default" : "outline"}
                  size="sm"
                  onClick={() => onChartTypeChange(type.id)}
                  className="h-auto p-3 flex flex-col items-center space-y-1"
                  title={type.description}
                >
                  <IconComponent className="h-4 w-4" />
                  <span className="text-xs">{type.name}</span>
                </Button>
              )
            })}
          </div>
        </div>

        {/* 配置货架 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium">配置货架</h3>
          <div className="space-y-3">
            {shelves.map((shelf) => (
              <div key={shelf.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">{shelf.name}</label>
                  <span className="text-xs text-muted-foreground">{shelf.description}</span>
                </div>
                
                <div
                  className="min-h-[60px] p-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800 transition-colors hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  onDrop={(e) => handleDrop(e, shelf.id)}
                  onDragOver={handleDragOver}
                >
                  {configuration[shelf.id]?.length > 0 ? (
                    <div className="space-y-2">
                      {configuration[shelf.id].map((field: any, index: number) => (
                        <div key={index} className="flex items-center justify-between bg-white dark:bg-gray-700 p-2 rounded border">
                          <div className="flex items-center space-x-2">
                            <Badge className={field.type === "metric" ? "bg-blue-500" : "bg-green-500"}>
                              {field.type === "metric" ? "指标" : "维度"}
                            </Badge>
                            <span className="text-sm font-medium">{field.name}</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeField(shelf.id, index)}
                            className="h-6 w-6 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full text-sm text-muted-foreground">
                      <Plus className="h-4 w-4 mr-2" />
                      拖拽字段到此处
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 筛选器配置 */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">筛选器</h3>
          <div
            className="min-h-[60px] p-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800 transition-colors hover:border-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20"
            onDrop={(e) => handleDrop(e, "filters")}
            onDragOver={handleDragOver}
          >
            {configuration.filters?.length > 0 ? (
              <div className="space-y-2">
                {configuration.filters.map((field: any, index: number) => (
                  <div key={index} className="flex items-center justify-between bg-white dark:bg-gray-700 p-2 rounded border">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">筛选</Badge>
                      <span className="text-sm font-medium">{field.name}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeField("filters", index)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-sm text-muted-foreground">
                <Plus className="h-4 w-4 mr-2" />
                拖拽字段添加筛选器
              </div>
            )}
          </div>
        </div>

        {/* 配置提示 */}
        <div className="text-xs text-muted-foreground bg-green-50 dark:bg-green-900/20 p-3 rounded border border-green-200 dark:border-green-800">
          <p className="font-medium text-green-800 dark:text-green-200 mb-1">配置提示：</p>
          <ul className="space-y-1 text-green-700 dark:text-green-300">
            <li>• 从左侧拖拽字段到对应货架</li>
            <li>• 维度字段用于分组和分类</li>
            <li>• 指标字段用于数值计算</li>
            <li>• 点击 × 可移除已配置的字段</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
