"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Download, Edit, RefreshCw, Calendar, User, Eye } from "lucide-react"
import { ReportChart } from "./report-chart"

interface ReportViewerViewProps {
  viewingReport: any
  onBackToList: () => void
  onEditReport: (report: any) => void
}

/**
 * 报表查看器视图组件
 * 提供报表的只读查看功能，包含筛选器交互和导出功能
 */
export function ReportViewerView({ viewingReport, onBackToList, onEditReport }: ReportViewerViewProps) {
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [lastUpdated, setLastUpdated] = useState(new Date())

  // 根据查看的报表获取配置
  const getReportConfiguration = (report: any) => {
    const configurations = {
      "月度工单处理效率分析": {
        dataSource: "workorders",
        chartType: "line",
        configuration: {
          x_axis: [{ id: "created_month", name: "创建月份", type: "dimension", dataType: "date" }],
          y_axis: [{ id: "resolution_time", name: "解决时长", type: "metric", dataType: "number", aggregation: "avg" }],
          breakdown: [{ id: "department", name: "处理部门", type: "dimension", dataType: "text" }],
          filters: [
            { id: "date_range", name: "时间范围", type: "dimension", dataType: "date" },
            { id: "priority", name: "优先级", type: "dimension", dataType: "text" }
          ]
        }
      },
      "客户满意度分布报表": {
        dataSource: "satisfaction",
        chartType: "pie",
        configuration: {
          category: [{ id: "satisfaction_score", name: "满意度评分", type: "dimension", dataType: "text" }],
          value: [{ id: "ticket_count", name: "工单数量", type: "metric", dataType: "number", aggregation: "count" }],
          filters: [
            { id: "department", name: "部门", type: "dimension", dataType: "text" },
            { id: "customer_type", name: "客户类型", type: "dimension", dataType: "text" }
          ]
        }
      },
      "工单类型趋势对比": {
        dataSource: "workorders",
        chartType: "bar",
        configuration: {
          x_axis: [{ id: "type", name: "工单类型", type: "dimension", dataType: "text" }],
          y_axis: [{ id: "ticket_count", name: "工单数量", type: "metric", dataType: "number", aggregation: "count" }],
          breakdown: [{ id: "created_month", name: "创建月份", type: "dimension", dataType: "date" }],
          filters: [
            { id: "date_range", name: "时间范围", type: "dimension", dataType: "date" },
            { id: "status", name: "工单状态", type: "dimension", dataType: "text" }
          ]
        }
      },
      "处理人绩效明细表": {
        dataSource: "users",
        chartType: "table",
        configuration: {
          rows: [{ id: "assignee", name: "处理人", type: "dimension", dataType: "text" }],
          columns: [{ id: "type", name: "工单类型", type: "dimension", dataType: "text" }],
          values: [
            { id: "ticket_count", name: "工单数量", type: "metric", dataType: "number", aggregation: "count" },
            { id: "resolution_time", name: "解决时长", type: "metric", dataType: "number", aggregation: "avg" },
            { id: "satisfaction_score", name: "满意度评分", type: "metric", dataType: "number", aggregation: "avg" }
          ],
          filters: [
            { id: "department", name: "部门", type: "dimension", dataType: "text" },
            { id: "date_range", name: "时间范围", type: "dimension", dataType: "date" }
          ]
        }
      }
    }

    return configurations[report?.name] || {
      dataSource: "workorders",
      chartType: "table",
      configuration: { filters: [] }
    }
  }

  const reportConfig = getReportConfiguration(viewingReport)

  const handleExport = () => {
    console.log("导出报表:", viewingReport?.name)
    // 这里可以添加导出逻辑
  }

  const handleRefresh = () => {
    setLastUpdated(new Date())
    console.log("刷新报表数据")
    // 这里可以添加数据刷新逻辑
  }

  const handleFilterChange = (filterId: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterId]: value
    }))
  }

  // 筛选器选项
  const getFilterOptions = (filterId: string) => {
    const options = {
      date_range: [
        { value: "last_7_days", label: "最近7天" },
        { value: "last_30_days", label: "最近30天" },
        { value: "last_3_months", label: "最近3个月" },
        { value: "last_year", label: "最近一年" }
      ],
      priority: [
        { value: "urgent", label: "紧急" },
        { value: "high", label: "高" },
        { value: "medium", label: "中" },
        { value: "low", label: "低" }
      ],
      department: [
        { value: "it_support", label: "IT支持部" },
        { value: "network", label: "网络部" },
        { value: "security", label: "安全部" },
        { value: "development", label: "开发部" }
      ],
      customer_type: [
        { value: "vip", label: "VIP客户" },
        { value: "enterprise", label: "企业客户" },
        { value: "individual", label: "个人客户" }
      ],
      status: [
        { value: "open", label: "待处理" },
        { value: "in_progress", label: "处理中" },
        { value: "resolved", label: "已解决" },
        { value: "closed", label: "已关闭" }
      ]
    }
    return options[filterId] || []
  }

  if (!viewingReport) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">未找到报表数据</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 顶部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBackToList}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回列表
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold">{viewingReport.name}</h1>
            <p className="text-sm text-muted-foreground mt-1">
              {viewingReport.description}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-xs">
            <Eye className="h-3 w-3 mr-1" />
            查看模式
          </Badge>
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button onClick={() => onEditReport(viewingReport)}>
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
        </div>
      </div>

      {/* 筛选器区域 */}
      {reportConfig.configuration.filters?.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">筛选条件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {reportConfig.configuration.filters.map((filter: any) => (
                <div key={filter.id} className="space-y-2">
                  <label className="text-sm font-medium">{filter.name}</label>
                  <Select
                    value={filters[filter.id] || "all"}
                    onValueChange={(value) => handleFilterChange(filter.id, value === "all" ? "" : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={`选择${filter.name}`} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      {getFilterOptions(filter.id).map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 主要图表展示区域 */}
      <Card>
        <CardContent className="p-6">
          <ReportChart
            chartType={reportConfig.chartType}
            configuration={reportConfig.configuration}
            dataSource={reportConfig.dataSource}
            filters={filters}
            isViewMode={true}
          />
        </CardContent>
      </Card>

      {/* 报表信息面板 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 mb-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">创建信息</span>
            </div>
            <div className="space-y-1 text-sm text-muted-foreground">
              <div>创建人: {viewingReport.creator}</div>
              <div>创建时间: {new Date(viewingReport.createdAt).toLocaleString()}</div>
              <div>最后修改: {new Date(viewingReport.updatedAt).toLocaleString()}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">数据信息</span>
            </div>
            <div className="space-y-1 text-sm text-muted-foreground">
              <div>数据源: {
                reportConfig.dataSource === "workorders" ? "工单数据" :
                reportConfig.dataSource === "sla" ? "SLA数据" :
                reportConfig.dataSource === "satisfaction" ? "满意度数据" :
                reportConfig.dataSource === "users" ? "用户数据" : reportConfig.dataSource
              }</div>
              <div>最后更新: {lastUpdated.toLocaleString()}</div>
              <div>数据范围: 最近30天</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Eye className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">使用统计</span>
            </div>
            <div className="space-y-1 text-sm text-muted-foreground">
              <div>查看次数: {viewingReport.viewCount}</div>
              <div>分享状态: {viewingReport.isShared ? "已分享" : "私有"}</div>
              <div>图表类型: {
                reportConfig.chartType === "table" ? "表格" :
                reportConfig.chartType === "bar" ? "柱状图" :
                reportConfig.chartType === "line" ? "折线图" :
                reportConfig.chartType === "pie" ? "饼图" : reportConfig.chartType
              }</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
