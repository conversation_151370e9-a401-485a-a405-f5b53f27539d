"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Copy, 
  Share, 
  Trash2,
  BarChart3,
  PieChart,
  LineChart,
  Table as TableIcon
} from "lucide-react"

interface ReportListViewProps {
  onCreateReport: () => void
  onEditReport: (report: any) => void
  onViewReport: (report: any) => void
}

/**
 * 报表管理列表视图组件
 * 展示所有已创建的自定义报表，支持搜索、查看、编辑等操作
 */
export function ReportListView({ onCreateReport, onEditReport, onViewReport }: ReportListViewProps) {
  const [searchQuery, setSearchQuery] = useState("")

  // 模拟报表数据
  const reports = [
    {
      id: "1",
      name: "月度工单处理效率分析",
      creator: "张三",
      creatorAvatar: "/avatars/01.png",
      createdAt: "2024-01-10 14:30:00",
      updatedAt: "2024-01-15 09:20:00",
      description: "按月统计各部门工单处理效率和SLA达成情况",
      chartType: "line",
      isShared: true,
      viewCount: 45
    },
    {
      id: "2", 
      name: "客户满意度分布报表",
      creator: "李四",
      creatorAvatar: "/avatars/02.png",
      createdAt: "2024-01-08 16:45:00",
      updatedAt: "2024-01-12 11:30:00",
      description: "展示不同客户群体的满意度评分分布情况",
      chartType: "pie",
      isShared: false,
      viewCount: 23
    },
    {
      id: "3",
      name: "工单类型趋势对比",
      creator: "王五",
      creatorAvatar: "/avatars/03.png", 
      createdAt: "2024-01-05 10:15:00",
      updatedAt: "2024-01-14 15:45:00",
      description: "对比不同工单类型的数量变化趋势",
      chartType: "bar",
      isShared: true,
      viewCount: 67
    },
    {
      id: "4",
      name: "处理人绩效明细表",
      creator: "赵六",
      creatorAvatar: "/avatars/04.png",
      createdAt: "2024-01-03 13:20:00", 
      updatedAt: "2024-01-13 08:10:00",
      description: "详细展示每个处理人的工单处理数量和质量指标",
      chartType: "table",
      isShared: false,
      viewCount: 34
    }
  ]

  const getChartIcon = (chartType: string) => {
    switch (chartType) {
      case "line": return <LineChart className="h-4 w-4" />
      case "bar": return <BarChart3 className="h-4 w-4" />
      case "pie": return <PieChart className="h-4 w-4" />
      case "table": return <TableIcon className="h-4 w-4" />
      default: return <BarChart3 className="h-4 w-4" />
    }
  }

  const getChartTypeName = (chartType: string) => {
    switch (chartType) {
      case "line": return "折线图"
      case "bar": return "柱状图"
      case "pie": return "饼图"
      case "table": return "表格"
      default: return "图表"
    }
  }

  const filteredReports = reports.filter(report =>
    report.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    report.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    report.creator.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleViewReportClick = (report: any) => {
    onViewReport(report)
  }

  const handleCopyReport = (report: any) => {
    console.log("复制报表:", report.name)
  }

  const handleShareReport = (report: any) => {
    console.log("分享报表:", report.name)
  }

  const handleDeleteReport = (report: any) => {
    console.log("删除报表:", report.name)
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            自定义报表
          </h1>
          <p className="text-muted-foreground mt-2">
            创建和管理您的个性化数据报表，灵活组合各种数据维度和可视化图表
          </p>
        </div>
        
        <Button onClick={onCreateReport} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          新建自定义报表
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="搜索报表名称、描述或创建人..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <span>共 {filteredReports.length} 个报表</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 报表列表 */}
      <Card>
        <CardHeader>
          <CardTitle>我的报表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>报表名称</TableHead>
                <TableHead>创建人</TableHead>
                <TableHead>图表类型</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>最后修改</TableHead>
                <TableHead>查看次数</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReports.map((report) => (
                <TableRow key={report.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <TableCell>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {report.name}
                      </p>
                      <p className="text-sm text-muted-foreground mt-1">
                        {report.description}
                      </p>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={report.creatorAvatar} alt={report.creator} />
                        <AvatarFallback>{report.creator[0]}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{report.creator}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getChartIcon(report.chartType)}
                      <span className="text-sm">{getChartTypeName(report.chartType)}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <span className="text-sm text-muted-foreground">
                      {new Date(report.createdAt).toLocaleDateString()}
                    </span>
                  </TableCell>
                  
                  <TableCell>
                    <span className="text-sm text-muted-foreground">
                      {new Date(report.updatedAt).toLocaleDateString()}
                    </span>
                  </TableCell>
                  
                  <TableCell>
                    <span className="text-sm">{report.viewCount}</span>
                  </TableCell>
                  
                  <TableCell>
                    {report.isShared ? (
                      <Badge variant="default" className="bg-green-500">
                        <Share className="h-3 w-3 mr-1" />
                        已分享
                      </Badge>
                    ) : (
                      <Badge variant="secondary">私有</Badge>
                    )}
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewReportClick(report)}>
                          <Eye className="h-4 w-4 mr-2" />
                          查看
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEditReport(report)}>
                          <Edit className="h-4 w-4 mr-2" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyReport(report)}>
                          <Copy className="h-4 w-4 mr-2" />
                          复制
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleShareReport(report)}>
                          <Share className="h-4 w-4 mr-2" />
                          分享
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteReport(report)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredReports.length === 0 && (
            <div className="text-center py-12">
              <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                {searchQuery ? "没有找到匹配的报表" : "还没有创建任何自定义报表"}
              </p>
              {!searchQuery && (
                <Button onClick={onCreateReport} className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  创建第一个报表
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
