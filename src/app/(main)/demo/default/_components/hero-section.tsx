import { Button } from "@/components/ui/button"
import { ArrowRight, Play } from "lucide-react"

/**
 * 主页英雄区域组件
 * 展示产品的核心价值主张和主要行动号召
 */
export function HeroSection() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-blue-900">
      <div className="container mx-auto px-6 py-16 md:py-24 lg:py-32">
        <div className="mx-auto max-w-4xl text-center">
          {/* 主标题 */}
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl md:text-6xl lg:text-7xl">
            不再是处理工单
            <br />
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              而是经营客户关系
            </span>
          </h1>
          
          {/* 副标题 */}
          <p className="mx-auto mt-6 max-w-3xl text-lg leading-8 text-gray-600 dark:text-gray-300 sm:text-xl">
            欢迎来到下一代服务运营。我们的一站式智能平台，将繁琐的服务请求转化为提升效率、优化流程、并赢得客户忠诚度的宝贵机会。
          </p>
          
          {/* 行动号召按钮 */}
          <div className="mt-10 flex flex-col items-center justify-center gap-4 sm:flex-row sm:gap-6">
            <Button size="lg" className="group">
              预约产品演示
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
            <Button variant="outline" size="lg" className="group">
              <Play className="mr-2 h-4 w-4" />
              免费开始试用
            </Button>
          </div>
          
          {/* 信任指标 */}
          <div className="mt-16">
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
              已被数千家企业信赖
            </p>
            <div className="mt-6 flex flex-wrap items-center justify-center gap-8 opacity-60">
              {/* 实际公司Logo */}
              <div className="flex items-center space-x-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <span className="text-sm font-bold text-blue-600 dark:text-blue-400">华</span>
                </div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">华为技术</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/30">
                  <span className="text-sm font-bold text-red-600 dark:text-red-400">京</span>
                </div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">京东集团</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/30">
                  <span className="text-sm font-bold text-green-600 dark:text-green-400">招</span>
                </div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">招商银行</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/30">
                  <span className="text-sm font-bold text-purple-600 dark:text-purple-400">海</span>
                </div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">海尔集团</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900/30">
                  <span className="text-sm font-bold text-orange-600 dark:text-orange-400">政</span>
                </div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">政府机构</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute left-1/2 top-0 -z-10 -translate-x-1/2 blur-3xl xl:-top-6" aria-hidden="true">
          <div
            className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-blue-400 to-indigo-300 opacity-20"
            style={{
              clipPath:
                'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
            }}
          />
        </div>
      </div>
    </section>
  )
}
