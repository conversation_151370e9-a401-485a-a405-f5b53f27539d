import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Workflow, Zap, Users, CheckCircle } from "lucide-react"

/**
 * 解决方案展示区域组件
 * 展示平台的核心解决方案和能力
 */
export function SolutionSection() {
  const solutions = [
    {
      icon: Workflow,
      title: "可视化流程，灵活构建",
      subtitle: "Build",
      features: [
        {
          name: "动态模板引擎",
          description: "告别一成不变的表单！通过拖拽式设计器，轻松创建贴合您任何业务场景（IT支持、客户投诉、售后服务、行政审批）的工单模板。"
        },
        {
          name: "自动化规则",
          description: "设定「如果...那么...」规则，实现工单的自动分类、指派、状态更新和通知，将团队从重复性工作中解放。"
        }
      ],
      color: "bg-blue-500"
    },
    {
      icon: Zap,
      title: "智能调度，精准触达",
      subtitle: "Dispatch",
      features: [
        {
          name: "AI智能派单",
          description: "我们的「智能调度大脑」能实时分析工单需求和员工的技能、负载与历史表现，将每一个任务精准地分配给最合适的处理人。"
        },
        {
          name: "工单池与抢单模式",
          description: "营造积极主动的工作氛围，让团队成员根据自身能力和意愿，高效认领任务。"
        }
      ],
      color: "bg-green-500"
    },
    {
      icon: Users,
      title: "无缝协同，信息通达",
      subtitle: "Collaborate",
      features: [
        {
          name: "统一作战指挥室",
          description: "在一个工单详情页，即可看到完整的处理时间轴、所有人的沟通记录和关联信息，彻底打破信息孤岛。"
        },
        {
          name: "矩阵式主协办",
          description: "在工单创建之初，即可建立跨部门的协同团队，并行处理复杂问题，效率倍增。"
        }
      ],
      color: "bg-purple-500"
    },
    {
      icon: CheckCircle,
      title: "全方位质检，服务闭环",
      subtitle: "Assure",
      features: [
        {
          name: "独立的专业回访",
          description: "独创的回访员角色，确保每一个已办结的工单都经过独立的质量检验，将客户的真实满意度作为服务的终点。"
        },
        {
          name: "SLA全生命周期监控",
          description: "从承诺（策略设置）到执行（实时倒计时）再到复盘（达成率报告），确保您的每一份服务承诺都精准兑现。"
        }
      ],
      color: "bg-orange-500"
    }
  ]

  return (
    <section className="py-16 md:py-24 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-6">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            一个平台，重构您的整个服务体系
          </h2>
          <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
            我们提供了一个前所未有的强大平台，将 
            <span className="font-semibold text-blue-600 dark:text-blue-400"> 动态流程引擎、矩阵式协同、AI智能辅助、全景数据分析 </span>
            融为一体
          </p>
        </div>
        
        <div className="mt-16 grid grid-cols-1 gap-8 lg:grid-cols-2">
          {solutions.map((solution, index) => {
            const IconComponent = solution.icon
            return (
              <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-4">
                    <div className={`rounded-lg p-3 ${solution.color} text-white`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <div>
                      <Badge variant="secondary" className="mb-2">
                        {solution.subtitle}
                      </Badge>
                      <CardTitle className="text-xl text-gray-900 dark:text-white">
                        {solution.title}
                      </CardTitle>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {solution.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="space-y-2">
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        {feature.name}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </section>
  )
}
