import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Quote, Star } from "lucide-react"

/**
 * 客户证言区域组件
 * 展示客户评价和成功案例
 */
export function TestimonialsSection() {
  const testimonials = [
    {
      quote: "自从使用了这个平台，我们的客户首次响应时间缩短了60%，客户满意度达到了历史新高。它彻底改变了我们的服务方式。",
      author: "李明",
      title: "服务总监",
      company: "某知名科技公司",
      avatar: "/avatars/01.png",
      rating: 5,
      metrics: [
        { label: "响应时间缩短", value: "60%" },
        { label: "满意度提升", value: "35%" }
      ]
    },
    {
      quote: "平台的AI智能派单功能让我们的工作效率提升了一倍，每个工单都能找到最合适的处理人，团队协作变得前所未有的顺畅。",
      author: "王芳",
      title: "运营经理",
      company: "领先制造企业",
      avatar: "/avatars/02.png",
      rating: 5,
      metrics: [
        { label: "工作效率提升", value: "100%" },
        { label: "派单准确率", value: "95%" }
      ]
    },
    {
      quote: "作为政府部门，我们需要处理大量市民诉求。这个平台帮助我们建立了标准化的处理流程，大大提升了公共服务质量。",
      author: "张强",
      title: "信息化主任",
      company: "某市政府服务中心",
      avatar: "/avatars/03.png",
      rating: 5,
      metrics: [
        { label: "处理效率提升", value: "80%" },
        { label: "市民满意度", value: "92%" }
      ]
    }
  ]

  return (
    <section className="py-16 md:py-24 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            客户的成功，就是我们的成功
          </h2>
          <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
            听听我们的客户如何通过我们的平台实现业务转型
          </p>
        </div>
        
        <div className="mt-16 grid grid-cols-1 gap-8 lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
              <CardContent className="p-6">
                {/* 评分 */}
                <div className="flex items-center space-x-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                
                {/* 引用图标 */}
                <Quote className="h-8 w-8 text-blue-500 mb-4" />
                
                {/* 证言内容 */}
                <blockquote className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
                  "{testimonial.quote}"
                </blockquote>
                
                {/* 关键指标 */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  {testimonial.metrics.map((metric, metricIndex) => (
                    <div key={metricIndex} className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {metric.value}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {metric.label}
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* 作者信息 */}
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src={testimonial.avatar} alt={testimonial.author} />
                    <AvatarFallback>{testimonial.author[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {testimonial.author}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {testimonial.title}
                    </div>
                    <Badge variant="secondary" className="mt-1">
                      {testimonial.company}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* 信任指标 */}
        <div className="mt-16 text-center">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">1000+</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">企业客户</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600 dark:text-green-400">99.9%</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">系统可用性</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">50M+</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">处理工单数</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">4.9/5</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">客户评分</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
