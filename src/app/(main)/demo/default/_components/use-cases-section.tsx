import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Monitor, Headphones, Building, MapPin } from "lucide-react"

/**
 * 应用场景区域组件
 * 展示不同行业的解决方案
 */
export function UseCasesSection() {
  const useCases = [
    {
      icon: Monitor,
      title: "IT服务管理 (ITSM)",
      description: "实现IT报障、变更请求、资产管理流程的标准化和自动化，提升员工满意度。",
      features: ["IT报障处理", "变更请求管理", "资产生命周期", "知识库管理"],
      color: "bg-blue-500",
      badge: "技术服务"
    },
    {
      icon: Headphones,
      title: "客户支持与售后",
      description: "管理客户投诉、技术支持、退换货和上门维修流程，将每一次服务都变成提升客户忠诚度的机会。",
      features: ["客户投诉处理", "技术支持", "退换货流程", "上门维修"],
      color: "bg-green-500",
      badge: "客户服务"
    },
    {
      icon: Building,
      title: "企业内部协同",
      description: "将采购申请、入职流程、费用报销等内部流程线上化、自动化，提升组织运营效率。",
      features: ["采购申请", "入职流程", "费用报销", "行政审批"],
      color: "bg-purple-500",
      badge: "内部管理"
    },
    {
      icon: MapPin,
      title: "政务与公共事业",
      description: "处理市民热线、公共设施报修、政策咨询等，提升公共服务响应速度和透明度。",
      features: ["市民热线", "设施报修", "政策咨询", "便民服务"],
      color: "bg-orange-500",
      badge: "公共服务"
    }
  ]

  return (
    <section className="py-16 md:py-24 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-6">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            无论您身处哪个行业，我们都有为您量身打造的解决方案
          </h2>
          <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
            从IT服务到客户支持，从内部协同到公共服务，我们的平台适用于各种业务场景
          </p>
        </div>
        
        <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-2">
          {useCases.map((useCase, index) => {
            const IconComponent = useCase.icon
            return (
              <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`rounded-lg ${useCase.color} p-3 text-white`}>
                        <IconComponent className="h-6 w-6" />
                      </div>
                      <div>
                        <Badge variant="secondary" className="mb-2">
                          {useCase.badge}
                        </Badge>
                        <CardTitle className="text-xl text-gray-900 dark:text-white">
                          {useCase.title}
                        </CardTitle>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {useCase.description}
                  </p>
                  
                  <div>
                    <h4 className="mb-3 font-semibold text-gray-900 dark:text-white">
                      核心功能：
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {useCase.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-2">
                          <div className="h-1.5 w-1.5 rounded-full bg-blue-500" />
                          <span className="text-sm text-gray-600 dark:text-gray-300">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
        
        <div className="mt-16 text-center">
          <div className="rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-8">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              没有找到您的行业？
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              我们的平台高度可配置，能够适应各种业务场景。联系我们的专家，为您定制专属解决方案。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Badge variant="outline" className="px-4 py-2">制造业</Badge>
              <Badge variant="outline" className="px-4 py-2">金融服务</Badge>
              <Badge variant="outline" className="px-4 py-2">教育机构</Badge>
              <Badge variant="outline" className="px-4 py-2">医疗健康</Badge>
              <Badge variant="outline" className="px-4 py-2">零售电商</Badge>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
