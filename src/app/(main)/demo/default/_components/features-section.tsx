import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Brain, BarChart3, Settings, <PERSON> } from "lucide-react"

/**
 * 功能亮点区域组件
 * 展示平台的核心功能和优势
 */
export function FeaturesSection() {
  const features = [
    {
      icon: Brain,
      title: "AI副驾驶，赋能一线",
      description: "我们的AI助手嵌入在您员工的每一个操作中。从一键生成工单摘要、专业话术润色，到智能推荐相似解决方案和知识库文章，让每一位员工都拥有「专家级」的辅助。",
      value: "提升沟通质量，缩短解决时长，加速新人成长。",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      icon: BarChart3,
      title: "服务运营驾驶舱",
      description: "将海量运营数据转化为直观、可交互的仪表盘。实时监控工单状态、团队绩效、SLA达成率和客户满意度，一览全局，洞察先机。",
      value: "用数据驱动决策，告别模糊管理，精准定位问题与机会。",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: Settings,
      title: "高度可配置的「万能」平台",
      description: "从自定义字段、工单标签，到复杂的自动化规则和SLA策略，系统的每一个角落几乎都可以根据您的需求进行配置。",
      value: "平台能与您的业务共同成长，无论未来如何变化，系统都能完美适配。",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      icon: Link,
      title: "开放连接，融入生态",
      description: "提供丰富的原生应用集成（如Slack, Jira, Salesforce）和强大的API、Webhook能力，轻松与您现有的CRM、ERP等系统打通，消除数据孤岛。",
      value: "成为您企业数字化生态的核心枢纽，实现端到端的流程自动化。",
      gradient: "from-orange-500 to-red-500"
    }
  ]

  return (
    <section className="py-16 md:py-24 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            不止于强大，更在于智慧
          </h2>
          <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
            每一个功能都经过精心设计，为您的团队带来实实在在的价值提升
          </p>
        </div>
        
        <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-2">
          {features.map((feature, index) => {
            const IconComponent = feature.icon
            return (
              <Card key={index} className="group relative overflow-hidden hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                {/* 背景渐变 */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />
                
                <CardHeader className="relative">
                  <div className="flex items-center space-x-4">
                    <div className={`rounded-lg bg-gradient-to-br ${feature.gradient} p-3 text-white shadow-lg`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <CardTitle className="text-xl text-gray-900 dark:text-white">
                      {feature.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                
                <CardContent className="relative space-y-4">
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>
                  
                  <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4 border-l-4 border-blue-500">
                    <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                      <span className="font-semibold">价值：</span>{feature.value}
                    </p>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
        
        <div className="mt-16 text-center">
          <div className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/30 px-6 py-3">
            <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
              💡 这些功能如何为您的业务带来价值？立即预约演示了解详情
            </span>
          </div>
        </div>
      </div>
    </section>
  )
}
