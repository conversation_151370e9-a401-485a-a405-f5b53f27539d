import { Card, CardContent } from "@/components/ui/card"
import { Clock, Users, Settings, BarChart3, BookOpen } from "lucide-react"

/**
 * 痛点聚焦区域组件
 * 展示客户面临的主要问题和挑战
 */
export function PainPointsSection() {
  const painPoints = [
    {
      icon: Clock,
      title: "客户等待，怨声载道？",
      description: "响应不及时，问题处理周期长，客户满意度持续下滑。",
      color: "text-red-500"
    },
    {
      icon: Users,
      title: "团队协作，壁垒重重？",
      description: "跨部门沟通靠吼，信息反复传递，责任不清，效率低下。",
      color: "text-orange-500"
    },
    {
      icon: Settings,
      title: "流程僵化，难以适应？",
      description: "业务快速变化，现有工具无法灵活配置，处处受限。",
      color: "text-yellow-500"
    },
    {
      icon: BarChart3,
      title: "工作成果，难以衡量？",
      description: "团队表现凭感觉，服务质量和效率无法量化，优化无从下手。",
      color: "text-blue-500"
    },
    {
      icon: BookOpen,
      title: "宝贵经验，付之东流？",
      description: "优秀员工的解决方案无法沉淀，新人成长缓慢，团队整体能力提升难。",
      color: "text-purple-500"
    }
  ]

  return (
    <section className="py-16 md:py-24 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            您的团队是否正在被低效的服务流程所困扰？
          </h2>
          <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
            这些常见的痛点正在拖累您的团队效率和客户满意度
          </p>
        </div>
        
        {/* 前三个卡片 */}
        <div className="mt-16 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {painPoints.slice(0, 3).map((point, index) => {
            const IconComponent = point.icon
            return (
              <Card key={index} className="group hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className={`flex-shrink-0 rounded-lg bg-gray-100 dark:bg-gray-800 p-3 ${point.color}`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {point.title}
                      </h3>
                      <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                        {point.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* 后两个卡片居中显示 */}
        <div className="mt-6 flex justify-center">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 max-w-2xl">
            {painPoints.slice(3, 5).map((point, index) => {
              const IconComponent = point.icon
              return (
                <Card key={index + 3} className="group hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className={`flex-shrink-0 rounded-lg bg-gray-100 dark:bg-gray-800 p-3 ${point.color}`}>
                        <IconComponent className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                          {point.title}
                        </h3>
                        <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                          {point.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
        
        <div className="mt-12 text-center">
          <p className="text-lg font-medium text-gray-700 dark:text-gray-300">
            如果您的答案是"是"，那么是时候做出改变了
          </p>
        </div>
      </div>
    </section>
  )
}
