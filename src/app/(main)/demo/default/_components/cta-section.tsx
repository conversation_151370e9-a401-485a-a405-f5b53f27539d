import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowRight, Calendar, DollarSign, CheckCircle } from "lucide-react"

/**
 * 最终行动号召区域组件
 * 引导用户采取行动
 */
export function CtaSection() {
  const benefits = [
    "30天免费试用",
    "专业技术支持",
    "数据安全保障",
    "灵活定制服务"
  ]

  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white">
      <div className="container mx-auto px-6">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">
            准备好开启您的智能服务运营之旅了吗？
          </h2>
          <p className="mt-6 text-xl text-blue-100">
            加入数千家正在通过我们平台重塑客户体验的领先企业
          </p>
          
          {/* 核心优势 */}
          <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center justify-center space-x-2 text-blue-100">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm">{benefit}</span>
              </div>
            ))}
          </div>
          
          {/* 主要行动号召 */}
          <div className="mt-12 flex flex-col items-center justify-center gap-6 sm:flex-row">
            <Button size="lg" className="group bg-white text-blue-600 hover:bg-gray-100">
              <Calendar className="mr-2 h-5 w-5" />
              预约一对一产品演示
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
            <Button variant="outline" size="lg" className="group border-white text-white hover:bg-white hover:text-blue-600">
              <DollarSign className="mr-2 h-5 w-5" />
              查看定价方案
            </Button>
          </div>
          
          {/* 联系信息卡片 */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-white/10 border-white/20 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <Calendar className="mx-auto h-8 w-8 text-blue-300 mb-4" />
                <h3 className="text-lg font-semibold mb-2">预约演示</h3>
                <p className="text-blue-100 text-sm mb-4">
                  与我们的产品专家进行一对一演示，了解平台如何解决您的具体需求
                </p>
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                  立即预约
                </Button>
              </CardContent>
            </Card>
            
            <Card className="bg-white/10 border-white/20 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <DollarSign className="mx-auto h-8 w-8 text-green-300 mb-4" />
                <h3 className="text-lg font-semibold mb-2">免费试用</h3>
                <p className="text-blue-100 text-sm mb-4">
                  30天完全免费试用，无需信用卡，体验完整功能
                </p>
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                  开始试用
                </Button>
              </CardContent>
            </Card>
          </div>
          
          {/* 底部信息 */}
          <div className="mt-12 text-center">
            <p className="text-blue-200 text-sm">
              🔒 您的数据安全是我们的首要任务 | 📞 7x24小时技术支持 | 🌍 全球部署，就近服务
            </p>
          </div>
        </div>
      </div>
      
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="h-[800px] w-[800px] rounded-full bg-gradient-to-r from-blue-400/20 to-indigo-400/20 blur-3xl" />
        </div>
      </div>
    </section>
  )
}
