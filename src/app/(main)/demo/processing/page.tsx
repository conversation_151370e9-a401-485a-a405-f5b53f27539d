import { ProcessOverview } from "./_components/process-overview"
import { FlowVisualization } from "./_components/flow-visualization"
import { ScenarioTabs } from "./_components/scenario-tabs"
import { ProcessStats } from "./_components/process-stats"
import { RoleMatrix } from "./_components/role-matrix"

/**
 * 运行流程可视化展示页面
 * 展示工单从创建到关闭的完整生命周期和多级流转机制
 */
export default function ProcessingPage() {
  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* 流程概览 */}
      <ProcessOverview />
      
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 mt-6">
        {/* 主要内容区域 */}
        <div className="xl:col-span-3 space-y-6">
          {/* 流程可视化图 */}
          <FlowVisualization />
          
          {/* 场景示例标签页 */}
          <ScenarioTabs />
        </div>
        
        {/* 侧边栏 */}
        <div className="xl:col-span-1 space-y-6">
          {/* 流程统计 */}
          <ProcessStats />
          
          {/* 角色权限矩阵 */}
          <RoleMatrix />
        </div>
      </div>
    </div>
  )
}
