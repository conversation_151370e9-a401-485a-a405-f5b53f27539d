"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Play, 
  Pause, 
  RotateCcw, 
  ArrowRight, 
  ArrowDown,
  ArrowUp,
  ArrowLeft,
  Users,
  FileText,
  CheckCircle,
  Clock,
  AlertTriangle,
  UserCheck,
  Settings
} from "lucide-react"

/**
 * 流程可视化图组件
 * 动态展示工单流转的完整路径和状态变化
 */
export function FlowVisualization() {
  const [isAnimating, setIsAnimating] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [selectedFlow, setSelectedFlow] = useState<"standard" | "multilevel" | "collaborative">("standard")

  // 标准流程步骤
  const standardFlow = [
    { id: "create", title: "创建工单", role: "客服", status: "待处理", icon: FileText, color: "bg-blue-500" },
    { id: "assign", title: "分派处理", role: "客服", status: "已分派", icon: UserCheck, color: "bg-blue-600" },
    { id: "process", title: "处理中", role: "处理人", status: "处理中", icon: Settings, color: "bg-orange-500" },
    { id: "complete", title: "办结", role: "处理人", status: "待回访", icon: CheckCircle, color: "bg-green-500" },
    { id: "review", title: "回访", role: "回访员", status: "回访中", icon: Users, color: "bg-purple-500" },
    { id: "close", title: "关闭", role: "回访员", status: "已关闭", icon: CheckCircle, color: "bg-green-600" }
  ]

  // 多级流转流程
  const multilevelFlow = [
    { id: "create", title: "创建工单", role: "客服", status: "待处理", icon: FileText, color: "bg-blue-500" },
    { id: "l1", title: "一线处理", role: "L1支持", status: "L1处理中", icon: Settings, color: "bg-orange-400" },
    { id: "escalate1", title: "升级L2", role: "L1支持", status: "待L2处理", icon: ArrowUp, color: "bg-yellow-500" },
    { id: "l2", title: "二线处理", role: "L2工程师", status: "L2处理中", icon: Settings, color: "bg-orange-500" },
    { id: "escalate2", title: "升级L3", role: "L2工程师", status: "待L3处理", icon: ArrowUp, color: "bg-red-500" },
    { id: "l3", title: "研发处理", role: "L3研发", status: "L3处理中", icon: Settings, color: "bg-red-600" },
    { id: "complete", title: "办结", role: "L3研发", status: "待回访", icon: CheckCircle, color: "bg-green-500" },
    { id: "close", title: "关闭", role: "回访员", status: "已关闭", icon: CheckCircle, color: "bg-green-600" }
  ]

  // 协同流程
  const collaborativeFlow = [
    { id: "create", title: "创建工单", role: "客服", status: "待处理", icon: FileText, color: "bg-blue-500" },
    { id: "assign", title: "多方分派", role: "客服", status: "已分派", icon: Users, color: "bg-blue-600" },
    { id: "main", title: "主办处理", role: "主办方", status: "主办处理中", icon: Settings, color: "bg-orange-500" },
    { id: "collab1", title: "协办1", role: "技术部", status: "协办中", icon: Settings, color: "bg-purple-400" },
    { id: "collab2", title: "协办2", role: "商务部", status: "协办中", icon: Settings, color: "bg-purple-500" },
    { id: "merge", title: "汇总方案", role: "主办方", status: "方案汇总", icon: Users, color: "bg-green-400" },
    { id: "complete", title: "办结", role: "主办方", status: "待回访", icon: CheckCircle, color: "bg-green-500" },
    { id: "close", title: "关闭", role: "回访员", status: "已关闭", icon: CheckCircle, color: "bg-green-600" }
  ]

  const flows = {
    standard: standardFlow,
    multilevel: multilevelFlow,
    collaborative: collaborativeFlow
  }

  const currentFlow = flows[selectedFlow]

  const startAnimation = () => {
    setIsAnimating(true)
    setCurrentStep(0)
    
    const interval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev >= currentFlow.length - 1) {
          setIsAnimating(false)
          clearInterval(interval)
          return prev
        }
        return prev + 1
      })
    }, 1500)
  }

  const resetAnimation = () => {
    setIsAnimating(false)
    setCurrentStep(0)
  }

  const getStepStatus = (index: number) => {
    if (index < currentStep) return "completed"
    if (index === currentStep && isAnimating) return "active"
    return "pending"
  }

  const renderFlowStep = (step: any, index: number) => {
    const IconComponent = step.icon
    const status = getStepStatus(index)
    
    return (
      <div key={step.id} className="flex flex-col items-center space-y-2">
        {/* 步骤节点 */}
        <div className={`relative w-16 h-16 rounded-full flex items-center justify-center transition-all duration-500 ${
          status === "completed" ? step.color + " scale-110" :
          status === "active" ? step.color + " scale-125 animate-pulse" :
          "bg-gray-300 dark:bg-gray-600"
        }`}>
          <IconComponent className={`h-8 w-8 ${
            status === "pending" ? "text-gray-500" : "text-white"
          }`} />
          
          {/* 活动指示器 */}
          {status === "active" && (
            <div className="absolute -inset-2 rounded-full border-4 border-blue-400 animate-ping"></div>
          )}
        </div>
        
        {/* 步骤信息 */}
        <div className="text-center">
          <p className={`text-sm font-medium ${
            status === "pending" ? "text-gray-500" : "text-gray-900 dark:text-white"
          }`}>
            {step.title}
          </p>
          <p className="text-xs text-muted-foreground">{step.role}</p>
          <Badge 
            variant={status === "pending" ? "secondary" : "default"} 
            className="text-xs mt-1"
          >
            {step.status}
          </Badge>
        </div>
        
        {/* 连接线 */}
        {index < currentFlow.length - 1 && (
          <div className="flex items-center justify-center mt-4">
            {selectedFlow === "collaborative" && (index === 2 || index === 3) ? (
              <ArrowDown className={`h-6 w-6 transition-colors duration-500 ${
                index < currentStep ? "text-green-500" : "text-gray-400"
              }`} />
            ) : (
              <ArrowRight className={`h-6 w-6 transition-colors duration-500 ${
                index < currentStep ? "text-green-500" : "text-gray-400"
              }`} />
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>流程可视化</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={isAnimating ? "destructive" : "default"}
              size="sm"
              onClick={isAnimating ? resetAnimation : startAnimation}
            >
              {isAnimating ? (
                <>
                  <Pause className="h-4 w-4 mr-2" />
                  停止
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  演示
                </>
              )}
            </Button>
            
            <Button variant="outline" size="sm" onClick={resetAnimation}>
              <RotateCcw className="h-4 w-4 mr-2" />
              重置
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 流程类型选择 */}
        <div className="flex justify-center space-x-4">
          <Button
            variant={selectedFlow === "standard" ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setSelectedFlow("standard")
              resetAnimation()
            }}
          >
            标准流程
          </Button>
          <Button
            variant={selectedFlow === "multilevel" ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setSelectedFlow("multilevel")
              resetAnimation()
            }}
          >
            多级流转
          </Button>
          <Button
            variant={selectedFlow === "collaborative" ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setSelectedFlow("collaborative")
              resetAnimation()
            }}
          >
            协同流程
          </Button>
        </div>

        {/* 流程图 */}
        <div className="overflow-x-auto">
          <div className={`flex items-start space-x-8 p-6 min-w-max ${
            selectedFlow === "collaborative" ? "flex-wrap justify-center" : ""
          }`}>
            {selectedFlow === "collaborative" ? (
              // 协同流程特殊布局
              <div className="space-y-8">
                {/* 前两步 */}
                <div className="flex items-center space-x-8">
                  {currentFlow.slice(0, 2).map((step, index) => renderFlowStep(step, index))}
                </div>
                
                {/* 并行协同步骤 */}
                <div className="flex justify-center">
                  <div className="grid grid-cols-3 gap-8 items-center">
                    {renderFlowStep(currentFlow[2], 2)}
                    <div className="flex flex-col space-y-4">
                      {renderFlowStep(currentFlow[3], 3)}
                      {renderFlowStep(currentFlow[4], 4)}
                    </div>
                    {renderFlowStep(currentFlow[5], 5)}
                  </div>
                </div>
                
                {/* 后两步 */}
                <div className="flex items-center justify-center space-x-8">
                  {currentFlow.slice(6).map((step, index) => renderFlowStep(step, index + 6))}
                </div>
              </div>
            ) : (
              // 标准和多级流程线性布局
              currentFlow.map((step, index) => renderFlowStep(step, index))
            )}
          </div>
        </div>

        {/* 流程说明 */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            {selectedFlow === "standard" && "标准流程说明"}
            {selectedFlow === "multilevel" && "多级流转说明"}
            {selectedFlow === "collaborative" && "协同流程说明"}
          </h4>
          <p className="text-sm text-muted-foreground">
            {selectedFlow === "standard" && "工单按照标准路径从创建、分派、处理到最终关闭的线性流程。"}
            {selectedFlow === "multilevel" && "技术问题逐级升级，从一线支持到二线工程师再到研发团队的多层处理机制。"}
            {selectedFlow === "collaborative" && "复杂问题需要多部门协同处理，主办方统筹协调，协办方并行工作的协作模式。"}
          </p>
        </div>

        {/* 当前步骤详情 */}
        {isAnimating && currentStep < currentFlow.length && (
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800 dark:text-blue-200">
                当前步骤：{currentFlow[currentStep].title}
              </span>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              负责角色：{currentFlow[currentStep].role} | 
              状态：{currentFlow[currentStep].status}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
