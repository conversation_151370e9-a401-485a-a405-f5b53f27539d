"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Monitor, 
  ShoppingCart, 
  MessageSquare, 
  ArrowRight,
  ArrowUp,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle
} from "lucide-react"

/**
 * 场景示例标签页组件
 * 展示不同类型的工单流转场景和处理过程
 */
export function ScenarioTabs() {
  // IT技术支持逐级上报场景
  const itScenario = {
    title: "IT技术支持逐级上报",
    description: "客户无法上网问题的多级技术升级处理",
    timeline: [
      {
        step: 1,
        role: "客服",
        action: "创建工单",
        description: "客户报告无法上网，客服创建工单并分派给L1技术支持",
        status: "completed",
        time: "09:30"
      },
      {
        step: 2,
        role: "L1技术支持",
        action: "初步排查",
        description: "执行标准排查步骤：重启路由器、检查网络设置",
        status: "completed",
        time: "09:45"
      },
      {
        step: 3,
        role: "L1技术支持",
        action: "升级L2",
        description: "发现是服务器端问题，超出权限范围，转办给L2网络工程师",
        status: "completed",
        time: "10:15"
      },
      {
        step: 4,
        role: "L2网络工程师",
        action: "深度排查",
        description: "基于L1记录继续排查，发现核心网络设备故障",
        status: "completed",
        time: "10:30"
      },
      {
        step: 5,
        role: "L2网络工程师",
        action: "升级L3",
        description: "需要研发资源修复产品BUG，上报给核心研发团队",
        status: "in-progress",
        time: "11:00"
      },
      {
        step: 6,
        role: "L3研发团队",
        action: "根本原因分析",
        description: "进行代码修复和系统优化",
        status: "pending",
        time: "待处理"
      }
    ]
  }

  // 采购申请多级审批场景
  const purchaseScenario = {
    title: "采购申请多级审批",
    description: "部门员工申请采购笔记本电脑的审批流程",
    timeline: [
      {
        step: 1,
        role: "申请人",
        action: "提交申请",
        description: "员工提交采购笔记本电脑申请，预算15000元",
        status: "completed",
        time: "14:00"
      },
      {
        step: 2,
        role: "部门主管",
        action: "部门审批",
        description: "审核申请必要性，确认部门预算充足",
        status: "completed",
        time: "14:30"
      },
      {
        step: 3,
        role: "采购部门",
        action: "选型报价",
        description: "进行设备选型和供应商报价比较",
        status: "completed",
        time: "15:00"
      },
      {
        step: 4,
        role: "财务部门",
        action: "财务审批",
        description: "审核预算和报价，确认采购合规性",
        status: "in-progress",
        time: "15:30"
      },
      {
        step: 5,
        role: "采购部门",
        action: "执行采购",
        description: "完成设备采购和交付",
        status: "pending",
        time: "待处理"
      }
    ]
  }

  // VIP客户投诉协同处理场景
  const vipScenario = {
    title: "VIP客户投诉协同处理",
    description: "软件崩溃和合同计费问题的跨部门协同解决",
    participants: [
      { role: "主办方", department: "客户成功部", avatar: "/avatars/01.png" },
      { role: "协办方1", department: "技术支持部", avatar: "/avatars/02.png" },
      { role: "协办方2", department: "商务部", avatar: "/avatars/03.png" }
    ],
    timeline: [
      {
        step: 1,
        role: "客服",
        action: "创建协同工单",
        description: "VIP客户投诉软件崩溃和计费问题，设置多方协同",
        status: "completed",
        time: "10:00"
      },
      {
        step: 2,
        role: "客户成功部",
        action: "客户安抚",
        description: "立即联系客户安抚，告知专门小组已成立",
        status: "completed",
        time: "10:15"
      },
      {
        step: 3,
        role: "技术支持部",
        action: "技术排查",
        description: "并行排查软件崩溃原因，分析日志和错误信息",
        status: "completed",
        time: "10:30"
      },
      {
        step: 4,
        role: "商务部",
        action: "合同核查",
        description: "并行审查合同条款与销售沟通记录",
        status: "completed",
        time: "10:30"
      },
      {
        step: 5,
        role: "客户成功部",
        action: "方案汇总",
        description: "汇总技术和商务解决方案，形成完整答复",
        status: "in-progress",
        time: "11:00"
      }
    ]
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed": return <CheckCircle className="h-4 w-4 text-green-500" />
      case "in-progress": return <Clock className="h-4 w-4 text-blue-500 animate-spin" />
      case "pending": return <AlertTriangle className="h-4 w-4 text-gray-400" />
      default: return null
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "border-green-200 bg-green-50 dark:bg-green-900/20"
      case "in-progress": return "border-blue-200 bg-blue-50 dark:bg-blue-900/20"
      case "pending": return "border-gray-200 bg-gray-50 dark:bg-gray-800"
      default: return "border-gray-200"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MessageSquare className="h-5 w-5" />
          <span>典型场景示例</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="it-support" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="it-support" className="flex items-center space-x-2">
              <Monitor className="h-4 w-4" />
              <span>技术支持</span>
            </TabsTrigger>
            <TabsTrigger value="purchase" className="flex items-center space-x-2">
              <ShoppingCart className="h-4 w-4" />
              <span>采购审批</span>
            </TabsTrigger>
            <TabsTrigger value="vip-complaint" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>VIP投诉</span>
            </TabsTrigger>
          </TabsList>

          {/* IT技术支持场景 */}
          <TabsContent value="it-support" className="space-y-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                {itScenario.title}
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {itScenario.description}
              </p>
            </div>
            
            <div className="space-y-4">
              {itScenario.timeline.map((item, index) => (
                <div key={index} className={`relative p-4 rounded-lg border ${getStatusColor(item.status)}`}>
                  {/* 连接线 */}
                  {index < itScenario.timeline.length - 1 && (
                    <div className="absolute left-6 top-16 w-0.5 h-8 bg-gray-300"></div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 flex items-center space-x-2">
                      {getStatusIcon(item.status)}
                      <Badge variant="outline" className="text-xs">
                        步骤{item.step}
                      </Badge>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-sm">{item.role}</span>
                          <ArrowRight className="h-3 w-3 text-gray-400" />
                          <span className="text-sm text-gray-600 dark:text-gray-300">{item.action}</span>
                        </div>
                        <span className="text-xs text-muted-foreground">{item.time}</span>
                      </div>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          {/* 采购审批场景 */}
          <TabsContent value="purchase" className="space-y-4">
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                {purchaseScenario.title}
              </h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                {purchaseScenario.description}
              </p>
            </div>
            
            <div className="space-y-4">
              {purchaseScenario.timeline.map((item, index) => (
                <div key={index} className={`relative p-4 rounded-lg border ${getStatusColor(item.status)}`}>
                  {index < purchaseScenario.timeline.length - 1 && (
                    <div className="absolute left-6 top-16 w-0.5 h-8 bg-gray-300"></div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 flex items-center space-x-2">
                      {getStatusIcon(item.status)}
                      <Badge variant="outline" className="text-xs">
                        步骤{item.step}
                      </Badge>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-sm">{item.role}</span>
                          <ArrowRight className="h-3 w-3 text-gray-400" />
                          <span className="text-sm text-gray-600 dark:text-gray-300">{item.action}</span>
                        </div>
                        <span className="text-xs text-muted-foreground">{item.time}</span>
                      </div>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          {/* VIP客户投诉场景 */}
          <TabsContent value="vip-complaint" className="space-y-4">
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <h3 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">
                {vipScenario.title}
              </h3>
              <p className="text-sm text-purple-700 dark:text-purple-300 mb-3">
                {vipScenario.description}
              </p>
              
              {/* 参与方展示 */}
              <div className="flex items-center space-x-4">
                <span className="text-xs text-purple-600 dark:text-purple-300">参与方：</span>
                {vipScenario.participants.map((participant, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={participant.avatar} alt={participant.department} />
                      <AvatarFallback>{participant.department[0]}</AvatarFallback>
                    </Avatar>
                    <div className="text-xs">
                      <p className="font-medium text-purple-800 dark:text-purple-200">
                        {participant.department}
                      </p>
                      <p className="text-purple-600 dark:text-purple-300">
                        {participant.role}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="space-y-4">
              {vipScenario.timeline.map((item, index) => (
                <div key={index} className={`relative p-4 rounded-lg border ${getStatusColor(item.status)}`}>
                  {index < vipScenario.timeline.length - 1 && (
                    <div className="absolute left-6 top-16 w-0.5 h-8 bg-gray-300"></div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 flex items-center space-x-2">
                      {getStatusIcon(item.status)}
                      <Badge variant="outline" className="text-xs">
                        步骤{item.step}
                      </Badge>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-sm">{item.role}</span>
                          <ArrowRight className="h-3 w-3 text-gray-400" />
                          <span className="text-sm text-gray-600 dark:text-gray-300">{item.action}</span>
                        </div>
                        <span className="text-xs text-muted-foreground">{item.time}</span>
                      </div>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
