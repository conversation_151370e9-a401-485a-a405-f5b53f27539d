import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart3, 
  TrendingUp, 
  Clock, 
  Users,
  ArrowUp,
  ArrowDown,
  CheckCircle,
  AlertTriangle
} from "lucide-react"

/**
 * 流程统计组件
 * 展示工单流转的关键指标和统计数据
 */
export function ProcessStats() {
  const stats = {
    totalTickets: 1247,
    completionRate: 94.2,
    avgProcessTime: "4.2小时",
    escalationRate: 12.5,
    satisfactionScore: 4.6,
    slaCompliance: 89.3
  }

  const stageDistribution = [
    { stage: "创建与分派", count: 45, percentage: 15, color: "bg-blue-500" },
    { stage: "处理中", count: 156, percentage: 52, color: "bg-orange-500" },
    { stage: "待回访", count: 67, percentage: 22, color: "bg-purple-500" },
    { stage: "已关闭", count: 33, percentage: 11, color: "bg-green-500" }
  ]

  const escalationData = [
    { level: "L1处理", count: 875, percentage: 87.5, success: true },
    { level: "L2升级", count: 98, percentage: 9.8, success: true },
    { level: "L3升级", count: 27, percentage: 2.7, success: false }
  ]

  const trends = [
    { metric: "处理效率", value: "+8.3%", trend: "up", color: "text-green-600" },
    { metric: "升级率", value: "-2.1%", trend: "down", color: "text-green-600" },
    { metric: "满意度", value: "+0.4", trend: "up", color: "text-green-600" },
    { metric: "SLA达成", value: "-1.2%", trend: "down", color: "text-red-600" }
  ]

  return (
    <div className="space-y-6">
      {/* 核心指标 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg">
            <BarChart3 className="h-5 w-5" />
            <span>流程统计</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 总体指标 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-800 dark:text-blue-200">总工单数</p>
              <p className="text-2xl font-bold text-blue-600">{stats.totalTickets}</p>
            </div>
            
            <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-sm text-green-800 dark:text-green-200">完成率</p>
              <p className="text-2xl font-bold text-green-600">{stats.completionRate}%</p>
            </div>
          </div>

          {/* 关键指标 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">平均处理时间</span>
              </div>
              <span className="font-medium">{stats.avgProcessTime}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <ArrowUp className="h-4 w-4 text-orange-500" />
                <span className="text-sm">升级率</span>
              </div>
              <span className="font-medium">{stats.escalationRate}%</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-purple-500" />
                <span className="text-sm">满意度评分</span>
              </div>
              <span className="font-medium">{stats.satisfactionScore}/5.0</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">SLA达成率</span>
              </div>
              <span className="font-medium">{stats.slaCompliance}%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 阶段分布 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">阶段分布</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {stageDistribution.map((stage, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>{stage.stage}</span>
                <span className="font-medium">{stage.count}个</span>
              </div>
              <div className="flex items-center space-x-2">
                <Progress value={stage.percentage} className="flex-1 h-2" />
                <span className="text-xs text-muted-foreground w-12">
                  {stage.percentage}%
                </span>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 升级统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">升级统计</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-3">
          {escalationData.map((item, index) => (
            <div key={index} className="flex items-center justify-between p-2 border rounded">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${
                  item.success ? 'bg-green-500' : 'bg-orange-500'
                }`}></div>
                <span className="text-sm font-medium">{item.level}</span>
              </div>
              <div className="text-right">
                <p className="text-sm font-bold">{item.count}</p>
                <p className="text-xs text-muted-foreground">{item.percentage}%</p>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 趋势分析 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg">
            <TrendingUp className="h-5 w-5" />
            <span>趋势分析</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-3">
          {trends.map((trend, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="text-sm">{trend.metric}</span>
              <div className="flex items-center space-x-1">
                {trend.trend === "up" ? (
                  <ArrowUp className={`h-3 w-3 ${trend.color}`} />
                ) : (
                  <ArrowDown className={`h-3 w-3 ${trend.color}`} />
                )}
                <span className={`text-sm font-medium ${trend.color}`}>
                  {trend.value}
                </span>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 质量指标 */}
      <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
        <CardContent className="p-4">
          <div className="flex items-center space-x-2 mb-3">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span className="font-medium text-green-800 dark:text-green-200">
              质量保障
            </span>
          </div>
          <div className="space-y-2 text-sm text-green-700 dark:text-green-300">
            <div className="flex justify-between">
              <span>一次解决率</span>
              <span className="font-medium">87.5%</span>
            </div>
            <div className="flex justify-between">
              <span>重启率</span>
              <span className="font-medium">3.2%</span>
            </div>
            <div className="flex justify-between">
              <span>客户投诉率</span>
              <span className="font-medium">1.8%</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
