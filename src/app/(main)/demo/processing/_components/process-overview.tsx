import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  FileText, 
  Users, 
  CheckCircle, 
  RotateCcw,
  ArrowRight,
  Clock,
  AlertTriangle,
  Target
} from "lucide-react"

/**
 * 流程概览组件
 * 展示工单生命周期的三个主要阶段和关键特性
 */
export function ProcessOverview() {
  const processStages = [
    {
      id: "creation",
      title: "创建与分派",
      description: "服务请求的入口",
      icon: FileText,
      color: "bg-blue-500",
      features: [
        "客服接收需求",
        "信息录入与标签",
        "智能分派推荐",
        "工单预约服务"
      ],
      roles: ["客服人员", "系统自动化"]
    },
    {
      id: "processing",
      title: "处理与协作",
      description: "核心问题解决阶段",
      icon: Users,
      color: "bg-orange-500",
      features: [
        "多级流转处理",
        "跨部门协作",
        "实时补记记录",
        "转办与上报"
      ],
      roles: ["处理人", "协办人", "管理者"]
    },
    {
      id: "closure",
      title: "质检与关闭",
      description: "服务质量闭环",
      icon: CheckCircle,
      color: "bg-green-500",
      features: [
        "客户满意度回访",
        "服务质量评估",
        "工单最终关闭",
        "问题重启机制"
      ],
      roles: ["回访员", "客户"]
    }
  ]

  const specialFeatures = [
    {
      icon: RotateCcw,
      title: "逆向流程",
      description: "退回、撤回、重启",
      color: "text-purple-600"
    },
    {
      icon: Clock,
      title: "审批流程",
      description: "延期、挂起、申诉",
      color: "text-blue-600"
    },
    {
      icon: AlertTriangle,
      title: "监控预警",
      description: "超时提醒、SLA监控",
      color: "text-red-600"
    },
    {
      icon: Target,
      title: "多级流转",
      description: "L1→L2→L3逐级上报",
      color: "text-green-600"
    }
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
          工单运行流程可视化
        </h1>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          展示工单从诞生到终结的完整生命周期旅程，包含多级流转、跨部门协作和特殊流程处理机制
        </p>
      </div>

      {/* 主要流程阶段 */}
      <Card>
        <CardContent className="p-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {processStages.map((stage, index) => {
              const IconComponent = stage.icon
              return (
                <div key={stage.id} className="relative">
                  {/* 连接线 */}
                  {index < processStages.length - 1 && (
                    <div className="hidden lg:block absolute top-16 -right-4 z-10">
                      <ArrowRight className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                  
                  <div className="text-center space-y-4">
                    {/* 阶段图标 */}
                    <div className={`mx-auto w-16 h-16 rounded-full ${stage.color} flex items-center justify-center`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    
                    {/* 阶段信息 */}
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {stage.title}
                      </h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {stage.description}
                      </p>
                    </div>
                    
                    {/* 关键特性 */}
                    <div className="space-y-2">
                      {stage.features.map((feature, idx) => (
                        <div key={idx} className="text-sm text-gray-600 dark:text-gray-300">
                          • {feature}
                        </div>
                      ))}
                    </div>
                    
                    {/* 参与角色 */}
                    <div className="flex flex-wrap justify-center gap-1">
                      {stage.roles.map((role, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {role}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 特殊流程与并行操作 */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white text-center">
              特殊流程与并行操作
            </h3>
            <p className="text-sm text-muted-foreground text-center">
              贯穿整个工单生命周期的灵活处理机制
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
              {specialFeatures.map((feature, index) => {
                const IconComponent = feature.icon
                return (
                  <div key={index} className="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
                    <IconComponent className={`h-8 w-8 mx-auto mb-3 ${feature.color}`} />
                    <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                      {feature.title}
                    </h4>
                    <p className="text-xs text-muted-foreground">
                      {feature.description}
                    </p>
                  </div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 流程特点说明 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
          <CardContent className="p-6">
            <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-3">
              多级流转机制
            </h4>
            <ul className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
              <li>• 支持L1→L2→L3逐级技术升级</li>
              <li>• 跨部门协同处理复杂问题</li>
              <li>• 主协办角色清晰分工</li>
              <li>• 完整的流转轨迹记录</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
          <CardContent className="p-6">
            <h4 className="font-semibold text-green-800 dark:text-green-200 mb-3">
              质量保障体系
            </h4>
            <ul className="space-y-2 text-sm text-green-700 dark:text-green-300">
              <li>• 独立回访员质检机制</li>
              <li>• 客户满意度闭环管理</li>
              <li>• SLA监控与预警系统</li>
              <li>• 问题重启与持续改进</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
