import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  CheckCircle, 
  X, 
  Minus,
  Shield,
  User,
  UserCheck,
  Settings,
  Eye
} from "lucide-react"

/**
 * 角色权限矩阵组件
 * 展示不同角色在工单流程中的权限和操作能力
 */
export function RoleMatrix() {
  const roles = [
    { id: "customer-service", name: "客服", icon: User, color: "bg-blue-500" },
    { id: "processor", name: "处理人", icon: UserCheck, color: "bg-orange-500" },
    { id: "collaborator", name: "协办人", icon: Users, color: "bg-purple-500" },
    { id: "reviewer", name: "回访员", icon: Eye, color: "bg-green-500" },
    { id: "manager", name: "管理者", icon: Shield, color: "bg-red-500" }
  ]

  const operations = [
    { id: "create", name: "创建工单", category: "基础操作" },
    { id: "assign", name: "分派工单", category: "基础操作" },
    { id: "receive", name: "接收工单", category: "基础操作" },
    { id: "process", name: "处理工单", category: "基础操作" },
    { id: "comment", name: "工单补记", category: "基础操作" },
    { id: "complete", name: "办结工单", category: "基础操作" },
    { id: "review", name: "执行回访", category: "基础操作" },
    { id: "close", name: "关闭工单", category: "基础操作" },
    { id: "transfer", name: "转办工单", category: "流转操作" },
    { id: "escalate", name: "上报升级", category: "流转操作" },
    { id: "return", name: "退回工单", category: "流转操作" },
    { id: "withdraw", name: "撤回操作", category: "流转操作" },
    { id: "restart", name: "重启工单", category: "流转操作" },
    { id: "merge", name: "工单合并", category: "流转操作" },
    { id: "invite", name: "邀请协办", category: "协作操作" },
    { id: "collaborate", name: "协办处理", category: "协作操作" },
    { id: "approve", name: "审批操作", category: "管理操作" },
    { id: "monitor", name: "监控预警", category: "管理操作" },
    { id: "report", name: "查看报表", category: "管理操作" },
    { id: "batch", name: "批量处理", category: "管理操作" }
  ]

  // 权限矩阵：角色 -> 操作 -> 权限级别
  const permissions = {
    "customer-service": {
      create: "full", assign: "full", receive: "none", process: "none", 
      comment: "limited", complete: "none", review: "none", close: "none",
      transfer: "limited", escalate: "none", return: "receive", withdraw: "own",
      restart: "limited", merge: "full", invite: "limited", collaborate: "none",
      approve: "none", monitor: "limited", report: "limited", batch: "none"
    },
    "processor": {
      create: "none", assign: "none", receive: "full", process: "full",
      comment: "full", complete: "full", review: "limited", close: "none",
      transfer: "full", escalate: "full", return: "full", withdraw: "own",
      restart: "none", merge: "none", invite: "full", collaborate: "none",
      approve: "request", monitor: "own", report: "own", batch: "none"
    },
    "collaborator": {
      create: "none", assign: "none", receive: "full", process: "limited",
      comment: "full", complete: "limited", review: "none", close: "none",
      transfer: "none", escalate: "none", return: "full", withdraw: "own",
      restart: "none", merge: "none", invite: "none", collaborate: "full",
      approve: "none", monitor: "own", report: "own", batch: "none"
    },
    "reviewer": {
      create: "none", assign: "none", receive: "full", process: "none",
      comment: "full", complete: "none", review: "full", close: "full",
      transfer: "none", escalate: "none", return: "none", withdraw: "none",
      restart: "full", merge: "none", invite: "none", collaborate: "none",
      approve: "none", monitor: "limited", report: "limited", batch: "none"
    },
    "manager": {
      create: "full", assign: "full", receive: "full", process: "full",
      comment: "full", complete: "full", review: "full", close: "full",
      transfer: "full", escalate: "full", return: "full", withdraw: "any",
      restart: "full", merge: "full", invite: "full", collaborate: "full",
      approve: "full", monitor: "full", report: "full", batch: "full"
    }
  }

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case "full": return <CheckCircle className="h-4 w-4 text-green-500" />
      case "limited": return <Minus className="h-4 w-4 text-yellow-500" />
      case "own": return <Eye className="h-4 w-4 text-blue-500" />
      case "receive": return <User className="h-4 w-4 text-purple-500" />
      case "request": return <Settings className="h-4 w-4 text-orange-500" />
      case "any": return <Shield className="h-4 w-4 text-red-500" />
      case "none": return <X className="h-4 w-4 text-gray-400" />
      default: return <X className="h-4 w-4 text-gray-400" />
    }
  }

  const getPermissionText = (permission: string) => {
    switch (permission) {
      case "full": return "完全权限"
      case "limited": return "受限权限"
      case "own": return "仅自己的"
      case "receive": return "仅接收"
      case "request": return "申请权限"
      case "any": return "任意操作"
      case "none": return "无权限"
      default: return "无权限"
    }
  }

  const categories = [...new Set(operations.map(op => op.category))]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-lg">
          <Users className="h-5 w-5" />
          <span>角色权限</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 角色说明 */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-muted-foreground">系统角色</h4>
          <div className="grid grid-cols-1 gap-2">
            {roles.map((role) => {
              const IconComponent = role.icon
              return (
                <div key={role.id} className="flex items-center space-x-2 p-2 border rounded">
                  <div className={`w-6 h-6 rounded ${role.color} flex items-center justify-center`}>
                    <IconComponent className="h-3 w-3 text-white" />
                  </div>
                  <span className="text-sm font-medium">{role.name}</span>
                </div>
              )
            })}
          </div>
        </div>

        {/* 权限说明 */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-muted-foreground">权限级别</h4>
          <div className="grid grid-cols-1 gap-1 text-xs">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>完全权限</span>
            </div>
            <div className="flex items-center space-x-2">
              <Minus className="h-3 w-3 text-yellow-500" />
              <span>受限权限</span>
            </div>
            <div className="flex items-center space-x-2">
              <Eye className="h-3 w-3 text-blue-500" />
              <span>仅自己的</span>
            </div>
            <div className="flex items-center space-x-2">
              <X className="h-3 w-3 text-gray-400" />
              <span>无权限</span>
            </div>
          </div>
        </div>

        {/* 权限矩阵 */}
        <div className="space-y-4">
          {categories.map((category) => (
            <div key={category} className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {category}
              </h4>
              <div className="space-y-1">
                {operations
                  .filter(op => op.category === category)
                  .map((operation) => (
                    <div key={operation.id} className="bg-gray-50 dark:bg-gray-800 rounded p-2">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">{operation.name}</span>
                      </div>
                      <div className="grid grid-cols-5 gap-1">
                        {roles.map((role) => {
                          const permission = permissions[role.id][operation.id]
                          return (
                            <div key={role.id} className="text-center">
                              <div className="flex justify-center mb-1">
                                {getPermissionIcon(permission)}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {role.name}
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>

        {/* 特殊权限说明 */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            权限说明
          </h4>
          <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
            <li>• 管理者拥有所有操作的完全权限</li>
            <li>• 处理人可以申请延期、挂起等特殊操作</li>
            <li>• 协办人只能处理被邀请的协办任务</li>
            <li>• 回访员负责质检和最终关闭工单</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
