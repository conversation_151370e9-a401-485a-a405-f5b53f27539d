import { WorkOrderHeader } from "./_components/work-order-header"
import { TimelineSection } from "./_components/timeline-section"
import { ActionInputBox } from "./_components/action-input-box"
import { SidePanel } from "./_components/side-panel"

/**
 * 工单详情页面
 * 提供工单的360度全景视图，包含完整的交互历史和操作入口
 */
export default function WorkOrderDetailPage() {
  // 模拟工单数据
  const workOrderData = {
    id: "WO-2024-001234",
    title: "销售部打印机无法连接网络",
    status: "处理中",
    priority: "高",
    type: "IT服务请求",
    createdAt: "2024-01-15 09:30:00",
    createdBy: "张三",
    assignedTo: "李四",
    slaDeadline: "2024-01-15 17:30:00",
    tags: ["VIP客户", "紧急", "网络问题"],
    customer: {
      name: "王总",
      company: "科技有限公司",
      phone: "138****1234",
      email: "<EMAIL>",
      level: "VIP"
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* 工单概览与核心状态区 */}
      <WorkOrderHeader workOrder={workOrderData} />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        {/* 主要内容区域 - 时间轴和操作 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 操作输入区 */}
          <ActionInputBox workOrderId={workOrderData.id} />
          
          {/* 时间轴历史记录 */}
          <TimelineSection workOrderId={workOrderData.id} />
        </div>
        
        {/* 侧边栏 - 工单属性和关联信息 */}
        <div className="lg:col-span-1">
          <SidePanel workOrder={workOrderData} />
        </div>
      </div>
    </div>
  )
}
