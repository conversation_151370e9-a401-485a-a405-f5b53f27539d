"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  MessageSquare, 
  UserPlus, 
  Users, 
  CheckCircle, 
  Upload, 
  Send, 
  Sparkles,
  Brain
} from "lucide-react"

interface ActionInputBoxProps {
  workOrderId: string
}

/**
 * 操作输入框组件
 * 多功能输入区域，支持补记、指派、协办、办结等操作
 */
export function ActionInputBox({ workOrderId }: ActionInputBoxProps) {
  const [activeTab, setActiveTab] = useState("comment")
  const [comment, setComment] = useState("")
  const [assignTo, setAssignTo] = useState("")
  const [solution, setSolution] = useState("")

  const handleSubmitComment = () => {
    console.log("提交补记:", comment)
    setComment("")
  }

  const handleAssign = () => {
    console.log("指派给:", assignTo)
    setAssignTo("")
  }

  const handleComplete = () => {
    console.log("办结工单:", solution)
    setSolution("")
  }

  const generateAISummary = () => {
    // 模拟AI生成摘要
    const aiSummary = "经过排查，确认是网络配置问题。已重新配置IP地址和DNS设置，打印机现已正常连接网络。建议客户定期检查网络设置，如有问题及时联系技术支持。"
    setSolution(aiSummary)
  }

  const availableUsers = [
    { id: "user1", name: "王五", department: "网络部", avatar: "/avatars/02.png" },
    { id: "user2", name: "赵六", department: "硬件部", avatar: "/avatars/03.png" },
    { id: "user3", name: "孙七", department: "软件部", avatar: "/avatars/04.png" }
  ]

  return (
    <Card>
      <CardContent className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="comment" className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4" />
              <span>补记</span>
            </TabsTrigger>
            <TabsTrigger value="assign" className="flex items-center space-x-2">
              <UserPlus className="h-4 w-4" />
              <span>指派/转办</span>
            </TabsTrigger>
            <TabsTrigger value="collaborate" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>邀请协办</span>
            </TabsTrigger>
            <TabsTrigger value="complete" className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4" />
              <span>办结</span>
            </TabsTrigger>
          </TabsList>

          {/* 补记标签页 */}
          <TabsContent value="comment" className="space-y-4">
            <div className="space-y-3">
              <Label htmlFor="comment">添加补记或评论</Label>
              <Textarea
                id="comment"
                placeholder="记录处理过程、与客户沟通内容或其他重要信息..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                rows={4}
                className="resize-none"
              />
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Upload className="h-4 w-4" />
                  <span>支持上传附件</span>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    上传附件
                  </Button>
                  <Button onClick={handleSubmitComment} disabled={!comment.trim()}>
                    <Send className="h-4 w-4 mr-2" />
                    提交补记
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* 指派/转办标签页 */}
          <TabsContent value="assign" className="space-y-4">
            <div className="space-y-3">
              <Label>选择新的处理人</Label>
              <div className="space-y-3">
                {availableUsers.map((user) => (
                  <div
                    key={user.id}
                    className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                      assignTo === user.id 
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20" 
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => setAssignTo(user.id)}
                  >
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={user.avatar} alt={user.name} />
                      <AvatarFallback>{user.name[0]}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="font-medium">{user.name}</p>
                      <p className="text-sm text-muted-foreground">{user.department}</p>
                    </div>
                    {assignTo === user.id && (
                      <Badge className="bg-blue-500">已选择</Badge>
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setAssignTo("")}>
                  取消
                </Button>
                <Button onClick={handleAssign} disabled={!assignTo}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  确认指派
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* 邀请协办标签页 */}
          <TabsContent value="collaborate" className="space-y-4">
            <div className="space-y-3">
              <Label>邀请协办方</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择协办人员或团队" />
                </SelectTrigger>
                <SelectContent>
                  {availableUsers.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={user.avatar} alt={user.name} />
                          <AvatarFallback>{user.name[0]}</AvatarFallback>
                        </Avatar>
                        <span>{user.name} - {user.department}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Textarea
                placeholder="说明需要协办的具体事项..."
                rows={3}
                className="resize-none"
              />
              <div className="flex justify-end space-x-2">
                <Button variant="outline">取消</Button>
                <Button>
                  <Users className="h-4 w-4 mr-2" />
                  发送邀请
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* 办结标签页 */}
          <TabsContent value="complete" className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="solution">解决方案</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generateAISummary}
                  className="text-purple-600 border-purple-200 hover:bg-purple-50"
                >
                  <Brain className="h-4 w-4 mr-2" />
                  AI生成摘要
                </Button>
              </div>
              <Textarea
                id="solution"
                placeholder="详细描述问题的解决方案和处理结果..."
                value={solution}
                onChange={(e) => setSolution(e.target.value)}
                rows={4}
                className="resize-none"
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>解决状态</Label>
                  <Select defaultValue="resolved">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="resolved">已解决</SelectItem>
                      <SelectItem value="workaround">临时解决</SelectItem>
                      <SelectItem value="cannot-resolve">无法解决</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>客户满意度</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择满意度" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">非常满意 (5分)</SelectItem>
                      <SelectItem value="4">满意 (4分)</SelectItem>
                      <SelectItem value="3">一般 (3分)</SelectItem>
                      <SelectItem value="2">不满意 (2分)</SelectItem>
                      <SelectItem value="1">非常不满意 (1分)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button variant="outline">保存草稿</Button>
                <Button onClick={handleComplete} disabled={!solution.trim()}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  办结工单
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
