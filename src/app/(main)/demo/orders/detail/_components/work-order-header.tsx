"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Copy, Clock, User, AlertTriangle, CheckCircle, ArrowRight } from "lucide-react"

interface WorkOrderHeaderProps {
  workOrder: {
    id: string
    title: string
    status: string
    priority: string
    type: string
    createdAt: string
    createdBy: string
    assignedTo: string
    slaDeadline: string
    tags: string[]
    customer: {
      name: string
      company: string
      phone: string
      email: string
      level: string
    }
  }
}

/**
 * 工单头部组件
 * 显示工单概览信息、状态进度和SLA倒计时
 */
export function WorkOrderHeader({ workOrder }: WorkOrderHeaderProps) {
  const [timeRemaining, setTimeRemaining] = useState("")
  const [slaProgress, setSlaProgress] = useState(0)

  // 计算SLA倒计时
  useEffect(() => {
    const updateCountdown = () => {
      const now = new Date()
      const deadline = new Date(workOrder.slaDeadline)
      const created = new Date(workOrder.createdAt)
      
      const totalTime = deadline.getTime() - created.getTime()
      const remainingTime = deadline.getTime() - now.getTime()
      
      if (remainingTime > 0) {
        const hours = Math.floor(remainingTime / (1000 * 60 * 60))
        const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60))
        setTimeRemaining(`${hours}小时${minutes}分钟`)
        setSlaProgress(((totalTime - remainingTime) / totalTime) * 100)
      } else {
        setTimeRemaining("已超时")
        setSlaProgress(100)
      }
    }

    updateCountdown()
    const interval = setInterval(updateCountdown, 60000) // 每分钟更新

    return () => clearInterval(interval)
  }, [workOrder.slaDeadline, workOrder.createdAt])

  const copyWorkOrderId = () => {
    navigator.clipboard.writeText(workOrder.id)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "待处理": return "bg-yellow-500"
      case "处理中": return "bg-blue-500"
      case "待回访": return "bg-purple-500"
      case "已关闭": return "bg-green-500"
      default: return "bg-gray-500"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "紧急": return "bg-red-500"
      case "高": return "bg-orange-500"
      case "中": return "bg-yellow-500"
      case "低": return "bg-green-500"
      default: return "bg-gray-500"
    }
  }

  const getSlaColor = () => {
    if (timeRemaining === "已超时") return "text-red-600"
    if (slaProgress > 80) return "text-orange-600"
    return "text-green-600"
  }

  const statusSteps = [
    { name: "待处理", completed: true },
    { name: "处理中", completed: true, current: workOrder.status === "处理中" },
    { name: "待回访", completed: false, current: workOrder.status === "待回访" },
    { name: "已关闭", completed: false, current: workOrder.status === "已关闭" }
  ]

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* 标题和基本信息 */}
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {workOrder.title}
                </h1>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyWorkOrderId}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  {workOrder.id}
                </Button>
              </div>
              
              {/* 标签 */}
              <div className="flex flex-wrap gap-2 mb-4">
                {workOrder.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                <Badge className={getPriorityColor(workOrder.priority)}>
                  {workOrder.priority}
                </Badge>
                <Badge variant="outline">
                  {workOrder.type}
                </Badge>
              </div>
            </div>
            
            {/* SLA倒计时 */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 min-w-[200px]">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  SLA倒计时
                </span>
              </div>
              <div className={`text-lg font-bold ${getSlaColor()}`}>
                {timeRemaining}
              </div>
              <Progress value={slaProgress} className="mt-2 h-2" />
              <p className="text-xs text-muted-foreground mt-1">
                截止时间：{new Date(workOrder.slaDeadline).toLocaleString()}
              </p>
            </div>
          </div>

          {/* 状态进度条 */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              工单流程状态
            </h3>
            <div className="flex items-center space-x-4">
              {statusSteps.map((step, index) => (
                <div key={step.name} className="flex items-center">
                  <div className="flex items-center space-x-2">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                      step.completed 
                        ? "bg-green-500 border-green-500 text-white" 
                        : step.current
                        ? "bg-blue-500 border-blue-500 text-white"
                        : "border-gray-300 text-gray-400"
                    }`}>
                      {step.completed ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <span className="text-xs font-medium">{index + 1}</span>
                      )}
                    </div>
                    <span className={`text-sm font-medium ${
                      step.current ? "text-blue-600" : step.completed ? "text-green-600" : "text-gray-400"
                    }`}>
                      {step.name}
                    </span>
                  </div>
                  {index < statusSteps.length - 1 && (
                    <ArrowRight className="h-4 w-4 text-gray-400 mx-2" />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 核心负责人信息 */}
          <div className="flex flex-wrap items-center gap-6 pt-4 border-t">
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">创建人：</span>
              <span className="text-sm font-medium">{workOrder.createdBy}</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src="/avatars/01.png" alt={workOrder.assignedTo} />
                <AvatarFallback>{workOrder.assignedTo[0]}</AvatarFallback>
              </Avatar>
              <span className="text-sm text-gray-600 dark:text-gray-400">当前处理人：</span>
              <span className="text-sm font-medium">{workOrder.assignedTo}</span>
              <Badge variant="outline" className="text-xs">主办方</Badge>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">创建时间：</span>
              <span className="text-sm">{new Date(workOrder.createdAt).toLocaleString()}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
