"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Filter, 
  MessageSquare, 
  Settings, 
  UserPlus, 
  Upload, 
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText
} from "lucide-react"

interface TimelineSectionProps {
  workOrderId: string
}

/**
 * 时间轴组件
 * 显示工单的完整交互历史，支持智能过滤
 */
export function TimelineSection({ workOrderId }: TimelineSectionProps) {
  const [activeFilters, setActiveFilters] = useState<string[]>([])

  // 模拟时间轴数据
  const timelineEvents = [
    {
      id: "1",
      type: "comment",
      user: "李四",
      avatar: "/avatars/01.png",
      action: "添加了补记",
      content: "已联系客户确认问题，初步判断为网络配置问题。正在进行详细排查。",
      timestamp: "2024-01-15 14:30:00",
      attachments: []
    },
    {
      id: "2", 
      type: "upload",
      user: "李四",
      avatar: "/avatars/01.png",
      action: "上传了附件",
      content: "网络诊断截图",
      timestamp: "2024-01-15 14:25:00",
      attachments: ["network_diagnostic.png", "error_log.txt"]
    },
    {
      id: "3",
      type: "assign",
      user: "张三",
      avatar: "/avatars/02.png", 
      action: "将工单指派给了",
      content: "李四 (IT支持部)",
      timestamp: "2024-01-15 10:15:00",
      attachments: []
    },
    {
      id: "4",
      type: "status",
      user: "系统",
      avatar: "",
      action: "工单状态变更为",
      content: "处理中",
      timestamp: "2024-01-15 10:15:00",
      attachments: []
    },
    {
      id: "5",
      type: "create",
      user: "张三",
      avatar: "/avatars/02.png",
      action: "创建了工单",
      content: "销售部打印机无法连接网络，影响正常办公。客户反馈打印机显示网络连接错误。",
      timestamp: "2024-01-15 09:30:00",
      attachments: []
    }
  ]

  const filterOptions = [
    { id: "comment", label: "仅看评论", icon: MessageSquare },
    { id: "status", label: "仅看状态变更", icon: Settings },
    { id: "upload", label: "仅看附件", icon: Upload },
    { id: "assign", label: "仅看指派", icon: UserPlus }
  ]

  const toggleFilter = (filterId: string) => {
    setActiveFilters(prev => 
      prev.includes(filterId) 
        ? prev.filter(id => id !== filterId)
        : [...prev, filterId]
    )
  }

  const filteredEvents = activeFilters.length > 0 
    ? timelineEvents.filter(event => activeFilters.includes(event.type))
    : timelineEvents

  const getEventIcon = (type: string) => {
    switch (type) {
      case "comment": return MessageSquare
      case "upload": return Upload
      case "assign": return UserPlus
      case "status": return Settings
      case "create": return FileText
      default: return MessageSquare
    }
  }

  const getEventColor = (type: string) => {
    switch (type) {
      case "comment": return "text-blue-600"
      case "upload": return "text-green-600"
      case "assign": return "text-purple-600"
      case "status": return "text-orange-600"
      case "create": return "text-gray-600"
      default: return "text-gray-600"
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>交互历史</span>
            <Badge variant="secondary">{timelineEvents.length}条记录</Badge>
          </CardTitle>
          
          {/* 时间轴筛选器 */}
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <div className="flex space-x-1">
              {filterOptions.map((filter) => {
                const IconComponent = filter.icon
                const isActive = activeFilters.includes(filter.id)
                return (
                  <Button
                    key={filter.id}
                    variant={isActive ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleFilter(filter.id)}
                    className="text-xs"
                  >
                    <IconComponent className="h-3 w-3 mr-1" />
                    {filter.label}
                  </Button>
                )
              })}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-6">
          {filteredEvents.map((event, index) => {
            const IconComponent = getEventIcon(event.type)
            const isSystem = event.user === "系统"
            
            return (
              <div key={event.id} className="relative">
                {/* 时间轴线条 */}
                {index < filteredEvents.length - 1 && (
                  <div className="absolute left-6 top-12 w-0.5 h-16 bg-gray-200 dark:bg-gray-700"></div>
                )}
                
                <div className="flex space-x-4">
                  {/* 头像或图标 */}
                  <div className="flex-shrink-0">
                    {isSystem ? (
                      <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-800">
                        <IconComponent className={`h-5 w-5 ${getEventColor(event.type)}`} />
                      </div>
                    ) : (
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={event.avatar} alt={event.user} />
                        <AvatarFallback>{event.user[0]}</AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                  
                  {/* 事件内容 */}
                  <div className="flex-1 min-w-0">
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                      {/* 事件头部 */}
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900 dark:text-white">
                            {event.user}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {event.action}
                          </span>
                          {event.type === "assign" && (
                            <Badge variant="outline" className="text-xs">
                              {event.content}
                            </Badge>
                          )}
                          {event.type === "status" && (
                            <Badge className="text-xs bg-blue-500">
                              {event.content}
                            </Badge>
                          )}
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {new Date(event.timestamp).toLocaleString()}
                        </span>
                      </div>
                      
                      {/* 事件内容 */}
                      {(event.type === "comment" || event.type === "create") && (
                        <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                          {event.content}
                        </p>
                      )}
                      
                      {/* 附件 */}
                      {event.attachments.length > 0 && (
                        <div className="mt-3 space-y-2">
                          <p className="text-xs text-muted-foreground">附件：</p>
                          <div className="flex flex-wrap gap-2">
                            {event.attachments.map((attachment, idx) => (
                              <Badge key={idx} variant="secondary" className="text-xs cursor-pointer hover:bg-blue-100">
                                <Upload className="h-3 w-3 mr-1" />
                                {attachment}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
        
        {filteredEvents.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>没有符合筛选条件的记录</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
