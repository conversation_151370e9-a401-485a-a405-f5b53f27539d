"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  FileText, 
  User, 
  Users, 
  Phone, 
  Mail, 
  ExternalLink, 
  Link,
  BookOpen,
  Star,
  Clock,
  AlertTriangle,
  AtSign
} from "lucide-react"

interface SidePanelProps {
  workOrder: {
    id: string
    title: string
    status: string
    priority: string
    type: string
    createdAt: string
    createdBy: string
    assignedTo: string
    slaDeadline: string
    tags: string[]
    customer: {
      name: string
      company: string
      phone: string
      email: string
      level: string
    }
  }
}

/**
 * 侧边栏组件
 * 显示工单属性、客户信息、参与人和关联信息
 */
export function SidePanel({ workOrder }: SidePanelProps) {
  // 模拟数据
  const collaborators = [
    { id: "1", name: "王五", department: "网络部", avatar: "/avatars/02.png", online: true },
    { id: "2", name: "赵六", department: "硬件部", avatar: "/avatars/03.png", online: false }
  ]

  const ccList = [
    { id: "1", name: "张总监", department: "IT部", avatar: "/avatars/04.png" }
  ]

  const customerHistory = [
    { id: "WO-2024-001200", title: "邮件服务器连接问题", status: "已关闭" },
    { id: "WO-2024-001150", title: "打印机驱动安装", status: "已关闭" },
    { id: "WO-2024-001100", title: "网络权限申请", status: "已关闭" }
  ]

  const relatedTickets = [
    { id: "WO-2024-001235", title: "销售部网络设备维护", status: "处理中" }
  ]

  const knowledgeArticles = [
    { id: "KB-001", title: "打印机网络连接故障排查指南", category: "网络问题" },
    { id: "KB-002", title: "办公设备IP地址配置教程", category: "网络配置" }
  ]

  return (
    <div className="space-y-6">
      {/* 工单详情卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg">
            <FileText className="h-5 w-5" />
            <span>工单详情</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">优先级</p>
              <Badge className="mt-1 bg-orange-500">{workOrder.priority}</Badge>
            </div>
            <div>
              <p className="text-muted-foreground">工单类型</p>
              <p className="mt-1 font-medium">{workOrder.type}</p>
            </div>
            <div>
              <p className="text-muted-foreground">创建时间</p>
              <p className="mt-1 font-medium">{new Date(workOrder.createdAt).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-muted-foreground">创建人</p>
              <p className="mt-1 font-medium">{workOrder.createdBy}</p>
            </div>
          </div>
          
          <Separator />
          
          <div>
            <p className="text-muted-foreground text-sm mb-2">SLA策略</p>
            <p className="text-sm font-medium">高优IT服务 - 4小时内响应，24小时内解决</p>
          </div>
          
          <div>
            <p className="text-muted-foreground text-sm mb-2">自定义字段</p>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>影响的业务系统</span>
                <span className="font-medium">OA办公系统</span>
              </div>
              <div className="flex justify-between">
                <span>设备资产编号</span>
                <span className="font-medium">PRINTER-205</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 客户信息卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg">
            <User className="h-5 w-5" />
            <span>客户信息</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarFallback>{workOrder.customer.name[0]}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <p className="font-medium">{workOrder.customer.name}</p>
                {workOrder.customer.level === "VIP" && (
                  <Badge className="bg-yellow-500">
                    <Star className="h-3 w-3 mr-1" />
                    VIP
                  </Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground">{workOrder.customer.company}</p>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{workOrder.customer.phone}</span>
              </div>
              <Button variant="ghost" size="sm">
                <Phone className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{workOrder.customer.email}</span>
              </div>
              <Button variant="ghost" size="sm">
                <Mail className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <Separator />
          
          <div>
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium">历史工单摘要</p>
              <Button variant="ghost" size="sm">
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
            <div className="space-y-2">
              {customerHistory.slice(0, 3).map((ticket) => (
                <div key={ticket.id} className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground truncate flex-1 mr-2">
                    {ticket.title}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {ticket.status}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 参与人卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg">
            <Users className="h-5 w-5" />
            <span>参与人</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm font-medium mb-2">主办方</p>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/avatars/01.png" alt={workOrder.assignedTo} />
                  <AvatarFallback>{workOrder.assignedTo[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-medium">{workOrder.assignedTo}</p>
                  <p className="text-xs text-muted-foreground">IT支持部</p>
                </div>
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
              </div>
              <Button variant="ghost" size="sm">
                <AtSign className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {collaborators.length > 0 && (
            <>
              <Separator />
              <div>
                <p className="text-sm font-medium mb-2">协办方</p>
                <div className="space-y-2">
                  {collaborators.map((collaborator) => (
                    <div key={collaborator.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={collaborator.avatar} alt={collaborator.name} />
                          <AvatarFallback>{collaborator.name[0]}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">{collaborator.name}</p>
                          <p className="text-xs text-muted-foreground">{collaborator.department}</p>
                        </div>
                        <div className={`h-2 w-2 rounded-full ${collaborator.online ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <AtSign className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
          
          {ccList.length > 0 && (
            <>
              <Separator />
              <div>
                <p className="text-sm font-medium mb-2">抄送列表</p>
                <div className="space-y-2">
                  {ccList.map((person) => (
                    <div key={person.id} className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={person.avatar} alt={person.name} />
                        <AvatarFallback>{person.name[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{person.name}</p>
                        <p className="text-xs text-muted-foreground">{person.department}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* 关联信息卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg">
            <Link className="h-5 w-5" />
            <span>关联信息</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {relatedTickets.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-2">关联工单</p>
              <div className="space-y-2">
                {relatedTickets.map((ticket) => (
                  <div key={ticket.id} className="flex items-center justify-between text-sm">
                    <span className="text-blue-600 hover:underline cursor-pointer truncate flex-1 mr-2">
                      {ticket.id}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {ticket.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {knowledgeArticles.length > 0 && (
            <>
              <Separator />
              <div>
                <p className="text-sm font-medium mb-2">关联知识库</p>
                <div className="space-y-2">
                  {knowledgeArticles.map((article) => (
                    <div key={article.id} className="space-y-1">
                      <p className="text-sm text-blue-600 hover:underline cursor-pointer">
                        {article.title}
                      </p>
                      <Badge variant="secondary" className="text-xs">
                        {article.category}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
