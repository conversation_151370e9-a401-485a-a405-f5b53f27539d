"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Send, Save, X, Eye, CheckCircle, Clock, Users, ArrowRight } from "lucide-react"

/**
 * 操作按钮组件
 * 提供创建、保存草稿、取消等操作，并显示自动化流程预览
 */
export function ActionButtons() {
  const [showPreview, setShowPreview] = useState(true)

  // 模拟自动化流程预览数据
  const automationPreview = {
    assignTo: "IT网络组",
    ccTo: "张三(总监)",
    slaPolicy: "高优IT服务",
    estimatedTime: "4小时内响应，24小时内解决",
    nextSteps: [
      "工单将自动指派给李四",
      "系统将发送邮件通知相关人员", 
      "SLA倒计时开始",
      "客户将收到工单创建确认"
    ]
  }

  const handleSubmit = () => {
    // 模拟提交逻辑
    console.log("提交工单")
  }

  const handleSaveDraft = () => {
    // 模拟保存草稿逻辑
    console.log("保存草稿")
  }

  const handleCancel = () => {
    // 模拟取消逻辑
    console.log("取消创建")
  }

  return (
    <div className="space-y-6">
      {/* 自动化流程预览 */}
      {showPreview && (
        <Card className="border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold text-blue-800 dark:text-blue-200">
                  自动化流程预览
                </h3>
                <Badge variant="secondary">实时更新</Badge>
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setShowPreview(false)}
                className="text-blue-600 hover:text-blue-800"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="space-y-4">
              <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                根据当前设置，提交后将执行以下流程：
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">
                      自动指派给「{automationPreview.assignTo}」
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-purple-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">
                      自动抄送给「{automationPreview.ccTo}」
                    </span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-orange-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">
                      应用SLA策略「{automationPreview.slaPolicy}」
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ArrowRight className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">
                      {automationPreview.estimatedTime}
                    </span>
                  </div>
                </div>
              </div>
              
              <Separator className="bg-blue-200 dark:bg-blue-700" />
              
              <div>
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                  后续流程步骤：
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {automationPreview.nextSteps.map((step, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                      <span className="text-xs text-blue-700 dark:text-blue-300">
                        {step}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 操作按钮区域 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4 justify-end">
            <Button 
              variant="outline" 
              onClick={handleCancel}
              className="sm:w-auto w-full"
            >
              <X className="h-4 w-4 mr-2" />
              取消
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleSaveDraft}
              className="sm:w-auto w-full"
            >
              <Save className="h-4 w-4 mr-2" />
              保存为草稿
            </Button>
            
            <Button 
              onClick={handleSubmit}
              className="sm:w-auto w-full bg-blue-600 hover:bg-blue-700"
            >
              <Send className="h-4 w-4 mr-2" />
              创建并提交
            </Button>
          </div>
          
          <div className="mt-4 text-center">
            <p className="text-xs text-muted-foreground">
              提交后工单将进入处理流程，您可以在「我的工单」中跟踪进度
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
