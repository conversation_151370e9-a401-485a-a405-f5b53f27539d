"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Monitor, Headphones, ShoppingCart, Wrench } from "lucide-react"

/**
 * 工单模板选择组件
 * 用户首先选择工单类型，后续表单内容会根据选择动态变化
 */
export function TemplateSelection() {
  const [selectedTemplate, setSelectedTemplate] = useState<string>("")

  const templates = [
    {
      id: "it-service",
      name: "IT服务请求",
      description: "系统故障、设备报修、软件安装等技术支持",
      icon: Monitor,
      color: "bg-blue-500",
      badge: "技术支持"
    },
    {
      id: "customer-complaint",
      name: "客户投诉",
      description: "客户反馈、服务质量问题、产品缺陷等",
      icon: Headphones,
      color: "bg-red-500",
      badge: "客户服务"
    },
    {
      id: "purchase-request",
      name: "采购申请",
      description: "物资采购、设备申请、供应商管理等",
      icon: ShoppingCart,
      color: "bg-green-500",
      badge: "采购管理"
    },
    {
      id: "maintenance-order",
      name: "售后维修单",
      description: "产品维修、上门服务、保修处理等",
      icon: Wrench,
      color: "bg-orange-500",
      badge: "售后服务"
    }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span>选择工单类型</span>
          <Badge variant="destructive">必选</Badge>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          请选择最符合您需求的工单类型，系统将根据您的选择提供相应的表单字段
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {templates.map((template) => {
            const IconComponent = template.icon
            const isSelected = selectedTemplate === template.id
            
            return (
              <div
                key={template.id}
                className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 hover:shadow-md ${
                  isSelected 
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20" 
                    : "border-gray-200 dark:border-gray-700 hover:border-gray-300"
                }`}
                onClick={() => setSelectedTemplate(template.id)}
              >
                <div className="flex items-start space-x-3">
                  <div className={`rounded-lg ${template.color} p-2 text-white`}>
                    <IconComponent className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {template.name}
                      </h3>
                      <Badge variant="secondary" className="text-xs">
                        {template.badge}
                      </Badge>
                    </div>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                      {template.description}
                    </p>
                  </div>
                </div>
                
                {/* 选中状态指示器 */}
                {isSelected && (
                  <div className="absolute top-2 right-2">
                    <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                  </div>
                )}
              </div>
            )
          })}
        </div>
        
        {selectedTemplate && (
          <div className="mt-4 rounded-lg bg-green-50 dark:bg-green-900/20 p-3 border border-green-200 dark:border-green-800">
            <p className="text-sm text-green-800 dark:text-green-200">
              ✓ 已选择「{templates.find(t => t.id === selectedTemplate)?.name}」模板，表单字段已更新
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
