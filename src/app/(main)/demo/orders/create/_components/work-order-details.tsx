"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar, Upload, Clock, Tag } from "lucide-react"

/**
 * 工单核心信息组件
 * 根据选择的模板动态显示相应的表单字段
 */
export function WorkOrderDetails() {
  const [priority, setPriority] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState("")

  const priorityOptions = [
    { value: "urgent", label: "紧急", color: "bg-red-500", description: "需要立即处理" },
    { value: "high", label: "高", color: "bg-orange-500", description: "4小时内响应" },
    { value: "medium", label: "中", color: "bg-yellow-500", description: "24小时内响应" },
    { value: "low", label: "低", color: "bg-green-500", description: "72小时内响应" }
  ]

  const commonTags = ["网络问题", "打印机", "软件安装", "硬件故障", "权限申请", "数据恢复"]

  const handleAddTag = (tag: string) => {
    if (tag && !tags.includes(tag)) {
      setTags([...tags, tag])
      setNewTag("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>工单详细信息</CardTitle>
        <p className="text-sm text-muted-foreground">
          请详细描述问题或需求，系统将根据内容自动推荐相关解决方案
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 工单标题 */}
        <div className="space-y-2">
          <Label htmlFor="title" className="flex items-center space-x-2">
            <span>工单标题</span>
            <Badge variant="destructive">必填</Badge>
          </Label>
          <Input
            id="title"
            placeholder="请简明扼要地概括问题，如：销售部打印机无法连接网络"
            className="text-base"
          />
        </div>

        {/* 问题描述 */}
        <div className="space-y-2">
          <Label htmlFor="description" className="flex items-center space-x-2">
            <span>问题描述</span>
            <Badge variant="destructive">必填</Badge>
          </Label>
          <Textarea
            id="description"
            placeholder="请详细描述问题现象、重现步骤、影响范围等信息..."
            rows={4}
            className="resize-none"
          />
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Upload className="h-4 w-4" />
            <span>支持上传图片和附件，最大10MB</span>
          </div>
        </div>

        {/* 优先级选择 */}
        <div className="space-y-2">
          <Label htmlFor="priority">优先级</Label>
          <Select value={priority} onValueChange={setPriority}>
            <SelectTrigger>
              <SelectValue placeholder="选择工单优先级" />
            </SelectTrigger>
            <SelectContent>
              {priorityOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center space-x-2">
                    <div className={`h-3 w-3 rounded-full ${option.color}`}></div>
                    <span>{option.label}</span>
                    <span className="text-xs text-muted-foreground">- {option.description}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 工单标签 */}
        <div className="space-y-2">
          <Label className="flex items-center space-x-2">
            <Tag className="h-4 w-4" />
            <span>工单标签</span>
          </Label>
          
          {/* 已选标签 */}
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="cursor-pointer hover:bg-red-100 dark:hover:bg-red-900/20">
                  {tag}
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 text-xs hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              ))}
            </div>
          )}
          
          {/* 标签输入 */}
          <div className="flex space-x-2">
            <Input
              placeholder="输入自定义标签"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleAddTag(newTag)
                }
              }}
            />
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => handleAddTag(newTag)}
              disabled={!newTag}
            >
              添加
            </Button>
          </div>
          
          {/* 常用标签 */}
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">常用标签：</p>
            <div className="flex flex-wrap gap-2">
              {commonTags.map((tag) => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  onClick={() => handleAddTag(tag)}
                >
                  + {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* SLA显示 */}
        {priority && (
          <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800 dark:text-blue-200">服务级别协议 (SLA)</span>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              根据当前优先级「{priorityOptions.find(p => p.value === priority)?.label}」，
              系统将自动应用相应的SLA策略：
              {priority === "urgent" && "需在1小时内响应，4小时内解决"}
              {priority === "high" && "需在4小时内响应，24小时内解决"}
              {priority === "medium" && "需在24小时内响应，72小时内解决"}
              {priority === "low" && "需在72小时内响应，7天内解决"}
            </p>
          </div>
        )}

        {/* 动态字段区域 - 根据模板显示 */}
        <div className="space-y-4 border-t pt-4">
          <h3 className="font-medium text-gray-900 dark:text-white">模板专用字段</h3>
          
          {/* IT服务请求专用字段 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="affected-system">影响的业务系统</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择受影响的系统" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="erp">ERP系统</SelectItem>
                  <SelectItem value="crm">CRM系统</SelectItem>
                  <SelectItem value="oa">OA办公系统</SelectItem>
                  <SelectItem value="email">邮件系统</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="asset-number">设备资产编号</Label>
              <Input
                id="asset-number"
                placeholder="如：PC-001, PRINTER-205"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
