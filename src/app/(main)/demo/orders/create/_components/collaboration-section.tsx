"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Brain, ChevronDown, Users, Mail, Star, Clock, CheckCircle } from "lucide-react"

/**
 * 协同与指派组件
 * 智能推荐处理人，支持主办方、协办方和抄送设置
 */
export function CollaborationSection() {
  const [showRecommendation, setShowRecommendation] = useState(false)
  const [selectedOwner, setSelectedOwner] = useState<any>(null)

  // 模拟智能推荐的处理人
  const recommendedOwner = {
    id: "user-001",
    name: "李四",
    department: "IT支持部",
    avatar: "/avatars/01.png",
    skills: ["数据库", "Python"],
    currentLoad: 2,
    teamAverage: 4.5,
    successfulCases: 5,
    rating: 4.9,
    matchScore: 92
  }

  const collaborators = [
    { id: "user-002", name: "王五", department: "网络部", avatar: "/avatars/02.png" },
    { id: "user-003", name: "赵六", department: "安全部", avatar: "/avatars/03.png" }
  ]

  const ccList = [
    { id: "user-004", name: "张总监", department: "IT部", avatar: "/avatars/04.png" }
  ]

  const handleAcceptRecommendation = () => {
    setSelectedOwner(recommendedOwner)
    setShowRecommendation(false)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="h-5 w-5" />
          <span>协同与指派</span>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          设定工单的处理团队，系统将根据技能匹配和负载情况智能推荐最佳人选
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 主办方指派 */}
        <div className="space-y-3">
          <Label className="flex items-center space-x-2">
            <span>主办方指派</span>
            <Badge variant="destructive">必填</Badge>
          </Label>
          
          {/* 智能推荐区域 */}
          {!selectedOwner && (
            <div className="rounded-lg border-2 border-dashed border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/20 p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Brain className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-800 dark:text-blue-200">
                    智能推荐
                  </span>
                  <Badge variant="secondary">AI分析</Badge>
                </div>
                <Badge className="bg-green-500">
                  匹配度 {recommendedOwner.matchScore}%
                </Badge>
              </div>
              
              <div className="flex items-center space-x-4 mb-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={recommendedOwner.avatar} alt={recommendedOwner.name} />
                  <AvatarFallback>{recommendedOwner.name[0]}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {recommendedOwner.name}
                    </h3>
                    <Badge variant="outline">{recommendedOwner.department}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    技能匹配：{recommendedOwner.skills.join("、")} • 当前负载：{recommendedOwner.currentLoad}个工单
                  </p>
                </div>
              </div>
              
              {/* 推荐理由 */}
              <Collapsible open={showRecommendation} onOpenChange={setShowRecommendation}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm" className="w-full justify-between">
                    <span>查看推荐理由</span>
                    <ChevronDown className={`h-4 w-4 transition-transform ${showRecommendation ? 'rotate-180' : ''}`} />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-3 space-y-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>技能匹配度 ({recommendedOwner.matchScore}%)</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-blue-500" />
                      <span>当前负载 (低于平均)</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span>历史评分 ({recommendedOwner.rating}/5.0)</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>相关经验 ({recommendedOwner.successfulCases}个案例)</span>
                    </div>
                  </div>
                  <div className="mt-3 p-3 bg-white dark:bg-gray-800 rounded border">
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      <strong>详细分析：</strong>工单所需的「数据库」(专家级)、「Python」(熟练级) 技能与李四的技能画像高度匹配。
                      李四当前处理中工单为 2 个(团队平均为4.5)，负载较低。在过去3个月内成功解决了 5 个类似的数据库连接问题，
                      历史平均服务评分为 4.9/5.0。
                    </p>
                  </div>
                </CollapsibleContent>
              </Collapsible>
              
              <div className="flex space-x-2 mt-4">
                <Button onClick={handleAcceptRecommendation} className="flex-1">
                  采用推荐
                </Button>
                <Button variant="outline" className="flex-1">
                  手动选择
                </Button>
              </div>
            </div>
          )}
          
          {/* 已选择的主办方 */}
          {selectedOwner && (
            <div className="flex items-center justify-between p-3 border rounded-lg bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
              <div className="flex items-center space-x-3">
                <Avatar>
                  <AvatarImage src={selectedOwner.avatar} alt={selectedOwner.name} />
                  <AvatarFallback>{selectedOwner.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-green-800 dark:text-green-200">
                    {selectedOwner.name}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-300">
                    {selectedOwner.department}
                  </p>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={() => setSelectedOwner(null)}>
                更换
              </Button>
            </div>
          )}
        </div>

        {/* 协办方添加 */}
        <div className="space-y-3">
          <Label>协办方 (可选)</Label>
          <Input placeholder="搜索并添加协办方..." />
          {collaborators.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">已添加的协办方：</p>
              {collaborators.map((collaborator) => (
                <div key={collaborator.id} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={collaborator.avatar} alt={collaborator.name} />
                      <AvatarFallback>{collaborator.name[0]}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{collaborator.name}</p>
                      <p className="text-xs text-muted-foreground">{collaborator.department}</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">移除</Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 抄送 */}
        <div className="space-y-3">
          <Label className="flex items-center space-x-2">
            <Mail className="h-4 w-4" />
            <span>抄送 (可选)</span>
          </Label>
          <Input placeholder="搜索并添加抄送人员..." />
          {ccList.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">抄送人员：</p>
              {ccList.map((person) => (
                <div key={person.id} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={person.avatar} alt={person.name} />
                      <AvatarFallback>{person.name[0]}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{person.name}</p>
                      <p className="text-xs text-muted-foreground">{person.department}</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">移除</Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
