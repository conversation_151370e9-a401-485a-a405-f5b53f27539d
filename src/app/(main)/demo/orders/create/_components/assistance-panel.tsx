"use client"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { BookOpen, FileText, Copy, ExternalLink, Lightbulb, History } from "lucide-react"

/**
 * 辅助工具面板组件
 * 提供知识库推荐、相似工单、回复模板等辅助功能
 */
export function AssistancePanel() {
  const knowledgeBaseArticles = [
    {
      id: "kb-001",
      title: "打印机网络连接故障排查指南",
      summary: "详细介绍打印机无法连接网络的常见原因和解决步骤",
      relevance: 95,
      category: "网络问题"
    },
    {
      id: "kb-002", 
      title: "Windows网络驱动程序更新方法",
      summary: "如何更新和重新安装网络驱动程序",
      relevance: 88,
      category: "驱动问题"
    },
    {
      id: "kb-003",
      title: "办公设备IP地址配置教程",
      summary: "设置静态IP地址和DNS配置的详细步骤",
      relevance: 82,
      category: "网络配置"
    }
  ]

  const similarTickets = [
    {
      id: "ticket-001",
      title: "财务部打印机连接问题",
      status: "已解决",
      solution: "更新打印机驱动程序，重新配置网络设置",
      handler: "李四",
      resolvedTime: "2小时",
      satisfaction: 4.8
    },
    {
      id: "ticket-002",
      title: "销售部网络打印机故障",
      status: "已解决", 
      solution: "检查网络线缆连接，重启路由器",
      handler: "王五",
      resolvedTime: "1.5小时",
      satisfaction: 4.9
    }
  ]

  const replyTemplates = [
    {
      id: "template-001",
      name: "IT问题确认模板",
      content: "您好，我们已收到您的IT支持请求。为了更好地为您服务，请提供以下信息：1. 具体的错误信息截图 2. 问题发生的时间 3. 之前是否正常使用"
    },
    {
      id: "template-002",
      name: "网络问题排查模板", 
      content: "请按以下步骤进行初步排查：1. 检查网络线缆连接是否正常 2. 重启设备 3. 检查IP地址配置 4. 如问题仍存在，请联系我们进一步处理"
    }
  ]

  return (
    <div className="space-y-6">
      {/* 知识库推荐 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg">
            <BookOpen className="h-5 w-5" />
            <span>知识库推荐</span>
            <Badge variant="secondary">AI智能</Badge>
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            基于工单内容智能推荐相关解决方案
          </p>
        </CardHeader>
        <CardContent className="space-y-3">
          {knowledgeBaseArticles.map((article) => (
            <div key={article.id} className="p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-medium text-sm leading-tight">{article.title}</h4>
                <Badge variant="outline" className="text-xs">
                  {article.relevance}%
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground mb-2">{article.summary}</p>
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-xs">{article.category}</Badge>
                <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                  <ExternalLink className="h-3 w-3 mr-1" />
                  查看
                </Button>
              </div>
            </div>
          ))}
          
          <Button variant="outline" className="w-full" size="sm">
            查看更多知识库文章
          </Button>
        </CardContent>
      </Card>

      {/* 相似历史工单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg">
            <History className="h-5 w-5" />
            <span>相似历史工单</span>
            <Badge variant="secondary">经验复用</Badge>
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            参考类似问题的成功解决方案
          </p>
        </CardHeader>
        <CardContent className="space-y-3">
          {similarTickets.map((ticket) => (
            <div key={ticket.id} className="p-3 border rounded-lg">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-medium text-sm leading-tight">{ticket.title}</h4>
                <Badge className="bg-green-500 text-xs">{ticket.status}</Badge>
              </div>
              <p className="text-xs text-muted-foreground mb-2">{ticket.solution}</p>
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>处理人：{ticket.handler}</span>
                <span>用时：{ticket.resolvedTime}</span>
              </div>
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center space-x-1">
                  <span className="text-xs text-muted-foreground">满意度：</span>
                  <span className="text-xs font-medium text-yellow-600">{ticket.satisfaction}/5.0</span>
                </div>
                <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                  <Copy className="h-3 w-3 mr-1" />
                  复用方案
                </Button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 回复模板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg">
            <FileText className="h-5 w-5" />
            <span>回复模板</span>
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            使用标准化模板提升沟通效率
          </p>
        </CardHeader>
        <CardContent className="space-y-3">
          {replyTemplates.map((template) => (
            <div key={template.id} className="p-3 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-sm">{template.name}</h4>
                <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                  <Copy className="h-3 w-3 mr-1" />
                  使用
                </Button>
              </div>
              <p className="text-xs text-muted-foreground leading-relaxed">
                {template.content.length > 80 
                  ? `${template.content.substring(0, 80)}...` 
                  : template.content
                }
              </p>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 智能提示 */}
      <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
        <CardContent className="p-4">
          <div className="flex items-start space-x-2">
            <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800 dark:text-blue-200 text-sm mb-1">
                智能提示
              </h4>
              <p className="text-xs text-blue-700 dark:text-blue-300 leading-relaxed">
                根据当前输入的内容，建议将工单优先级设置为「高」，并添加「网络问题」标签。
                系统检测到类似问题的平均解决时间为2小时。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
