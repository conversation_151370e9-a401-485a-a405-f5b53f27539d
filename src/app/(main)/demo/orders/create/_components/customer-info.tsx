"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, Plus, Star, Clock, AlertTriangle } from "lucide-react"

/**
 * 客户信息组件
 * 智能搜索客户信息，显示客户健康度看板
 */
export function CustomerInfo() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null)

  // 模拟客户搜索结果
  const searchResults = [
    {
      id: "1",
      name: "张三",
      company: "科技有限公司",
      phone: "138****1234",
      email: "z<PERSON><PERSON>@tech.com",
      vipLevel: "VIP",
      avatar: "/avatars/01.png"
    },
    {
      id: "2", 
      name: "李四",
      company: "制造集团",
      phone: "139****5678",
      email: "<EMAIL>",
      vipLevel: "普通",
      avatar: "/avatars/02.png"
    }
  ]

  // 模拟客户健康度数据
  const customerHealth = {
    slaRate: 85,
    satisfaction: 4.2,
    activeTickets: 3,
    commonTags: ["网络问题", "打印机", "VIP加急"]
  }

  const handleCustomerSelect = (customer: any) => {
    setSelectedCustomer(customer)
    setSearchQuery(customer.name)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span>客户信息</span>
          <Badge variant="destructive">必填</Badge>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          搜索并选择客户，系统将自动显示客户服务历史和健康度信息
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 客户搜索 */}
        <div className="space-y-2">
          <Label htmlFor="customer-search">客户搜索</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              id="customer-search"
              placeholder="输入客户姓名、电话、公司名称或员工工号..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {/* 搜索结果 */}
          {searchQuery && !selectedCustomer && (
            <div className="mt-2 rounded-lg border bg-white dark:bg-gray-800 shadow-lg">
              {searchResults
                .filter(customer => 
                  customer.name.includes(searchQuery) || 
                  customer.company.includes(searchQuery) ||
                  customer.phone.includes(searchQuery)
                )
                .map((customer) => (
                  <div
                    key={customer.id}
                    className="flex items-center space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b last:border-b-0"
                    onClick={() => handleCustomerSelect(customer)}
                  >
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={customer.avatar} alt={customer.name} />
                      <AvatarFallback>{customer.name[0]}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{customer.name}</span>
                        {customer.vipLevel === "VIP" && (
                          <Badge variant="default" className="bg-yellow-500">
                            <Star className="h-3 w-3 mr-1" />
                            VIP
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {customer.company} • {customer.phone}
                      </p>
                    </div>
                  </div>
                ))}
              
              {/* 新建客户选项 */}
              <div className="p-3 border-t">
                <Button variant="ghost" className="w-full justify-start" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  创建新客户档案
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* 选中客户的健康度看板 */}
        {selectedCustomer && (
          <div className="rounded-lg border bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900 dark:text-white">
                客户健康度看板
              </h3>
              <Badge variant="outline">实时数据</Badge>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Clock className="h-4 w-4 text-blue-500 mr-1" />
                  <span className="text-sm text-muted-foreground">SLA达成率</span>
                </div>
                <div className={`text-2xl font-bold ${customerHealth.slaRate >= 90 ? 'text-green-600' : customerHealth.slaRate >= 70 ? 'text-yellow-600' : 'text-red-600'}`}>
                  {customerHealth.slaRate}%
                </div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Star className="h-4 w-4 text-yellow-500 mr-1" />
                  <span className="text-sm text-muted-foreground">满意度</span>
                </div>
                <div className="text-2xl font-bold text-yellow-600">
                  {customerHealth.satisfaction}/5.0
                </div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <AlertTriangle className="h-4 w-4 text-orange-500 mr-1" />
                  <span className="text-sm text-muted-foreground">活跃工单</span>
                </div>
                <div className="text-2xl font-bold text-orange-600">
                  {customerHealth.activeTickets}
                </div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <span className="text-sm text-muted-foreground">常见标签</span>
                </div>
                <div className="flex flex-wrap gap-1 justify-center">
                  {customerHealth.commonTags.slice(0, 2).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
            
            {customerHealth.slaRate < 70 && (
              <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <p className="text-sm text-red-800 dark:text-red-200">
                  ⚠️ 注意：该客户近期SLA达成率较低，建议提升服务优先级并采取主动沟通策略
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
