import { PageHeader } from "./_components/page-header"
import { TemplateSelection } from "./_components/template-selection"
import { CustomerInfo } from "./_components/customer-info"
import { WorkOrderDetails } from "./_components/work-order-details"
import { CollaborationSection } from "./_components/collaboration-section"
import { AssistancePanel } from "./_components/assistance-panel"
import { ActionButtons } from "./_components/action-buttons"

/**
 * 新建工单页面
 * 基于业务需求文档设计的完整工单创建流程
 */
export default function CreateWorkOrderPage() {
  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <PageHeader 
        title="新建工单" 
        description="快速创建工单，高效处理客户需求和内部协作事务"
      />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主要表单区域 */}
        <div className="lg:col-span-2 space-y-6">
          <TemplateSelection />
          <CustomerInfo />
          <WorkOrderDetails />
          <CollaborationSection />
          <ActionButtons />
        </div>
        
        {/* 辅助工具面板 */}
        <div className="lg:col-span-1">
          <AssistancePanel />
        </div>
      </div>
    </div>
  )
}
