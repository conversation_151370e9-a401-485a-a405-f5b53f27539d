"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { 
  Users, 
  TrendingUp, 
  Clock, 
  Star,
  Zap,
  Target
} from "lucide-react"

/**
 * 团队统计组件
 * 显示团队成员的抢单情况和当前负载
 */
export function TeamStats() {
  // 模拟团队数据
  const teamMembers = [
    {
      id: "1",
      name: "李四",
      avatar: "/avatars/01.png",
      department: "IT支持部",
      currentLoad: 3,
      maxLoad: 8,
      todayClaimed: 5,
      avgResponseTime: "2.3分钟",
      rating: 4.8,
      isOnline: true,
      skills: ["网络问题", "硬件故障"]
    },
    {
      id: "2", 
      name: "王五",
      avatar: "/avatars/02.png",
      department: "网络部",
      currentLoad: 6,
      maxLoad: 8,
      todayClaimed: 3,
      avgResponseTime: "1.8分钟",
      rating: 4.9,
      isOnline: true,
      skills: ["网络配置", "服务器"]
    },
    {
      id: "3",
      name: "赵六", 
      avatar: "/avatars/03.png",
      department: "硬件部",
      currentLoad: 2,
      maxLoad: 6,
      todayClaimed: 7,
      avgResponseTime: "3.1分钟",
      rating: 4.7,
      isOnline: false,
      skills: ["硬件维修", "设备安装"]
    }
  ]

  const getLoadColor = (current: number, max: number) => {
    const percentage = (current / max) * 100
    if (percentage >= 90) return "text-red-600"
    if (percentage >= 70) return "text-orange-600"
    return "text-green-600"
  }

  const getLoadProgress = (current: number, max: number) => {
    return (current / max) * 100
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-lg">
          <Users className="h-5 w-5" />
          <span>团队动态</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 团队总体统计 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <Zap className="h-4 w-4 text-blue-600 mr-1" />
              <span className="text-sm text-blue-800 dark:text-blue-200">今日抢单</span>
            </div>
            <p className="text-2xl font-bold text-blue-600">
              {teamMembers.reduce((sum, member) => sum + member.todayClaimed, 0)}
            </p>
          </div>
          
          <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <Target className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-sm text-green-800 dark:text-green-200">在线人数</span>
            </div>
            <p className="text-2xl font-bold text-green-600">
              {teamMembers.filter(member => member.isOnline).length}/{teamMembers.length}
            </p>
          </div>
        </div>

        {/* 团队成员列表 */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-muted-foreground">团队成员负载</h4>
          
          {teamMembers.map((member) => (
            <div key={member.id} className="space-y-3 p-3 border rounded-lg">
              {/* 成员基本信息 */}
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={member.avatar} alt={member.name} />
                    <AvatarFallback>{member.name[0]}</AvatarFallback>
                  </Avatar>
                  <div className={`absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-white ${
                    member.isOnline ? 'bg-green-500' : 'bg-gray-400'
                  }`}></div>
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{member.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {member.department}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {member.skills.join(" • ")}
                  </p>
                </div>
                
                <div className="text-right">
                  <div className="flex items-center space-x-1">
                    <Star className="h-3 w-3 text-yellow-500" />
                    <span className="text-xs font-medium">{member.rating}</span>
                  </div>
                </div>
              </div>
              
              {/* 负载进度条 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">当前负载</span>
                  <span className={`font-medium ${getLoadColor(member.currentLoad, member.maxLoad)}`}>
                    {member.currentLoad}/{member.maxLoad}
                  </span>
                </div>
                <Progress 
                  value={getLoadProgress(member.currentLoad, member.maxLoad)} 
                  className="h-2"
                />
              </div>
              
              {/* 统计信息 */}
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div className="flex items-center space-x-1">
                  <Zap className="h-3 w-3 text-blue-500" />
                  <span className="text-muted-foreground">今日抢单:</span>
                  <span className="font-medium">{member.todayClaimed}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3 text-green-500" />
                  <span className="text-muted-foreground">响应时间:</span>
                  <span className="font-medium">{member.avgResponseTime}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 团队排行榜 */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground">今日抢单排行</h4>
          
          <div className="space-y-2">
            {teamMembers
              .sort((a, b) => b.todayClaimed - a.todayClaimed)
              .map((member, index) => (
                <div key={member.id} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                  <div className="flex items-center space-x-2">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                      index === 0 ? 'bg-yellow-500 text-white' :
                      index === 1 ? 'bg-gray-400 text-white' :
                      index === 2 ? 'bg-orange-500 text-white' :
                      'bg-gray-200 text-gray-600'
                    }`}>
                      {index + 1}
                    </div>
                    <span className="text-sm font-medium">{member.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-bold text-blue-600">
                      {member.todayClaimed}
                    </span>
                    <span className="text-xs text-muted-foreground">单</span>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* 团队效率指标 */}
        <div className="space-y-3 p-3 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg">
          <h4 className="text-sm font-medium text-purple-800 dark:text-purple-200">
            团队效率指标
          </h4>
          
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <p className="text-muted-foreground">平均响应时间</p>
              <p className="font-bold text-purple-600">
                {(teamMembers.reduce((sum, member) => 
                  sum + parseFloat(member.avgResponseTime), 0) / teamMembers.length
                ).toFixed(1)}分钟
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">团队平均评分</p>
              <p className="font-bold text-purple-600">
                {(teamMembers.reduce((sum, member) => sum + member.rating, 0) / teamMembers.length).toFixed(1)}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
