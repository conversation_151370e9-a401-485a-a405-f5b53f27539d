"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Eye, 
  Zap, 
  UserPlus, 
  Clock, 
  Star, 
  FileText,
  User,
  MessageSquare,
  Paperclip
} from "lucide-react"

/**
 * 快速预览面板组件
 * 显示选中工单的详细信息，无需跳转页面
 */
export function QuickViewPanel() {
  // 模拟选中的工单数据
  const selectedWorkOrder = {
    id: "WO-2024-001240",
    title: "销售部打印机无法连接网络",
    priority: "紧急",
    type: "IT服务请求",
    customer: { 
      name: "王总", 
      company: "科技有限公司", 
      phone: "138****1234",
      isVip: true 
    },
    description: "销售部打印机显示网络连接错误，影响正常办公。客户反馈打印机无法打印任何文档，已尝试重启设备但问题依然存在。需要技术人员现场排查网络配置问题。",
    tags: ["网络问题", "VIP客户", "硬件故障", "现场服务"],
    createdAt: "2024-01-15 14:30:00",
    createdBy: "张三",
    slaDeadline: "2024-01-15 18:30:00",
    points: 50,
    attachments: ["error_screenshot.png", "network_config.txt"],
    customFields: {
      "影响的业务系统": "OA办公系统",
      "设备资产编号": "PRINTER-205",
      "预约上门时间": "今日16:00-18:00"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "紧急": return "bg-red-500"
      case "高": return "bg-orange-500"
      case "中": return "bg-yellow-500"
      case "低": return "bg-green-500"
      default: return "bg-gray-500"
    }
  }

  const getTimeRemaining = (deadline: string) => {
    const now = new Date()
    const deadlineDate = new Date(deadline)
    const diff = deadlineDate.getTime() - now.getTime()
    
    if (diff <= 0) return "已超时"
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 0) return `剩余${hours}小时${minutes}分钟`
    return `剩余${minutes}分钟`
  }

  const handleClaimTicket = () => {
    console.log("抢单成功:", selectedWorkOrder.id)
  }

  const handleAssignTicket = () => {
    console.log("指派工单:", selectedWorkOrder.id)
  }

  if (!selectedWorkOrder) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Eye className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">
            点击工单卡片查看详细信息
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="sticky top-6">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-lg">
          <Eye className="h-5 w-5" />
          <span>快速预览</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 工单基本信息 */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Badge className={getPriorityColor(selectedWorkOrder.priority)}>
              {selectedWorkOrder.priority}
            </Badge>
            <Badge variant="outline">{selectedWorkOrder.type}</Badge>
          </div>
          
          <h3 className="font-semibold text-gray-900 dark:text-white leading-tight">
            {selectedWorkOrder.title}
          </h3>
          
          <p className="text-sm text-muted-foreground">
            工单ID: {selectedWorkOrder.id}
          </p>
        </div>

        <Separator />

        {/* SLA和积分信息 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">SLA截止时间</span>
            <div className={`text-sm font-medium ${
              getTimeRemaining(selectedWorkOrder.slaDeadline) === "已超时" 
                ? "text-red-600" 
                : "text-orange-600"
            }`}>
              <Clock className="h-4 w-4 inline mr-1" />
              {getTimeRemaining(selectedWorkOrder.slaDeadline)}
            </div>
          </div>
          
          {selectedWorkOrder.points > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">奖励积分</span>
              <div className="text-sm font-medium text-purple-600">
                <Star className="h-4 w-4 inline mr-1" />
                {selectedWorkOrder.points}积分
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* 客户信息 */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium flex items-center space-x-2">
            <User className="h-4 w-4" />
            <span>客户信息</span>
          </h4>
          
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback>{selectedWorkOrder.customer.name[0]}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{selectedWorkOrder.customer.name}</span>
                {selectedWorkOrder.customer.isVip && (
                  <Badge className="bg-yellow-500 text-xs">
                    <Star className="h-3 w-3 mr-1" />
                    VIP
                  </Badge>
                )}
              </div>
              <p className="text-xs text-muted-foreground">{selectedWorkOrder.customer.company}</p>
              <p className="text-xs text-muted-foreground">{selectedWorkOrder.customer.phone}</p>
            </div>
          </div>
        </div>

        <Separator />

        {/* 问题描述 */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium flex items-center space-x-2">
            <FileText className="h-4 w-4" />
            <span>问题描述</span>
          </h4>
          <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
            {selectedWorkOrder.description}
          </p>
        </div>

        {/* 附件 */}
        {selectedWorkOrder.attachments.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="text-sm font-medium flex items-center space-x-2">
                <Paperclip className="h-4 w-4" />
                <span>附件</span>
              </h4>
              <div className="space-y-2">
                {selectedWorkOrder.attachments.map((attachment, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <Paperclip className="h-3 w-3 text-muted-foreground" />
                    <span className="text-blue-600 hover:underline cursor-pointer">
                      {attachment}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* 自定义字段 */}
        <Separator />
        <div className="space-y-3">
          <h4 className="text-sm font-medium">详细信息</h4>
          <div className="space-y-2 text-sm">
            {Object.entries(selectedWorkOrder.customFields).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="text-muted-foreground">{key}</span>
                <span className="font-medium">{value}</span>
              </div>
            ))}
            <div className="flex justify-between">
              <span className="text-muted-foreground">创建人</span>
              <span className="font-medium">{selectedWorkOrder.createdBy}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">创建时间</span>
              <span className="font-medium">
                {new Date(selectedWorkOrder.createdAt).toLocaleString()}
              </span>
            </div>
          </div>
        </div>

        {/* 标签 */}
        <Separator />
        <div className="space-y-3">
          <h4 className="text-sm font-medium">标签</h4>
          <div className="flex flex-wrap gap-2">
            {selectedWorkOrder.tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        {/* 操作按钮 */}
        <Separator />
        <div className="space-y-3">
          <Button onClick={handleClaimTicket} className="w-full">
            <Zap className="h-4 w-4 mr-2" />
            抢单处理
          </Button>
          
          <div className="grid grid-cols-2 gap-2">
            <Button variant="outline" onClick={handleAssignTicket}>
              <UserPlus className="h-4 w-4 mr-2" />
              指派
            </Button>
            <Button variant="outline">
              <MessageSquare className="h-4 w-4 mr-2" />
              讨论
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
