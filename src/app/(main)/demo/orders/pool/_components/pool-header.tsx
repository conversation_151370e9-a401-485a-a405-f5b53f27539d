"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { RefreshCw, Bell, Users, Clock, TrendingUp } from "lucide-react"

/**
 * 工单池页面头部组件
 * 显示实时统计信息和自动刷新功能
 */
export function PoolHeader() {
  const [newTicketsCount, setNewTicketsCount] = useState(0)
  const [lastRefresh, setLastRefresh] = useState(new Date())
  const [autoRefresh, setAutoRefresh] = useState(true)

  // 模拟实时数据
  const poolStats = {
    totalUnassigned: 23,
    urgent: 5,
    myTeamPool: 8,
    avgClaimTime: "3.2分钟"
  }

  // 模拟新工单提醒
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        // 模拟随机新工单
        if (Math.random() > 0.7) {
          setNewTicketsCount(prev => prev + 1)
        }
      }, 10000) // 每10秒检查一次

      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  const handleRefresh = () => {
    setNewTicketsCount(0)
    setLastRefresh(new Date())
  }

  const toggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh)
  }

  return (
    <div className="space-y-4">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            工单池
          </h1>
          <p className="text-muted-foreground mt-2">
            发现并抢取适合您的工单，高效协作处理客户需求
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={toggleAutoRefresh}
            className={autoRefresh ? "border-green-500 text-green-600" : ""}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            {autoRefresh ? "自动刷新" : "手动刷新"}
          </Button>
          
          <Button onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            立即刷新
          </Button>
        </div>
      </div>

      {/* 新工单提醒条 */}
      {newTicketsCount > 0 && (
        <Card className="border-blue-500 bg-blue-50 dark:bg-blue-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Bell className="h-5 w-5 text-blue-600 animate-pulse" />
                <span className="font-medium text-blue-800 dark:text-blue-200">
                  有 {newTicketsCount} 个新工单等待处理
                </span>
              </div>
              <Button onClick={handleRefresh} size="sm">
                点击刷新
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 统计信息卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">公共池待抢单</p>
                <p className="text-2xl font-bold text-blue-600">{poolStats.totalUnassigned}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                <Clock className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">紧急工单</p>
                <p className="text-2xl font-bold text-red-600">{poolStats.urgent}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Users className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">我的团队池</p>
                <p className="text-2xl font-bold text-green-600">{poolStats.myTeamPool}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <TrendingUp className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">平均抢单时间</p>
                <p className="text-2xl font-bold text-purple-600">{poolStats.avgClaimTime}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 最后刷新时间 */}
      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          最后更新：{lastRefresh.toLocaleTimeString()}
          {autoRefresh && " • 自动刷新已开启"}
        </p>
      </div>
    </div>
  )
}
