"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Clock, 
  User, 
  Star, 
  Eye, 
  Zap, 
  MessageSquare,
  UserPlus,
  Grid3X3,
  List,
  Sparkles
} from "lucide-react"

/**
 * 工单列表组件
 * 显示可抢单的工单卡片，支持卡片和列表视图切换
 */
export function WorkOrderList() {
  const [viewMode, setViewMode] = useState<"card" | "list">("card")
  const [selectedTicket, setSelectedTicket] = useState<string | null>(null)
  const [lockedTickets, setLockedTickets] = useState<Record<string, { user: string; timestamp: number }>>({})

  // 模拟工单数据
  const workOrders = [
    {
      id: "WO-2024-001240",
      title: "销售部打印机无法连接网络",
      priority: "紧急",
      type: "IT服务请求",
      customer: { name: "王总", company: "科技有限公司", isVip: true },
      description: "销售部打印机显示网络连接错误，影响正常办公。客户反馈打印机无法打印任何文档...",
      tags: ["网络问题", "VIP客户", "硬件故障"],
      createdAt: "2024-01-15 14:30:00",
      slaDeadline: "2024-01-15 18:30:00",
      points: 50,
      isRecommended: true,
      comments: 2
    },
    {
      id: "WO-2024-001241",
      title: "财务系统登录异常",
      priority: "高",
      type: "IT服务请求", 
      customer: { name: "李经理", company: "制造集团", isVip: false },
      description: "财务系统无法正常登录，提示用户名或密码错误，但确认账号密码无误...",
      tags: ["软件问题", "权限问题"],
      createdAt: "2024-01-15 13:45:00",
      slaDeadline: "2024-01-16 09:45:00",
      points: 30,
      isRecommended: false,
      comments: 0
    },
    {
      id: "WO-2024-001242",
      title: "客户投诉产品质量问题",
      priority: "中",
      type: "客户投诉",
      customer: { name: "张先生", company: "贸易公司", isVip: true },
      description: "客户反馈收到的产品存在质量问题，要求退换货处理...",
      tags: ["VIP客户", "质量问题", "退换货"],
      createdAt: "2024-01-15 12:20:00",
      slaDeadline: "2024-01-16 12:20:00",
      points: 40,
      isRecommended: false,
      comments: 1
    }
  ]

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "紧急": return "bg-red-500"
      case "高": return "bg-orange-500"
      case "中": return "bg-yellow-500"
      case "低": return "bg-green-500"
      default: return "bg-gray-500"
    }
  }

  const getTimeRemaining = (deadline: string) => {
    const now = new Date()
    const deadlineDate = new Date(deadline)
    const diff = deadlineDate.getTime() - now.getTime()
    
    if (diff <= 0) return "已超时"
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 0) return `剩余${hours}小时${minutes}分钟`
    return `剩余${minutes}分钟`
  }

  const handleClaimTicket = (ticketId: string) => {
    // 模拟抢单成功
    console.log("抢单成功:", ticketId)
    // 这里可以添加成功动画和状态更新
  }

  const handlePreviewTicket = (ticketId: string) => {
    setSelectedTicket(ticketId)
    // 模拟锁定机制
    setLockedTickets(prev => ({
      ...prev,
      [ticketId]: { user: "当前用户", timestamp: Date.now() }
    }))
    
    // 15秒后自动解锁
    setTimeout(() => {
      setLockedTickets(prev => {
        const newLocked = { ...prev }
        delete newLocked[ticketId]
        return newLocked
      })
    }, 15000)
  }

  const handleAssignTicket = (ticketId: string) => {
    // 管理者指派功能
    console.log("指派工单:", ticketId)
  }

  const isLocked = (ticketId: string) => {
    const lock = lockedTickets[ticketId]
    if (!lock) return false
    
    // 检查是否超过15秒
    return Date.now() - lock.timestamp < 15000
  }

  const renderWorkOrderCard = (workOrder: any) => (
    <Card 
      key={workOrder.id} 
      className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
        selectedTicket === workOrder.id ? 'ring-2 ring-blue-500' : ''
      } ${isLocked(workOrder.id) ? 'opacity-75' : ''}`}
      onClick={() => handlePreviewTicket(workOrder.id)}
    >
      <CardContent className="p-6">
        {/* 卡片顶部 */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Badge className={getPriorityColor(workOrder.priority)}>
              {workOrder.priority}
            </Badge>
            <Badge variant="outline">{workOrder.type}</Badge>
            {workOrder.isRecommended && (
              <Badge className="bg-purple-500">
                <Sparkles className="h-3 w-3 mr-1" />
                推荐
              </Badge>
            )}
          </div>
          
          <div className="text-right">
            <div className={`text-sm font-medium ${
              getTimeRemaining(workOrder.slaDeadline) === "已超时" 
                ? "text-red-600" 
                : "text-orange-600"
            }`}>
              <Clock className="h-4 w-4 inline mr-1" />
              {getTimeRemaining(workOrder.slaDeadline)}
            </div>
            {workOrder.points > 0 && (
              <div className="text-xs text-purple-600 mt-1">
                <Star className="h-3 w-3 inline mr-1" />
                {workOrder.points}积分
              </div>
            )}
          </div>
        </div>

        {/* 卡片中部 */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white leading-tight">
            {workOrder.title}
          </h3>
          
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarFallback>{workOrder.customer.name[0]}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{workOrder.customer.name}</span>
                {workOrder.customer.isVip && (
                  <Badge className="bg-yellow-500 text-xs">
                    <Star className="h-3 w-3 mr-1" />
                    VIP
                  </Badge>
                )}
              </div>
              <p className="text-xs text-muted-foreground">{workOrder.customer.company}</p>
            </div>
          </div>
          
          <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
            {workOrder.description}
          </p>
        </div>

        {/* 卡片底部 */}
        <div className="flex items-center justify-between mt-4 pt-4 border-t">
          <div className="flex items-center space-x-2">
            <div className="flex flex-wrap gap-1">
              {workOrder.tags.slice(0, 2).map((tag: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {workOrder.tags.length > 2 && (
                <Badge variant="secondary" className="text-xs">
                  +{workOrder.tags.length - 2}
                </Badge>
              )}
            </div>
            
            {workOrder.comments > 0 && (
              <div className="flex items-center text-xs text-muted-foreground">
                <MessageSquare className="h-3 w-3 mr-1" />
                {workOrder.comments}
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-xs text-muted-foreground">
              {new Date(workOrder.createdAt).toLocaleString()}
            </span>
            
            {isLocked(workOrder.id) && (
              <div className="flex items-center text-xs text-orange-600">
                <Eye className="h-3 w-3 mr-1" />
                正在被查看
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2 mt-4" onClick={(e) => e.stopPropagation()}>
          <Button 
            onClick={() => handleClaimTicket(workOrder.id)}
            disabled={isLocked(workOrder.id)}
            className="flex-1"
          >
            <Zap className="h-4 w-4 mr-2" />
            {isLocked(workOrder.id) ? "正在被查看" : "抢单"}
          </Button>
          
          {/* 管理者功能 */}
          <Button 
            variant="outline" 
            onClick={() => handleAssignTicket(workOrder.id)}
            className="px-3"
          >
            <UserPlus className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* 视图切换 */}
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">可抢单工单</h2>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === "card" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("card")}
              >
                <Grid3X3 className="h-4 w-4 mr-2" />
                卡片视图
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4 mr-2" />
                列表视图
              </Button>
            </div>
          </div>

          {/* 工单列表 */}
          {viewMode === "card" ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {workOrders.map(renderWorkOrderCard)}
            </div>
          ) : (
            <div className="space-y-4">
              {workOrders.map(renderWorkOrderCard)}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
