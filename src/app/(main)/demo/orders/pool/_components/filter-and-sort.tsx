"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Filter, 
  SortAsc, 
  Search, 
  Star, 
  Users, 
  Globe,
  Sparkles,
  X
} from "lucide-react"

/**
 * 筛选和排序组件
 * 提供工单池的筛选、排序和搜索功能
 */
export function FilterAndSort() {
  const [activeTab, setActiveTab] = useState("public")
  const [sortBy, setSortBy] = useState("created_time")
  const [selectedFilters, setSelectedFilters] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState("")

  const sortOptions = [
    { value: "created_time", label: "按创建时间 (最新优先)", icon: "🕒" },
    { value: "sla_deadline", label: "按SLA截止时间 (最紧急优先)", icon: "⏰" },
    { value: "priority", label: "按优先级 (最高优先)", icon: "🔥" },
    { value: "points", label: "按积分/奖励 (最高优先)", icon: "⭐" },
    { value: "recommended", label: "为您推荐 (AI智能匹配)", icon: "🤖" }
  ]

  const filterOptions = [
    { id: "it-service", label: "IT服务请求", category: "type" },
    { id: "customer-complaint", label: "客户投诉", category: "type" },
    { id: "purchase", label: "采购申请", category: "type" },
    { id: "urgent", label: "紧急", category: "priority" },
    { id: "high", label: "高", category: "priority" },
    { id: "vip", label: "VIP客户", category: "tag" },
    { id: "network", label: "网络问题", category: "tag" },
    { id: "hardware", label: "硬件故障", category: "tag" }
  ]

  const toggleFilter = (filterId: string) => {
    setSelectedFilters(prev => 
      prev.includes(filterId) 
        ? prev.filter(id => id !== filterId)
        : [...prev, filterId]
    )
  }

  const clearAllFilters = () => {
    setSelectedFilters([])
    setSearchQuery("")
  }

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case "public": return Globe
      case "team": return Users
      case "all": return Star
      case "recommended": return Sparkles
      default: return Globe
    }
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* 标签页 - 池子类型 */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="public" className="flex items-center space-x-2">
                <Globe className="h-4 w-4" />
                <span>公共池/待抢单</span>
              </TabsTrigger>
              <TabsTrigger value="team" className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>我的团队池</span>
              </TabsTrigger>
              <TabsTrigger value="recommended" className="flex items-center space-x-2">
                <Sparkles className="h-4 w-4" />
                <span>为您推荐</span>
              </TabsTrigger>
              <TabsTrigger value="all" className="flex items-center space-x-2">
                <Star className="h-4 w-4" />
                <span>所有池子</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="public" className="mt-4">
              <p className="text-sm text-muted-foreground">
                显示所有未分配到具体个人的工单，这是处理人抢单的主要区域
              </p>
            </TabsContent>
            
            <TabsContent value="team" className="mt-4">
              <p className="text-sm text-muted-foreground">
                显示分配到您所在团队、但尚未分配到个人的工单
              </p>
            </TabsContent>
            
            <TabsContent value="recommended" className="mt-4">
              <div className="flex items-center space-x-2">
                <Sparkles className="h-4 w-4 text-purple-600" />
                <p className="text-sm text-muted-foreground">
                  基于您的技能和历史记录，AI为您智能推荐最适合的工单
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="all" className="mt-4">
              <p className="text-sm text-muted-foreground">
                管理者视图：查看所有团队的工单池情况
              </p>
            </TabsContent>
          </Tabs>

          {/* 搜索和排序 */}
          <div className="flex flex-col lg:flex-row gap-4">
            {/* 搜索框 */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="搜索工单标题、客户名称或描述..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* 排序选择 */}
            <div className="lg:w-80">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SortAsc className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="选择排序方式" />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center space-x-2">
                        <span>{option.icon}</span>
                        <span>{option.label}</span>
                        {option.value === "recommended" && (
                          <Badge variant="secondary" className="text-xs">AI</Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 快速筛选器 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">快速筛选</span>
              </div>
              {selectedFilters.length > 0 && (
                <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                  <X className="h-4 w-4 mr-1" />
                  清除筛选
                </Button>
              )}
            </div>
            
            <div className="flex flex-wrap gap-2">
              {filterOptions.map((filter) => {
                const isSelected = selectedFilters.includes(filter.id)
                return (
                  <Button
                    key={filter.id}
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleFilter(filter.id)}
                    className="text-xs"
                  >
                    {filter.label}
                    {isSelected && (
                      <X className="h-3 w-3 ml-1" />
                    )}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* 当前筛选状态 */}
          {(selectedFilters.length > 0 || searchQuery) && (
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <span>当前筛选：</span>
              {searchQuery && (
                <Badge variant="secondary">
                  搜索: "{searchQuery}"
                </Badge>
              )}
              {selectedFilters.map((filterId) => {
                const filter = filterOptions.find(f => f.id === filterId)
                return filter ? (
                  <Badge key={filterId} variant="secondary">
                    {filter.label}
                  </Badge>
                ) : null
              })}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
