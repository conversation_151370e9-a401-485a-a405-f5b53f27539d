import { PoolHeader } from "./_components/pool-header"
import { FilterAndSort } from "./_components/filter-and-sort"
import { WorkOrderList } from "./_components/work-order-list"
import { QuickViewPanel } from "./_components/quick-view-panel"
import { TeamStats } from "./_components/team-stats"

/**
 * 工单池页面
 * 提供工单抢单功能，支持筛选、排序和快速预览
 */
export default function WorkOrderPoolPage() {
  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <PoolHeader />
      
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 mt-6">
        {/* 主要内容区域 */}
        <div className="xl:col-span-3 space-y-6">
          {/* 筛选和排序 */}
          <FilterAndSort />
          
          {/* 工单列表 */}
          <WorkOrderList />
        </div>
        
        {/* 侧边栏 */}
        <div className="xl:col-span-1 space-y-6">
          {/* 团队统计 */}
          <TeamStats />
          
          {/* 快速预览面板 */}
          <QuickViewPanel />
        </div>
      </div>
    </div>
  )
}
