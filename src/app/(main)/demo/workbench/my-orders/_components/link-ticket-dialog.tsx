"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Link, Search, X, Clock, User, AlertCircle, ArrowRight, ArrowLeftRight } from "lucide-react"
import { 
  getPriorityColor, 
  getPriorityLabel,
  getSlaStatusColor,
  linkTypes,
  mockTicketsForLink,
  commonLinkReasons
} from "./ticket-utils"

interface LinkTicketDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ticket: any
  linkType: string
  selectedTicket: any
  linkDescription: string
  bidirectional: boolean
  searchQuery: string
  isLinking: boolean
  onLinkTypeChange: (type: string) => void
  onSelectedTicketChange: (ticket: any) => void
  onLinkDescriptionChange: (description: string) => void
  onBidirectionalChange: (bidirectional: boolean) => void
  onSearchQueryChange: (query: string) => void
  onConfirm: () => void
}

/**
 * 关联工单对话框组件
 */
export function LinkTicketDialog({
  open,
  onOpenChange,
  ticket,
  linkType,
  selectedTicket,
  linkDescription,
  bidirectional,
  searchQuery,
  isLinking,
  onLinkTypeChange,
  onSelectedTicketChange,
  onLinkDescriptionChange,
  onBidirectionalChange,
  onSearchQueryChange,
  onConfirm
}: LinkTicketDialogProps) {
  if (!ticket) return null

  // 过滤搜索结果
  const filteredTickets = mockTicketsForLink.filter(t => 
    t.id !== ticket.id && // 排除当前工单
    (t.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
     t.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
     t.assignee.toLowerCase().includes(searchQuery.toLowerCase()) ||
     t.department.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const selectedLinkType = linkTypes.find(type => type.value === linkType)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <Link className="h-5 w-5 text-blue-600" />
            <span>关联工单</span>
          </DialogTitle>
          <DialogDescription>
            建立工单之间的关联关系，便于跟踪相关问题和依赖关系。
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 pr-2">
          {/* 当前工单信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <p className="font-medium">当前工单: {ticket.id}</p>
              <Badge variant="outline" className={`${getPriorityColor(ticket.priority)} text-white border-0`}>
                {getPriorityLabel(ticket.priority)}优先级
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">{ticket.title}</p>
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span className={getSlaStatusColor(ticket.slaStatus)}>
                    SLA剩余: {ticket.slaRemaining}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3" />
                  <span>处理人: {ticket.assignee}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 关联类型选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              关联类型 <span className="text-red-500">*</span>
            </Label>
            <Select value={linkType} onValueChange={onLinkTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择关联类型" />
              </SelectTrigger>
              <SelectContent>
                {linkTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className={`${type.color} border text-xs`}>
                        {type.label}
                      </Badge>
                      <span className="text-sm">{type.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 双向关联选项 */}
          {selectedLinkType?.bidirectional && (
            <div className="flex items-center space-x-2">
              <Checkbox
                id="bidirectional"
                checked={bidirectional}
                onCheckedChange={onBidirectionalChange}
              />
              <Label htmlFor="bidirectional" className="text-sm">
                <div className="flex items-center space-x-2">
                  <ArrowLeftRight className="h-4 w-4" />
                  <span>建立双向关联</span>
                </div>
              </Label>
            </div>
          )}

          {/* 工单搜索 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              搜索要关联的工单 <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="输入工单ID、标题、处理人或部门进行搜索..."
                value={searchQuery}
                onChange={(e) => onSearchQueryChange(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* 搜索结果 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">搜索结果</Label>
            <div className="border rounded-lg">
              <ScrollArea className="h-64">
                <div className="p-2 space-y-2">
                  {filteredTickets.length > 0 ? (
                    filteredTickets.map((t) => (
                      <div
                        key={t.id}
                        className={`p-3 rounded border cursor-pointer transition-colors ${
                          selectedTicket?.id === t.id
                            ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20'
                            : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                        }`}
                        onClick={() => onSelectedTicketChange(t)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{t.id}</span>
                            <Badge variant="outline" className={`${getPriorityColor(t.priority)} text-white border-0 text-xs`}>
                              {getPriorityLabel(t.priority)}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {t.status}
                            </Badge>
                          </div>
                          {selectedTicket?.id === t.id && (
                            <div className="text-blue-600">
                              <ArrowRight className="h-4 w-4" />
                            </div>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{t.title}</p>
                        <div className="flex items-center justify-between text-xs">
                          <div className="flex items-center space-x-4">
                            <span>处理人: {t.assignee}</span>
                            <span>部门: {t.department}</span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {t.tags.map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>没有找到匹配的工单</p>
                      <p className="text-xs">请尝试其他搜索关键词</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>

          {/* 关联预览 */}
          {selectedTicket && selectedLinkType && (
            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Link className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">关联预览</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Badge variant="outline" className="bg-white">
                  {ticket.id}
                </Badge>
                <Badge variant="outline" className={`${selectedLinkType.color} border`}>
                  {selectedLinkType.label}
                </Badge>
                <Badge variant="outline" className="bg-white">
                  {selectedTicket.id}
                </Badge>
                {bidirectional && selectedLinkType.bidirectional && (
                  <>
                    <span className="text-muted-foreground">且</span>
                    <Badge variant="outline" className="bg-white">
                      {selectedTicket.id}
                    </Badge>
                    <Badge variant="outline" className={`${selectedLinkType.color} border`}>
                      {selectedLinkType.label}
                    </Badge>
                    <Badge variant="outline" className="bg-white">
                      {ticket.id}
                    </Badge>
                  </>
                )}
              </div>
            </div>
          )}

          {/* 关联描述 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              关联说明 <span className="text-red-500">*</span>
            </Label>
            <Textarea
              placeholder="请说明建立关联的原因和具体关系..."
              value={linkDescription}
              onChange={(e) => onLinkDescriptionChange(e.target.value)}
              rows={3}
              className="resize-none"
            />
          </div>

          {/* 常用关联原因快捷选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">常用关联原因</Label>
            <div className="flex flex-wrap gap-2">
              {commonLinkReasons.map((reason) => (
                <Button
                  key={reason}
                  variant="outline"
                  size="sm"
                  onClick={() => onLinkDescriptionChange(reason)}
                  className="text-xs h-7"
                >
                  {reason}
                </Button>
              ))}
            </div>
          </div>

          {/* 注意事项 */}
          <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
              <div className="text-xs space-y-1">
                <p className="font-medium text-amber-800 dark:text-amber-200">注意事项：</p>
                <ul className="text-amber-700 dark:text-amber-300 space-y-0.5">
                  <li>• 关联关系将影响工单的处理流程和优先级</li>
                  <li>• 依赖关系可能影响工单的完成时间</li>
                  <li>• 双向关联会在两个工单中都显示关联信息</li>
                  <li>• 关联关系建立后可以在工单详情中查看和管理</li>
                  <li>• 请谨慎建立关联，避免创建循环依赖</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex-shrink-0 flex-col sm:flex-row gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLinking}
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            disabled={
              !linkType || 
              !selectedTicket || 
              !linkDescription.trim() || 
              isLinking
            }
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLinking ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                建立关联中...
              </>
            ) : (
              "建立关联"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
