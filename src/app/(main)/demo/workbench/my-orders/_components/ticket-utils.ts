/**
 * 工单相关的工具函数
 */

/**
 * 获取优先级颜色
 */
export const getPriorityColor = (priority: string) => {
  const colors = {
    urgent: "bg-red-500",
    high: "bg-orange-500", 
    medium: "bg-yellow-500",
    low: "bg-green-500"
  }
  return colors[priority as keyof typeof colors] || "bg-gray-500"
}

/**
 * 获取优先级标签
 */
export const getPriorityLabel = (priority: string) => {
  const labels = {
    urgent: "紧急",
    high: "高",
    medium: "中", 
    low: "低"
  }
  return labels[priority as keyof typeof labels] || priority
}

/**
 * 获取状态颜色
 */
export const getStatusColor = (status: string) => {
  const colors = {
    pending: "bg-yellow-500",
    in_progress: "bg-blue-500",
    waiting_customer: "bg-orange-500",
    suspended: "bg-gray-500",
    resolved: "bg-green-500",
    completed: "bg-green-600",
    closed: "bg-gray-700"
  }
  return colors[status as keyof typeof colors] || "bg-gray-500"
}

/**
 * 获取状态标签
 */
export const getStatusLabel = (status: string) => {
  const labels = {
    pending: "待处理",
    in_progress: "处理中",
    waiting_customer: "等待客户",
    suspended: "已挂起", 
    resolved: "已解决",
    completed: "已完成",
    closed: "已关闭"
  }
  return labels[status as keyof typeof labels] || status
}

/**
 * 获取SLA状态颜色
 */
export const getSlaStatusColor = (slaStatus: string) => {
  const colors = {
    normal: "text-green-600",
    warning: "text-yellow-600",
    danger: "text-red-600",
    completed: "text-gray-500"
  }
  return colors[slaStatus as keyof typeof colors] || "text-gray-500"
}

/**
 * 获取客户级别颜色
 */
export const getCustomerLevelColor = (level: string) => {
  const colors = {
    VIP: "bg-purple-100 text-purple-800 border-purple-200",
    企业: "bg-blue-100 text-blue-800 border-blue-200",
    标准: "bg-gray-100 text-gray-800 border-gray-200"
  }
  return colors[level as keyof typeof colors] || "bg-gray-100 text-gray-800 border-gray-200"
}

/**
 * 人员选项数据
 */
export const personnelOptions = {
  IT支持部: ["张工程师", "李工程师", "王工程师", "IT支持组长"],
  网络部: ["网络管理员", "网络工程师", "网络部经理"],
  客服部: ["客服组长", "高级客服", "客服经理"],
  管理层: ["IT经理", "技术总监", "运营总监"]
}

/**
 * 常用退回原因
 */
export const commonRejectReasons = [
  "信息不完整",
  "描述不清楚", 
  "缺少必要附件",
  "不属于我的职责范围",
  "需要客户补充信息",
  "重复工单"
]

/**
 * 常用转办原因
 */
export const commonTransferReasons = [
  "需要专业技术支持",
  "超出我的权限范围", 
  "需要其他部门配合",
  "工作负载过重",
  "专业领域不匹配",
  "需要上级审批"
]

/**
 * 常用协办原因
 */
export const commonCollaborateReasons = [
  "需要技术支持",
  "需要专业意见", 
  "复杂问题需要协助",
  "需要跨部门配合",
  "质量把关",
  "经验分享"
]

/**
 * 协办权限选项
 */
export const collaboratePermissions = [
  { value: "view", label: "查看工单", description: "可以查看工单详情和处理记录" },
  { value: "comment", label: "添加评论", description: "可以添加处理记录和评论" },
  { value: "edit", label: "编辑工单", description: "可以修改工单信息和状态" },
  { value: "assign", label: "分配权限", description: "可以邀请其他人协办" }
]

/**
 * 优先级选项
 */
export const priorityOptions = [
  { value: "urgent", label: "紧急", color: "bg-red-500" },
  { value: "high", label: "高", color: "bg-orange-500" },
  { value: "medium", label: "中", color: "bg-yellow-500" },
  { value: "low", label: "低", color: "bg-green-500" }
]
