/**
 * 工单相关的工具函数
 */

/**
 * 获取优先级颜色
 */
export const getPriorityColor = (priority: string) => {
  const colors = {
    urgent: "bg-red-500",
    high: "bg-orange-500", 
    medium: "bg-yellow-500",
    low: "bg-green-500"
  }
  return colors[priority as keyof typeof colors] || "bg-gray-500"
}

/**
 * 获取优先级标签
 */
export const getPriorityLabel = (priority: string) => {
  const labels = {
    urgent: "紧急",
    high: "高",
    medium: "中", 
    low: "低"
  }
  return labels[priority as keyof typeof labels] || priority
}

/**
 * 获取状态颜色
 */
export const getStatusColor = (status: string) => {
  const colors = {
    pending: "bg-yellow-500",
    in_progress: "bg-blue-500",
    waiting_customer: "bg-orange-500",
    suspended: "bg-gray-500",
    resolved: "bg-green-500",
    completed: "bg-green-600",
    closed: "bg-gray-700"
  }
  return colors[status as keyof typeof colors] || "bg-gray-500"
}

/**
 * 获取状态标签
 */
export const getStatusLabel = (status: string) => {
  const labels = {
    pending: "待处理",
    in_progress: "处理中",
    waiting_customer: "等待客户",
    suspended: "已挂起", 
    resolved: "已解决",
    completed: "已完成",
    closed: "已关闭"
  }
  return labels[status as keyof typeof labels] || status
}

/**
 * 获取SLA状态颜色
 */
export const getSlaStatusColor = (slaStatus: string) => {
  const colors = {
    normal: "text-green-600",
    warning: "text-yellow-600",
    danger: "text-red-600",
    completed: "text-gray-500"
  }
  return colors[slaStatus as keyof typeof colors] || "text-gray-500"
}

/**
 * 获取客户级别颜色
 */
export const getCustomerLevelColor = (level: string) => {
  const colors = {
    VIP: "bg-purple-100 text-purple-800 border-purple-200",
    企业: "bg-blue-100 text-blue-800 border-blue-200",
    标准: "bg-gray-100 text-gray-800 border-gray-200"
  }
  return colors[level as keyof typeof colors] || "bg-gray-100 text-gray-800 border-gray-200"
}

/**
 * 人员选项数据
 */
export const personnelOptions = {
  IT支持部: ["张工程师", "李工程师", "王工程师", "IT支持组长"],
  网络部: ["网络管理员", "网络工程师", "网络部经理"],
  客服部: ["客服组长", "高级客服", "客服经理"],
  管理层: ["IT经理", "技术总监", "运营总监"]
}

/**
 * 常用退回原因
 */
export const commonRejectReasons = [
  "信息不完整",
  "描述不清楚", 
  "缺少必要附件",
  "不属于我的职责范围",
  "需要客户补充信息",
  "重复工单"
]

/**
 * 常用转办原因
 */
export const commonTransferReasons = [
  "需要专业技术支持",
  "超出我的权限范围", 
  "需要其他部门配合",
  "工作负载过重",
  "专业领域不匹配",
  "需要上级审批"
]

/**
 * 常用协办原因
 */
export const commonCollaborateReasons = [
  "需要技术支持",
  "需要专业意见", 
  "复杂问题需要协助",
  "需要跨部门配合",
  "质量把关",
  "经验分享"
]

/**
 * 协办权限选项
 */
export const collaboratePermissions = [
  { value: "view", label: "查看工单", description: "可以查看工单详情和处理记录" },
  { value: "comment", label: "添加评论", description: "可以添加处理记录和评论" },
  { value: "edit", label: "编辑工单", description: "可以修改工单信息和状态" },
  { value: "assign", label: "分配权限", description: "可以邀请其他人协办" }
]

/**
 * 优先级选项
 */
export const priorityOptions = [
  { value: "urgent", label: "紧急", color: "bg-red-500" },
  { value: "high", label: "高", color: "bg-orange-500" },
  { value: "medium", label: "中", color: "bg-yellow-500" },
  { value: "low", label: "低", color: "bg-green-500" }
]

/**
 * 上报/升级类型选项
 */
export const escalationTypes = [
  { value: "technical", label: "技术升级", description: "需要更高级别的技术支持" },
  { value: "management", label: "管理升级", description: "需要管理层介入处理" },
  { value: "urgent", label: "紧急上报", description: "紧急情况需要立即关注" },
  { value: "resource", label: "资源申请", description: "需要额外资源支持" },
  { value: "policy", label: "政策咨询", description: "涉及政策或流程问题" },
  { value: "external", label: "外部协调", description: "需要外部供应商或合作伙伴支持" }
]

/**
 * 上报目标选项
 */
export const escalationTargets = {
  技术升级: ["高级工程师", "技术专家", "架构师", "技术总监"],
  管理升级: ["部门经理", "IT经理", "运营总监", "技术总监"],
  紧急上报: ["值班经理", "应急响应团队", "高级管理层"],
  资源申请: ["资源管理员", "部门经理", "IT经理"],
  政策咨询: ["质量管理员", "流程管理员", "合规专员"],
  外部协调: ["供应商管理员", "合作伙伴联络员", "外部关系经理"]
}

/**
 * 常用上报原因
 */
export const commonEscalationReasons = [
  "技术难度超出当前能力范围",
  "需要更高权限进行操作",
  "涉及系统安全或合规问题",
  "客户要求管理层介入",
  "问题影响范围较大",
  "需要跨部门协调解决",
  "超出SLA时限需要上报",
  "涉及重要客户或VIP客户"
]

/**
 * 延期申请类型
 */
export const extensionTypes = [
  { value: "technical", label: "技术复杂", description: "问题技术复杂度超出预期" },
  { value: "dependency", label: "外部依赖", description: "等待外部系统或第三方响应" },
  { value: "resource", label: "资源不足", description: "缺少必要的人力或技术资源" },
  { value: "scope", label: "需求变更", description: "客户需求发生变化或范围扩大" },
  { value: "testing", label: "测试验证", description: "需要更多时间进行测试和验证" },
  { value: "coordination", label: "协调沟通", description: "需要跨部门或多方协调" },
  { value: "emergency", label: "紧急事件", description: "因其他紧急事件影响处理进度" },
  { value: "other", label: "其他原因", description: "其他不可预见的情况" }
]

/**
 * 延期时长选项
 */
export const extensionDurations = [
  { value: "2h", label: "2小时", hours: 2 },
  { value: "4h", label: "4小时", hours: 4 },
  { value: "8h", label: "8小时", hours: 8 },
  { value: "1d", label: "1天", hours: 24 },
  { value: "2d", label: "2天", hours: 48 },
  { value: "3d", label: "3天", hours: 72 },
  { value: "1w", label: "1周", hours: 168 },
  { value: "custom", label: "自定义", hours: 0 }
]

/**
 * 审批人选项
 */
export const extensionApprovers = [
  "直属主管",
  "部门经理",
  "IT经理",
  "运营总监",
  "质量管理员",
  "SLA管理员"
]

/**
 * 常用延期原因
 */
export const commonExtensionReasons = [
  "问题复杂度超出预期，需要更多时间分析",
  "等待客户提供必要信息或配合测试",
  "需要等待第三方供应商响应",
  "发现问题涉及多个系统，需要协调处理",
  "解决方案需要更多测试验证确保稳定性",
  "客户临时变更需求，需要重新评估",
  "遇到其他紧急事件，影响处理进度",
  "需要等待系统维护窗口期进行操作"
]

/**
 * 挂起申请类型
 */
export const suspensionTypes = [
  { value: "waiting_customer", label: "等待客户", description: "等待客户提供信息或确认" },
  { value: "waiting_vendor", label: "等待供应商", description: "等待第三方供应商响应或支持" },
  { value: "waiting_approval", label: "等待审批", description: "等待管理层或相关部门审批" },
  { value: "waiting_resource", label: "等待资源", description: "等待必要的人力或技术资源" },
  { value: "waiting_maintenance", label: "等待维护窗口", description: "等待系统维护时间窗口" },
  { value: "dependency_blocked", label: "依赖阻塞", description: "被其他工单或项目阻塞" },
  { value: "technical_research", label: "技术调研", description: "需要进行技术调研或方案设计" },
  { value: "other", label: "其他原因", description: "其他需要挂起的情况" }
]

/**
 * 挂起时长选项
 */
export const suspensionDurations = [
  { value: "1d", label: "1天", days: 1 },
  { value: "3d", label: "3天", days: 3 },
  { value: "1w", label: "1周", days: 7 },
  { value: "2w", label: "2周", days: 14 },
  { value: "1m", label: "1个月", days: 30 },
  { value: "indefinite", label: "无限期", days: 0 },
  { value: "custom", label: "自定义", days: 0 }
]

/**
 * 挂起审批人选项
 */
export const suspensionApprovers = [
  "直属主管",
  "部门经理",
  "IT经理",
  "运营总监",
  "质量管理员",
  "项目经理"
]

/**
 * 常用挂起原因
 */
export const commonSuspensionReasons = [
  "等待客户提供详细需求或确认方案",
  "等待第三方供应商技术支持响应",
  "需要等待相关系统升级或维护完成",
  "等待其他依赖工单完成后继续处理",
  "需要等待预算审批或资源分配",
  "等待客户安排测试环境或测试时间",
  "需要进行深入的技术调研和方案设计",
  "等待法务或合规部门审核确认"
]

/**
 * 抄送类型
 */
export const ccTypes = [
  { value: "notify", label: "仅通知", description: "仅通知相关人员，无需特别关注" },
  { value: "attention", label: "需要关注", description: "需要相关人员关注工单进展" },
  { value: "assist", label: "需要协助", description: "可能需要相关人员提供协助" },
  { value: "review", label: "需要审核", description: "需要相关人员审核或确认" },
  { value: "backup", label: "备用处理人", description: "作为备用处理人员" },
  { value: "stakeholder", label: "利益相关者", description: "工单的利益相关者" },
  { value: "supervisor", label: "上级监督", description: "上级领导监督工单处理" },
  { value: "other", label: "其他", description: "其他抄送原因" }
]

/**
 * 抄送权限级别
 */
export const ccPermissions = [
  { value: "view_basic", label: "基本信息", description: "只能查看工单基本信息" },
  { value: "view_details", label: "详细信息", description: "可以查看工单详细信息和处理记录" },
  { value: "view_comments", label: "查看评论", description: "可以查看所有评论和讨论" },
  { value: "add_comments", label: "添加评论", description: "可以添加评论和建议" },
  { value: "view_attachments", label: "查看附件", description: "可以查看和下载附件" },
  { value: "full_access", label: "完全访问", description: "除了处理权限外的完全访问" }
]

/**
 * 可抄送人员列表
 */
export const ccRecipients = [
  { id: "user1", name: "张三", role: "技术经理", department: "技术部" },
  { id: "user2", name: "李四", role: "产品经理", department: "产品部" },
  { id: "user3", name: "王五", role: "运维工程师", department: "运维部" },
  { id: "user4", name: "赵六", role: "测试工程师", department: "测试部" },
  { id: "user5", name: "钱七", role: "项目经理", department: "项目部" },
  { id: "user6", name: "孙八", role: "客户经理", department: "客服部" },
  { id: "user7", name: "周九", role: "质量管理员", department: "质量部" },
  { id: "user8", name: "吴十", role: "安全专员", department: "安全部" },
  { id: "user9", name: "郑一", role: "架构师", department: "技术部" },
  { id: "user10", name: "王二", role: "业务分析师", department: "业务部" }
]

/**
 * 常用抄送原因
 */
export const commonCcReasons = [
  "请相关人员关注此工单的处理进展",
  "需要相关部门配合处理此问题",
  "涉及多个系统，需要相关负责人了解",
  "可能需要相关人员提供技术支持",
  "请上级领导关注此重要工单",
  "需要相关人员审核处理方案",
  "涉及客户重要需求，请相关人员知悉",
  "可能影响其他项目，请相关人员注意"
]

/**
 * 标签类型定义
 */
export const tagTypes = [
  { value: "priority", label: "优先级", color: "bg-red-100 text-red-800 border-red-200" },
  { value: "status", label: "状态", color: "bg-blue-100 text-blue-800 border-blue-200" },
  { value: "category", label: "分类", color: "bg-green-100 text-green-800 border-green-200" },
  { value: "department", label: "部门", color: "bg-purple-100 text-purple-800 border-purple-200" },
  { value: "system", label: "系统", color: "bg-orange-100 text-orange-800 border-orange-200" },
  { value: "customer", label: "客户", color: "bg-pink-100 text-pink-800 border-pink-200" },
  { value: "project", label: "项目", color: "bg-indigo-100 text-indigo-800 border-indigo-200" },
  { value: "custom", label: "自定义", color: "bg-gray-100 text-gray-800 border-gray-200" }
]

/**
 * 预设标签配置
 */
export const presetTags = {
  priority: [
    { name: "紧急", color: "bg-red-100 text-red-800 border-red-200" },
    { name: "重要", color: "bg-orange-100 text-orange-800 border-orange-200" },
    { name: "普通", color: "bg-blue-100 text-blue-800 border-blue-200" },
    { name: "低优先级", color: "bg-gray-100 text-gray-800 border-gray-200" }
  ],
  status: [
    { name: "待处理", color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
    { name: "处理中", color: "bg-blue-100 text-blue-800 border-blue-200" },
    { name: "已完成", color: "bg-green-100 text-green-800 border-green-200" },
    { name: "已关闭", color: "bg-gray-100 text-gray-800 border-gray-200" },
    { name: "已暂停", color: "bg-orange-100 text-orange-800 border-orange-200" }
  ],
  category: [
    { name: "故障报告", color: "bg-red-100 text-red-800 border-red-200" },
    { name: "功能请求", color: "bg-blue-100 text-blue-800 border-blue-200" },
    { name: "技术支持", color: "bg-green-100 text-green-800 border-green-200" },
    { name: "系统维护", color: "bg-purple-100 text-purple-800 border-purple-200" },
    { name: "数据处理", color: "bg-indigo-100 text-indigo-800 border-indigo-200" },
    { name: "用户培训", color: "bg-pink-100 text-pink-800 border-pink-200" }
  ],
  department: [
    { name: "技术部", color: "bg-blue-100 text-blue-800 border-blue-200" },
    { name: "产品部", color: "bg-green-100 text-green-800 border-green-200" },
    { name: "运维部", color: "bg-orange-100 text-orange-800 border-orange-200" },
    { name: "测试部", color: "bg-purple-100 text-purple-800 border-purple-200" },
    { name: "客服部", color: "bg-pink-100 text-pink-800 border-pink-200" },
    { name: "销售部", color: "bg-indigo-100 text-indigo-800 border-indigo-200" }
  ],
  system: [
    { name: "核心系统", color: "bg-red-100 text-red-800 border-red-200" },
    { name: "业务系统", color: "bg-blue-100 text-blue-800 border-blue-200" },
    { name: "支撑系统", color: "bg-green-100 text-green-800 border-green-200" },
    { name: "第三方系统", color: "bg-orange-100 text-orange-800 border-orange-200" },
    { name: "移动端", color: "bg-purple-100 text-purple-800 border-purple-200" },
    { name: "Web端", color: "bg-indigo-100 text-indigo-800 border-indigo-200" }
  ],
  customer: [
    { name: "VIP客户", color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
    { name: "企业客户", color: "bg-blue-100 text-blue-800 border-blue-200" },
    { name: "个人客户", color: "bg-green-100 text-green-800 border-green-200" },
    { name: "内部用户", color: "bg-purple-100 text-purple-800 border-purple-200" }
  ],
  project: [
    { name: "项目A", color: "bg-blue-100 text-blue-800 border-blue-200" },
    { name: "项目B", color: "bg-green-100 text-green-800 border-green-200" },
    { name: "项目C", color: "bg-orange-100 text-orange-800 border-orange-200" },
    { name: "临时项目", color: "bg-gray-100 text-gray-800 border-gray-200" }
  ]
}

/**
 * 标签颜色选项
 */
export const tagColors = [
  { name: "红色", value: "bg-red-100 text-red-800 border-red-200" },
  { name: "橙色", value: "bg-orange-100 text-orange-800 border-orange-200" },
  { name: "黄色", value: "bg-yellow-100 text-yellow-800 border-yellow-200" },
  { name: "绿色", value: "bg-green-100 text-green-800 border-green-200" },
  { name: "蓝色", value: "bg-blue-100 text-blue-800 border-blue-200" },
  { name: "紫色", value: "bg-purple-100 text-purple-800 border-purple-200" },
  { name: "粉色", value: "bg-pink-100 text-pink-800 border-pink-200" },
  { name: "靛色", value: "bg-indigo-100 text-indigo-800 border-indigo-200" },
  { name: "灰色", value: "bg-gray-100 text-gray-800 border-gray-200" }
]

/**
 * 工单关联类型
 */
export const linkTypes = [
  {
    value: "depends_on",
    label: "依赖于",
    description: "当前工单依赖于目标工单完成",
    color: "bg-red-100 text-red-800 border-red-200",
    bidirectional: false
  },
  {
    value: "blocks",
    label: "阻塞",
    description: "当前工单阻塞目标工单进行",
    color: "bg-orange-100 text-orange-800 border-orange-200",
    bidirectional: false
  },
  {
    value: "related_to",
    label: "相关",
    description: "与目标工单相关",
    color: "bg-blue-100 text-blue-800 border-blue-200",
    bidirectional: true
  },
  {
    value: "duplicate_of",
    label: "重复",
    description: "与目标工单重复",
    color: "bg-purple-100 text-purple-800 border-purple-200",
    bidirectional: false
  },
  {
    value: "parent_of",
    label: "父任务",
    description: "当前工单是目标工单的父任务",
    color: "bg-green-100 text-green-800 border-green-200",
    bidirectional: false
  },
  {
    value: "child_of",
    label: "子任务",
    description: "当前工单是目标工单的子任务",
    color: "bg-yellow-100 text-yellow-800 border-yellow-200",
    bidirectional: false
  },
  {
    value: "follows",
    label: "后续",
    description: "当前工单在目标工单之后处理",
    color: "bg-indigo-100 text-indigo-800 border-indigo-200",
    bidirectional: false
  },
  {
    value: "precedes",
    label: "前置",
    description: "当前工单在目标工单之前处理",
    color: "bg-pink-100 text-pink-800 border-pink-200",
    bidirectional: false
  }
]

/**
 * 模拟工单数据用于关联搜索
 */
export const mockTicketsForLink = [
  {
    id: "TK-2024-002",
    title: "用户登录功能优化",
    status: "处理中",
    priority: "high",
    assignee: "张三",
    department: "技术部",
    createdAt: "2024-01-10T09:00:00Z",
    tags: ["功能优化", "用户体验"]
  },
  {
    id: "TK-2024-003",
    title: "数据库性能调优",
    status: "待处理",
    priority: "medium",
    assignee: "李四",
    department: "运维部",
    createdAt: "2024-01-12T14:30:00Z",
    tags: ["性能优化", "数据库"]
  },
  {
    id: "TK-2024-004",
    title: "移动端适配问题",
    status: "已完成",
    priority: "low",
    assignee: "王五",
    department: "前端组",
    createdAt: "2024-01-08T11:15:00Z",
    tags: ["移动端", "UI适配"]
  },
  {
    id: "TK-2024-005",
    title: "API接口文档更新",
    status: "处理中",
    priority: "medium",
    assignee: "赵六",
    department: "技术部",
    createdAt: "2024-01-14T16:45:00Z",
    tags: ["文档", "API"]
  },
  {
    id: "TK-2024-006",
    title: "系统安全漏洞修复",
    status: "紧急",
    priority: "urgent",
    assignee: "钱七",
    department: "安全部",
    createdAt: "2024-01-15T08:20:00Z",
    tags: ["安全", "漏洞修复"]
  },
  {
    id: "TK-2024-007",
    title: "用户反馈处理流程优化",
    status: "待处理",
    priority: "medium",
    assignee: "孙八",
    department: "客服部",
    createdAt: "2024-01-13T13:10:00Z",
    tags: ["流程优化", "客服"]
  },
  {
    id: "TK-2024-008",
    title: "报表功能开发",
    status: "处理中",
    priority: "high",
    assignee: "周九",
    department: "技术部",
    createdAt: "2024-01-11T10:30:00Z",
    tags: ["新功能", "报表"]
  },
  {
    id: "TK-2024-009",
    title: "第三方集成测试",
    status: "测试中",
    priority: "medium",
    assignee: "吴十",
    department: "测试部",
    createdAt: "2024-01-09T15:20:00Z",
    tags: ["集成测试", "第三方"]
  }
]

/**
 * 常用关联原因
 */
export const commonLinkReasons = [
  "这两个工单处理的是同一个系统的相关问题",
  "当前工单需要等待目标工单完成后才能继续",
  "这两个工单是同一个需求的不同部分",
  "目标工单的解决方案可能影响当前工单",
  "这两个工单涉及相同的用户群体或业务场景",
  "当前工单是目标工单的后续优化工作",
  "发现这两个工单描述的是重复问题",
  "这两个工单需要协调处理以避免冲突"
]
