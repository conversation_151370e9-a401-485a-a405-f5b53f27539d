/**
 * 工单相关的工具函数
 */

/**
 * 获取优先级颜色
 */
export const getPriorityColor = (priority: string) => {
  const colors = {
    urgent: "bg-red-500",
    high: "bg-orange-500", 
    medium: "bg-yellow-500",
    low: "bg-green-500"
  }
  return colors[priority as keyof typeof colors] || "bg-gray-500"
}

/**
 * 获取优先级标签
 */
export const getPriorityLabel = (priority: string) => {
  const labels = {
    urgent: "紧急",
    high: "高",
    medium: "中", 
    low: "低"
  }
  return labels[priority as keyof typeof labels] || priority
}

/**
 * 获取状态颜色
 */
export const getStatusColor = (status: string) => {
  const colors = {
    pending: "bg-yellow-500",
    in_progress: "bg-blue-500",
    waiting_customer: "bg-orange-500",
    suspended: "bg-gray-500",
    resolved: "bg-green-500",
    completed: "bg-green-600",
    closed: "bg-gray-700"
  }
  return colors[status as keyof typeof colors] || "bg-gray-500"
}

/**
 * 获取状态标签
 */
export const getStatusLabel = (status: string) => {
  const labels = {
    pending: "待处理",
    in_progress: "处理中",
    waiting_customer: "等待客户",
    suspended: "已挂起", 
    resolved: "已解决",
    completed: "已完成",
    closed: "已关闭"
  }
  return labels[status as keyof typeof labels] || status
}

/**
 * 获取SLA状态颜色
 */
export const getSlaStatusColor = (slaStatus: string) => {
  const colors = {
    normal: "text-green-600",
    warning: "text-yellow-600",
    danger: "text-red-600",
    completed: "text-gray-500"
  }
  return colors[slaStatus as keyof typeof colors] || "text-gray-500"
}

/**
 * 获取客户级别颜色
 */
export const getCustomerLevelColor = (level: string) => {
  const colors = {
    VIP: "bg-purple-100 text-purple-800 border-purple-200",
    企业: "bg-blue-100 text-blue-800 border-blue-200",
    标准: "bg-gray-100 text-gray-800 border-gray-200"
  }
  return colors[level as keyof typeof colors] || "bg-gray-100 text-gray-800 border-gray-200"
}

/**
 * 人员选项数据
 */
export const personnelOptions = {
  IT支持部: ["张工程师", "李工程师", "王工程师", "IT支持组长"],
  网络部: ["网络管理员", "网络工程师", "网络部经理"],
  客服部: ["客服组长", "高级客服", "客服经理"],
  管理层: ["IT经理", "技术总监", "运营总监"]
}

/**
 * 常用退回原因
 */
export const commonRejectReasons = [
  "信息不完整",
  "描述不清楚", 
  "缺少必要附件",
  "不属于我的职责范围",
  "需要客户补充信息",
  "重复工单"
]

/**
 * 常用转办原因
 */
export const commonTransferReasons = [
  "需要专业技术支持",
  "超出我的权限范围", 
  "需要其他部门配合",
  "工作负载过重",
  "专业领域不匹配",
  "需要上级审批"
]

/**
 * 常用协办原因
 */
export const commonCollaborateReasons = [
  "需要技术支持",
  "需要专业意见", 
  "复杂问题需要协助",
  "需要跨部门配合",
  "质量把关",
  "经验分享"
]

/**
 * 协办权限选项
 */
export const collaboratePermissions = [
  { value: "view", label: "查看工单", description: "可以查看工单详情和处理记录" },
  { value: "comment", label: "添加评论", description: "可以添加处理记录和评论" },
  { value: "edit", label: "编辑工单", description: "可以修改工单信息和状态" },
  { value: "assign", label: "分配权限", description: "可以邀请其他人协办" }
]

/**
 * 优先级选项
 */
export const priorityOptions = [
  { value: "urgent", label: "紧急", color: "bg-red-500" },
  { value: "high", label: "高", color: "bg-orange-500" },
  { value: "medium", label: "中", color: "bg-yellow-500" },
  { value: "low", label: "低", color: "bg-green-500" }
]

/**
 * 上报/升级类型选项
 */
export const escalationTypes = [
  { value: "technical", label: "技术升级", description: "需要更高级别的技术支持" },
  { value: "management", label: "管理升级", description: "需要管理层介入处理" },
  { value: "urgent", label: "紧急上报", description: "紧急情况需要立即关注" },
  { value: "resource", label: "资源申请", description: "需要额外资源支持" },
  { value: "policy", label: "政策咨询", description: "涉及政策或流程问题" },
  { value: "external", label: "外部协调", description: "需要外部供应商或合作伙伴支持" }
]

/**
 * 上报目标选项
 */
export const escalationTargets = {
  技术升级: ["高级工程师", "技术专家", "架构师", "技术总监"],
  管理升级: ["部门经理", "IT经理", "运营总监", "技术总监"],
  紧急上报: ["值班经理", "应急响应团队", "高级管理层"],
  资源申请: ["资源管理员", "部门经理", "IT经理"],
  政策咨询: ["质量管理员", "流程管理员", "合规专员"],
  外部协调: ["供应商管理员", "合作伙伴联络员", "外部关系经理"]
}

/**
 * 常用上报原因
 */
export const commonEscalationReasons = [
  "技术难度超出当前能力范围",
  "需要更高权限进行操作",
  "涉及系统安全或合规问题",
  "客户要求管理层介入",
  "问题影响范围较大",
  "需要跨部门协调解决",
  "超出SLA时限需要上报",
  "涉及重要客户或VIP客户"
]

/**
 * 延期申请类型
 */
export const extensionTypes = [
  { value: "technical", label: "技术复杂", description: "问题技术复杂度超出预期" },
  { value: "dependency", label: "外部依赖", description: "等待外部系统或第三方响应" },
  { value: "resource", label: "资源不足", description: "缺少必要的人力或技术资源" },
  { value: "scope", label: "需求变更", description: "客户需求发生变化或范围扩大" },
  { value: "testing", label: "测试验证", description: "需要更多时间进行测试和验证" },
  { value: "coordination", label: "协调沟通", description: "需要跨部门或多方协调" },
  { value: "emergency", label: "紧急事件", description: "因其他紧急事件影响处理进度" },
  { value: "other", label: "其他原因", description: "其他不可预见的情况" }
]

/**
 * 延期时长选项
 */
export const extensionDurations = [
  { value: "2h", label: "2小时", hours: 2 },
  { value: "4h", label: "4小时", hours: 4 },
  { value: "8h", label: "8小时", hours: 8 },
  { value: "1d", label: "1天", hours: 24 },
  { value: "2d", label: "2天", hours: 48 },
  { value: "3d", label: "3天", hours: 72 },
  { value: "1w", label: "1周", hours: 168 },
  { value: "custom", label: "自定义", hours: 0 }
]

/**
 * 审批人选项
 */
export const extensionApprovers = [
  "直属主管",
  "部门经理",
  "IT经理",
  "运营总监",
  "质量管理员",
  "SLA管理员"
]

/**
 * 常用延期原因
 */
export const commonExtensionReasons = [
  "问题复杂度超出预期，需要更多时间分析",
  "等待客户提供必要信息或配合测试",
  "需要等待第三方供应商响应",
  "发现问题涉及多个系统，需要协调处理",
  "解决方案需要更多测试验证确保稳定性",
  "客户临时变更需求，需要重新评估",
  "遇到其他紧急事件，影响处理进度",
  "需要等待系统维护窗口期进行操作"
]

/**
 * 挂起申请类型
 */
export const suspensionTypes = [
  { value: "waiting_customer", label: "等待客户", description: "等待客户提供信息或确认" },
  { value: "waiting_vendor", label: "等待供应商", description: "等待第三方供应商响应或支持" },
  { value: "waiting_approval", label: "等待审批", description: "等待管理层或相关部门审批" },
  { value: "waiting_resource", label: "等待资源", description: "等待必要的人力或技术资源" },
  { value: "waiting_maintenance", label: "等待维护窗口", description: "等待系统维护时间窗口" },
  { value: "dependency_blocked", label: "依赖阻塞", description: "被其他工单或项目阻塞" },
  { value: "technical_research", label: "技术调研", description: "需要进行技术调研或方案设计" },
  { value: "other", label: "其他原因", description: "其他需要挂起的情况" }
]

/**
 * 挂起时长选项
 */
export const suspensionDurations = [
  { value: "1d", label: "1天", days: 1 },
  { value: "3d", label: "3天", days: 3 },
  { value: "1w", label: "1周", days: 7 },
  { value: "2w", label: "2周", days: 14 },
  { value: "1m", label: "1个月", days: 30 },
  { value: "indefinite", label: "无限期", days: 0 },
  { value: "custom", label: "自定义", days: 0 }
]

/**
 * 挂起审批人选项
 */
export const suspensionApprovers = [
  "直属主管",
  "部门经理",
  "IT经理",
  "运营总监",
  "质量管理员",
  "项目经理"
]

/**
 * 常用挂起原因
 */
export const commonSuspensionReasons = [
  "等待客户提供详细需求或确认方案",
  "等待第三方供应商技术支持响应",
  "需要等待相关系统升级或维护完成",
  "等待其他依赖工单完成后继续处理",
  "需要等待预算审批或资源分配",
  "等待客户安排测试环境或测试时间",
  "需要进行深入的技术调研和方案设计",
  "等待法务或合规部门审核确认"
]
