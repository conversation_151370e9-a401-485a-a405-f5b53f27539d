"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { 
  MoreHorizontal, 
  ArrowRight,
  UserPlus,
  ArrowUp,
  RotateCcw,
  Flag,
  Pause,
  MessageSquare,
  Link,
  Plus,
  AlertTriangle,
  Printer
} from "lucide-react"
import { getPriorityColor, getPriorityLabel } from "./ticket-utils"

interface TicketActionsProps {
  ticket: any
  isAccepting: boolean
  isRejecting: boolean
  isTransferring: boolean
  isEscalating: boolean
  isRequestingExtension: boolean
  isRequestingSuspension: boolean
  isSendingCc: boolean
  onAcceptTicket: (ticket: any) => void
  onOpenRejectDialog: (ticket: any) => void
  onOpenTransferDialog: (ticket: any) => void
  onOpenCollaborateDialog: (ticket: any) => void
  onOpenEscalateDialog: (ticket: any) => void
  onOpenExtensionDialog: (ticket: any) => void
  onOpenSuspensionDialog: (ticket: any) => void
  onOpenCcDialog: (ticket: any) => void
  onTicketAction: (action: string, ticket: any) => void
  onPreviewTicket: (ticket: any) => void
}

/**
 * 工单操作组件
 * 根据工单状态显示不同的操作按钮
 */
export function TicketActions({
  ticket,
  isAccepting,
  isRejecting,
  isTransferring,
  isEscalating,
  isRequestingExtension,
  isRequestingSuspension,
  isSendingCc,
  onAcceptTicket,
  onOpenRejectDialog,
  onOpenTransferDialog,
  onOpenCollaborateDialog,
  onOpenEscalateDialog,
  onOpenExtensionDialog,
  onOpenSuspensionDialog,
  onOpenCcDialog,
  onTicketAction,
  onPreviewTicket
}: TicketActionsProps) {
  const { status } = ticket

  /**
   * 渲染快捷操作按钮
   */
  const renderQuickActions = () => {
    if (status === "pending") {
      return (
        <div className="flex items-center space-x-1">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                size="sm"
                disabled={isAccepting}
                className="bg-green-600 hover:bg-green-700"
              >
                {isAccepting ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                    接收中...
                  </>
                ) : (
                  "接收"
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>确认接收工单</AlertDialogTitle>
                <AlertDialogDescription>
                  <div className="space-y-2">
                    <p>您确定要接收以下工单吗？</p>
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                      <p className="font-medium">工单ID: {ticket.id}</p>
                      <p className="text-sm text-muted-foreground mt-1">{ticket.title}</p>
                      <div className="flex items-center space-x-2 mt-2">
                        <div className={`w-3 h-3 rounded-full ${getPriorityColor(ticket.priority)}`}></div>
                        <span className="text-sm">{getPriorityLabel(ticket.priority)}优先级</span>
                        <span className="text-sm text-muted-foreground">
                          SLA剩余: {ticket.slaRemaining}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      接收后，工单状态将变更为"处理中"，您将成为该工单的处理人。
                    </p>
                  </div>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>取消</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => onAcceptTicket(ticket)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  确认接收
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onOpenRejectDialog(ticket)}
            disabled={isRejecting}
            className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
          >
            {isRejecting ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600 mr-2"></div>
                退回中...
              </>
            ) : (
              "退回"
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onOpenTransferDialog(ticket)}
            disabled={isTransferring}
            className="border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
          >
            {isTransferring ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2"></div>
                转办中...
              </>
            ) : (
              "转办"
            )}
          </Button>
        </div>
      )
    }

    if (status === "in_progress") {
      return (
        <div className="flex items-center space-x-1">
          <Button size="sm" onClick={() => onTicketAction("complete", ticket)}>
            办结
          </Button>
          <Button variant="outline" size="sm" onClick={() => onTicketAction("comment", ticket)}>
            补记
          </Button>
        </div>
      )
    }

    if (status === "completed") {
      return (
        <div className="flex items-center space-x-1">
          <Button variant="outline" size="sm" onClick={() => onPreviewTicket(ticket)}>
            查看详情
          </Button>
        </div>
      )
    }

    return null
  }

  /**
   * 渲染更多操作下拉菜单
   */
  const renderMoreActions = () => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {/* 流程流转类操作 */}
          <DropdownMenuItem onClick={() => onOpenTransferDialog(ticket)}>
            <ArrowRight className="h-4 w-4 mr-2" />
            转办
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onOpenCollaborateDialog(ticket)}>
            <UserPlus className="h-4 w-4 mr-2" />
            邀请协办
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onOpenEscalateDialog(ticket)}>
            <ArrowUp className="h-4 w-4 mr-2" />
            上报/升级
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onOpenRejectDialog(ticket)}>
            <RotateCcw className="h-4 w-4 mr-2" />
            退回
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          {/* 状态变更类申请 */}
          <DropdownMenuItem onClick={() => onOpenExtensionDialog(ticket)}>
            <Flag className="h-4 w-4 mr-2" />
            申请延期
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onOpenSuspensionDialog(ticket)}>
            <Pause className="h-4 w-4 mr-2" />
            申请挂起
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onOpenCcDialog(ticket)}>
            <Mail className="h-4 w-4 mr-2" />
            抄送工单
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          {/* 信息与沟通类操作 */}
          <DropdownMenuItem onClick={() => onTicketAction("cc", ticket)}>
            <MessageSquare className="h-4 w-4 mr-2" />
            抄送
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onTicketAction("add_tags", ticket)}>
            <Flag className="h-4 w-4 mr-2" />
            添加/修改标签
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onTicketAction("link_ticket", ticket)}>
            <Link className="h-4 w-4 mr-2" />
            关联工单
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onTicketAction("create_subtask", ticket)}>
            <Plus className="h-4 w-4 mr-2" />
            创建子工单
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          {/* 高级或特殊操作 */}
          <DropdownMenuItem onClick={() => onTicketAction("appeal", ticket)}>
            <AlertTriangle className="h-4 w-4 mr-2" />
            工单申诉
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onTicketAction("print", ticket)}>
            <Printer className="h-4 w-4 mr-2" />
            打印工单
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <div className="flex items-center justify-end space-x-2">
      {renderQuickActions()}
      {renderMoreActions()}
    </div>
  )
}
