"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, Clock, User, ArrowUp } from "lucide-react"
import { 
  getPriorityColor, 
  getPriorityLabel, 
  escalationTypes,
  escalationTargets,
  commonEscalationReasons,
  priorityOptions 
} from "./ticket-utils"

interface EscalateTicketDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ticket: any
  escalationType: string
  escalationTarget: string
  escalationReason: string
  escalationPriority: string
  escalationUrgent: boolean
  isEscalating: boolean
  onEscalationTypeChange: (type: string) => void
  onEscalationTargetChange: (target: string) => void
  onEscalationReasonChange: (reason: string) => void
  onEscalationPriorityChange: (priority: string) => void
  onEscalationUrgentChange: (urgent: boolean) => void
  onConfirm: () => void
}

/**
 * 上报/升级工单对话框组件
 */
export function EscalateTicketDialog({
  open,
  onOpenChange,
  ticket,
  escalationType,
  escalationTarget,
  escalationReason,
  escalationPriority,
  escalationUrgent,
  isEscalating,
  onEscalationTypeChange,
  onEscalationTargetChange,
  onEscalationReasonChange,
  onEscalationPriorityChange,
  onEscalationUrgentChange,
  onConfirm
}: EscalateTicketDialogProps) {
  if (!ticket) return null

  const selectedEscalationType = escalationTypes.find(type => type.value === escalationType)
  const availableTargets = escalationTargets[selectedEscalationType?.label as keyof typeof escalationTargets] || []

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <ArrowUp className="h-5 w-5 text-orange-600" />
            <span>上报/升级工单</span>
          </DialogTitle>
          <DialogDescription>
            将工单上报给更高级别的人员或部门处理，请选择上报类型并填写详细说明。
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 pr-2">
          {/* 工单信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <p className="font-medium">工单ID: {ticket.id}</p>
              <Badge variant="outline" className={`${getPriorityColor(ticket.priority)} text-white border-0`}>
                {getPriorityLabel(ticket.priority)}优先级
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">{ticket.title}</p>
            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Clock className="h-3 w-3" />
                <span>SLA剩余: {ticket.slaRemaining}</span>
              </div>
              <div className="flex items-center space-x-1">
                <User className="h-3 w-3" />
                <span>当前处理人: {ticket.assignee}</span>
              </div>
            </div>
          </div>

          {/* 上报类型 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              上报类型 <span className="text-red-500">*</span>
            </Label>
            <Select value={escalationType} onValueChange={onEscalationTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择上报类型" />
              </SelectTrigger>
              <SelectContent>
                {escalationTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="space-y-1">
                      <div className="font-medium">{type.label}</div>
                      <div className="text-xs text-muted-foreground">{type.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 上报目标 */}
          {selectedEscalationType && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                上报给 <span className="text-red-500">*</span>
              </Label>
              <Select value={escalationTarget} onValueChange={onEscalationTargetChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择上报目标" />
                </SelectTrigger>
                <SelectContent>
                  {availableTargets.map((target) => (
                    <SelectItem key={target} value={target}>
                      {target}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* 上报原因 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              上报原因 <span className="text-red-500">*</span>
            </Label>
            <Textarea
              placeholder="请详细说明需要上报的原因和期望的支持..."
              value={escalationReason}
              onChange={(e) => onEscalationReasonChange(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          {/* 优先级调整 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">优先级调整</Label>
            <Select value={escalationPriority} onValueChange={onEscalationPriorityChange}>
              <SelectTrigger>
                <SelectValue placeholder="保持当前优先级" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="keep_current">保持当前优先级</SelectItem>
                {priorityOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${option.color}`}></div>
                      <span>{option.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 紧急上报标记 */}
          <div className="flex items-center space-x-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <Switch
              checked={escalationUrgent}
              onCheckedChange={onEscalationUrgentChange}
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
                <Label className="text-sm font-medium">标记为紧急上报</Label>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                将添加"紧急上报"标签，接收人会收到特别提醒和通知
              </p>
            </div>
          </div>

          {/* 常用上报原因快捷选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">常用上报原因</Label>
            <div className="flex flex-wrap gap-2">
              {commonEscalationReasons.map((reason) => (
                <Button
                  key={reason}
                  variant="outline"
                  size="sm"
                  onClick={() => onEscalationReasonChange(reason)}
                  className="text-xs h-7"
                >
                  {reason}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter className="flex-shrink-0 flex-col sm:flex-row gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isEscalating}
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            disabled={!escalationType || !escalationTarget || !escalationReason.trim() || isEscalating}
            className="bg-orange-600 hover:bg-orange-700"
          >
            {isEscalating ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                上报中...
              </>
            ) : (
              "确认上报"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
