"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Tag, X, Plus, Clock, User, AlertCircle } from "lucide-react"
import { 
  getPriorityColor, 
  getPriorityLabel,
  getSlaStatusColor,
  tagTypes,
  presetTags,
  tagColors
} from "./ticket-utils"

interface ManageTagsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ticket: any
  currentTags: Array<{ name: string; color: string }>
  newTagName: string
  newTagColor: string
  isUpdating: boolean
  onCurrentTagsChange: (tags: Array<{ name: string; color: string }>) => void
  onNewTagNameChange: (name: string) => void
  onNewTagColorChange: (color: string) => void
  onConfirm: () => void
}

/**
 * 标签管理对话框组件
 */
export function ManageTagsDialog({
  open,
  onOpenChange,
  ticket,
  currentTags,
  newTagName,
  newTagColor,
  isUpdating,
  onCurrentTagsChange,
  onNewTagNameChange,
  onNewTagColorChange,
  onConfirm
}: ManageTagsDialogProps) {
  if (!ticket) return null

  // 添加预设标签
  const addPresetTag = (tagName: string, tagColor: string) => {
    const exists = currentTags.some(tag => tag.name === tagName)
    if (!exists) {
      onCurrentTagsChange([...currentTags, { name: tagName, color: tagColor }])
    }
  }

  // 添加自定义标签
  const addCustomTag = () => {
    if (newTagName.trim()) {
      const exists = currentTags.some(tag => tag.name === newTagName.trim())
      if (!exists) {
        onCurrentTagsChange([...currentTags, { name: newTagName.trim(), color: newTagColor }])
        onNewTagNameChange("")
      }
    }
  }

  // 删除标签
  const removeTag = (tagName: string) => {
    onCurrentTagsChange(currentTags.filter(tag => tag.name !== tagName))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <Tag className="h-5 w-5 text-green-600" />
            <span>管理标签</span>
          </DialogTitle>
          <DialogDescription>
            为工单添加、修改或删除标签，便于分类和管理。
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 pr-2">
          {/* 工单信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <p className="font-medium">工单ID: {ticket.id}</p>
              <Badge variant="outline" className={`${getPriorityColor(ticket.priority)} text-white border-0`}>
                {getPriorityLabel(ticket.priority)}优先级
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">{ticket.title}</p>
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span className={getSlaStatusColor(ticket.slaStatus)}>
                    SLA剩余: {ticket.slaRemaining}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3" />
                  <span>处理人: {ticket.assignee}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 当前标签 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">当前标签</Label>
            <div className="min-h-[60px] border rounded-lg p-3 bg-gray-50 dark:bg-gray-800">
              {currentTags.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {currentTags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className={`${tag.color} border flex items-center space-x-1`}
                    >
                      <span>{tag.name}</span>
                      <button
                        onClick={() => removeTag(tag.name)}
                        className="ml-1 hover:bg-black/10 rounded-full p-0.5"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">暂无标签</p>
              )}
            </div>
          </div>

          {/* 标签管理 */}
          <Tabs defaultValue="preset" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="preset">预设标签</TabsTrigger>
              <TabsTrigger value="custom">自定义标签</TabsTrigger>
            </TabsList>

            {/* 预设标签 */}
            <TabsContent value="preset" className="space-y-4">
              <ScrollArea className="h-64">
                <div className="space-y-4">
                  {tagTypes.map((type) => (
                    <div key={type.value} className="space-y-2">
                      <Label className="text-sm font-medium">{type.label}</Label>
                      <div className="flex flex-wrap gap-2">
                        {presetTags[type.value as keyof typeof presetTags]?.map((tag) => (
                          <Button
                            key={tag.name}
                            variant="outline"
                            size="sm"
                            onClick={() => addPresetTag(tag.name, tag.color)}
                            disabled={currentTags.some(t => t.name === tag.name)}
                            className={`h-7 text-xs ${currentTags.some(t => t.name === tag.name) ? 'opacity-50' : ''}`}
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            {tag.name}
                          </Button>
                        ))}
                      </div>
                      <Separator />
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* 自定义标签 */}
            <TabsContent value="custom" className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">标签名称</Label>
                  <Input
                    placeholder="输入自定义标签名称"
                    value={newTagName}
                    onChange={(e) => onNewTagNameChange(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        addCustomTag()
                      }
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">标签颜色</Label>
                  <div className="flex flex-wrap gap-2">
                    {tagColors.map((color) => (
                      <button
                        key={color.value}
                        onClick={() => onNewTagColorChange(color.value)}
                        className={`w-8 h-8 rounded border-2 ${color.value} ${
                          newTagColor === color.value ? 'ring-2 ring-blue-500' : ''
                        }`}
                        title={color.name}
                      />
                    ))}
                  </div>
                </div>

                {/* 预览 */}
                {newTagName.trim() && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">预览</Label>
                    <Badge variant="outline" className={`${newTagColor} border`}>
                      {newTagName.trim()}
                    </Badge>
                  </div>
                )}

                <Button
                  onClick={addCustomTag}
                  disabled={!newTagName.trim() || currentTags.some(t => t.name === newTagName.trim())}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  添加自定义标签
                </Button>
              </div>
            </TabsContent>
          </Tabs>

          {/* 注意事项 */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-xs space-y-1">
                <p className="font-medium text-blue-800 dark:text-blue-200">使用说明：</p>
                <ul className="text-blue-700 dark:text-blue-300 space-y-0.5">
                  <li>• 标签用于工单分类和快速筛选</li>
                  <li>• 可以添加多个标签，建议不超过10个</li>
                  <li>• 预设标签按类型分组，便于统一管理</li>
                  <li>• 自定义标签可以满足特殊需求</li>
                  <li>• 点击标签上的×可以删除标签</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex-shrink-0 flex-col sm:flex-row gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isUpdating}
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isUpdating}
            className="bg-green-600 hover:bg-green-700"
          >
            {isUpdating ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                更新中...
              </>
            ) : (
              "保存标签"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
