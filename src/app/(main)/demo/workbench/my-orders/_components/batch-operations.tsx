"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  X, 
  Users, 
  Edit, 
  Tag, 
  Merge, 
  Trash2,
  ArrowRight,
  CheckCircle,
  Pause
} from "lucide-react"

interface BatchOperationsProps {
  selectedCount: number
  onClear: () => void
}

/**
 * 批量操作组件
 * 当选中工单时显示批量操作选项
 */
export function BatchOperations({ selectedCount, onClear }: BatchOperationsProps) {
  
  const handleBatchOperation = (operation: string) => {
    console.log(`执行批量操作: ${operation}, 选中数量: ${selectedCount}`)
  }

  return (
    <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Badge variant="default" className="bg-blue-600">
                {selectedCount}
              </Badge>
              <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                个工单已选中
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {/* 批量指派/转办 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBatchOperation("assign")}
                className="bg-white dark:bg-gray-800"
              >
                <Users className="h-4 w-4 mr-2" />
                批量指派
              </Button>
              
              {/* 批量更新状态 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBatchOperation("update_status")}
                className="bg-white dark:bg-gray-800"
              >
                <Edit className="h-4 w-4 mr-2" />
                更新状态
              </Button>
              
              {/* 批量添加标签 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBatchOperation("add_tags")}
                className="bg-white dark:bg-gray-800"
              >
                <Tag className="h-4 w-4 mr-2" />
                添加标签
              </Button>
              
              {/* 批量合并 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBatchOperation("merge")}
                className="bg-white dark:bg-gray-800"
              >
                <Merge className="h-4 w-4 mr-2" />
                合并工单
              </Button>
              
              {/* 更多批量操作 */}
              <div className="flex items-center space-x-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchOperation("transfer")}
                  className="bg-white dark:bg-gray-800"
                >
                  <ArrowRight className="h-4 w-4 mr-2" />
                  批量转办
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchOperation("complete")}
                  className="bg-white dark:bg-gray-800"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  批量办结
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchOperation("suspend")}
                  className="bg-white dark:bg-gray-800"
                >
                  <Pause className="h-4 w-4 mr-2" />
                  批量挂起
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchOperation("delete")}
                  className="bg-white dark:bg-gray-800 text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  批量删除
                </Button>
              </div>
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onClear}
            className="text-blue-600 hover:text-blue-700"
          >
            <X className="h-4 w-4 mr-2" />
            取消选择
          </Button>
        </div>
        
        {/* 操作说明 */}
        <div className="mt-3 text-xs text-blue-700 dark:text-blue-300">
          <p>• 批量操作将应用于所有选中的工单</p>
          <p>• 某些操作可能需要管理员权限</p>
          <p>• 删除操作不可恢复，请谨慎使用</p>
        </div>
      </CardContent>
    </Card>
  )
}
