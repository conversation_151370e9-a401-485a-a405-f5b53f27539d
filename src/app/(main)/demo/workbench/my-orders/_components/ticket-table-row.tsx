"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  TableCell,
  TableRow,
} from "@/components/ui/table"
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Eye } from "lucide-react"
import { 
  getPriorityColor, 
  getPriorityLabel,
  getStatusColor,
  getStatusLabel,
  getSlaStatusColor,
  getCustomerLevelColor
} from "./ticket-utils"
import { TicketActions } from "./ticket-actions"

interface TicketTableRowProps {
  ticket: any
  isSelected: boolean
  isAccepting: boolean
  isRejecting: boolean
  isTransferring: boolean
  isEscalating: boolean
  onSelectTicket: (ticketId: string, selected: boolean) => void
  onPreviewTicket: (ticket: any) => void
  onAcceptTicket: (ticket: any) => void
  onOpenRejectDialog: (ticket: any) => void
  onOpenTransferDialog: (ticket: any) => void
  onOpenCollaborateDialog: (ticket: any) => void
  onOpenEscalateDialog: (ticket: any) => void
  onTicketAction: (action: string, ticket: any) => void
}

/**
 * 工单表格行组件
 */
export function TicketTableRow({
  ticket,
  isSelected,
  isAccepting,
  isRejecting,
  isTransferring,
  isEscalating,
  onSelectTicket,
  onPreviewTicket,
  onAcceptTicket,
  onOpenRejectDialog,
  onOpenTransferDialog,
  onOpenCollaborateDialog,
  onOpenEscalateDialog,
  onTicketAction
}: TicketTableRowProps) {
  return (
    <TableRow className="hover:bg-gray-50 dark:hover:bg-gray-800">
      <TableCell>
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) => onSelectTicket(ticket.id, checked as boolean)}
        />
      </TableCell>
      
      <TableCell>
        <Button
          variant="link"
          className="p-0 h-auto font-mono text-blue-600"
          onClick={() => onPreviewTicket(ticket)}
        >
          {ticket.id}
        </Button>
      </TableCell>
      
      <TableCell>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="link"
              className="p-0 h-auto text-left font-medium max-w-xs truncate"
              onClick={() => onPreviewTicket(ticket)}
            >
              {ticket.title}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p className="max-w-sm">{ticket.title}</p>
          </TooltipContent>
        </Tooltip>
        
        {/* 标签 */}
        <div className="flex flex-wrap gap-1 mt-1">
          {ticket.tags.map((tag: string) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      </TableCell>
      
      <TableCell>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${getStatusColor(ticket.status)}`}></div>
          <span className="text-sm">{getStatusLabel(ticket.status)}</span>
        </div>
      </TableCell>
      
      <TableCell>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${getPriorityColor(ticket.priority)}`}></div>
          <span className="text-sm font-medium">{getPriorityLabel(ticket.priority)}</span>
        </div>
      </TableCell>
      
      <TableCell>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-2 cursor-pointer">
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">
                  {ticket.customer.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">{ticket.customer.name}</p>
                <Badge variant="outline" className={`text-xs ${getCustomerLevelColor(ticket.customer.level)}`}>
                  {ticket.customer.level}
                </Badge>
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <p><strong>客户:</strong> {ticket.customer.name}</p>
              <p><strong>公司:</strong> {ticket.customer.company}</p>
              <p><strong>电话:</strong> {ticket.customer.phone}</p>
              <p><strong>级别:</strong> {ticket.customer.level}</p>
            </div>
          </TooltipContent>
        </Tooltip>
      </TableCell>
      
      <TableCell>
        <span className="text-sm text-muted-foreground">
          {new Date(ticket.createdAt).toLocaleDateString()}
        </span>
      </TableCell>
      
      <TableCell>
        <span className={`text-sm font-medium ${getSlaStatusColor(ticket.slaStatus)}`}>
          {ticket.slaRemaining}
        </span>
      </TableCell>
      
      <TableCell>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-2 cursor-pointer">
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">
                  {ticket.assignee === "我" ? "我" : ticket.assignee.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm">{ticket.assignee}</span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <p><strong>处理人:</strong> {ticket.assignee}</p>
              <p><strong>部门:</strong> IT支持部</p>
              <p><strong>当前负载:</strong> 8个工单</p>
            </div>
          </TooltipContent>
        </Tooltip>
      </TableCell>
      
      <TableCell className="text-right">
        <div className="flex items-center justify-end space-x-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onPreviewTicket(ticket)}
                className="hover:bg-blue-50 hover:text-blue-600"
              >
                <Eye className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>快速预览</p>
            </TooltipContent>
          </Tooltip>

          <TicketActions
            ticket={ticket}
            isAccepting={isAccepting}
            isRejecting={isRejecting}
            isTransferring={isTransferring}
            isEscalating={isEscalating}
            onAcceptTicket={onAcceptTicket}
            onOpenRejectDialog={onOpenRejectDialog}
            onOpenTransferDialog={onOpenTransferDialog}
            onOpenCollaborateDialog={onOpenCollaborateDialog}
            onOpenEscalateDialog={onOpenEscalateDialog}
            onTicketAction={onTicketAction}
            onPreviewTicket={onPreviewTicket}
          />
        </div>
      </TableCell>
    </TableRow>
  )
}
