"use client"

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { getPriorityColor, getPriorityLabel } from "./ticket-utils"

interface AcceptTicketDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ticket: any
  onConfirm: () => void
}

/**
 * 接收工单确认对话框组件
 */
export function AcceptTicketDialog({
  open,
  onOpenChange,
  ticket,
  onConfirm
}: AcceptTicketDialogProps) {
  if (!ticket) return null

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认接收工单</AlertDialogTitle>
          <AlertDialogDescription>
            <div className="space-y-2">
              <p>您确定要接收以下工单吗？</p>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <p className="font-medium">工单ID: {ticket.id}</p>
                <p className="text-sm text-muted-foreground mt-1">{ticket.title}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <div className={`w-3 h-3 rounded-full ${getPriorityColor(ticket.priority)}`}></div>
                  <span className="text-sm">{getPriorityLabel(ticket.priority)}优先级</span>
                  <span className="text-sm text-muted-foreground">
                    SLA剩余: {ticket.slaRemaining}
                  </span>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                接收后，工单状态将变更为"处理中"，您将成为该工单的处理人。
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className="bg-green-600 hover:bg-green-700"
          >
            确认接收
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
