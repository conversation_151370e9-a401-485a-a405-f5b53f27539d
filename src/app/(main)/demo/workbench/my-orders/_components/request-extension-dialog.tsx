"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, AlertCircle, User } from "lucide-react"
import { 
  getPriorityColor, 
  getPriorityLabel,
  getSlaStatusColor,
  extensionTypes,
  extensionDurations,
  extensionApprovers,
  commonExtensionReasons
} from "./ticket-utils"

interface RequestExtensionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ticket: any
  extensionType: string
  extensionDuration: string
  customHours: number
  extensionReason: string
  extensionApprover: string
  isRequesting: boolean
  onExtensionTypeChange: (type: string) => void
  onExtensionDurationChange: (duration: string) => void
  onCustomHoursChange: (hours: number) => void
  onExtensionReasonChange: (reason: string) => void
  onExtensionApproverChange: (approver: string) => void
  onConfirm: () => void
}

/**
 * 申请延期对话框组件
 */
export function RequestExtensionDialog({
  open,
  onOpenChange,
  ticket,
  extensionType,
  extensionDuration,
  customHours,
  extensionReason,
  extensionApprover,
  isRequesting,
  onExtensionTypeChange,
  onExtensionDurationChange,
  onCustomHoursChange,
  onExtensionReasonChange,
  onExtensionApproverChange,
  onConfirm
}: RequestExtensionDialogProps) {
  if (!ticket) return null

  const selectedDuration = extensionDurations.find(d => d.value === extensionDuration)
  const finalHours = extensionDuration === "custom" ? customHours : (selectedDuration?.hours || 0)
  
  // 计算新的截止时间
  const currentDeadline = new Date(ticket.deadline || Date.now() + 24 * 60 * 60 * 1000)
  const newDeadline = new Date(currentDeadline.getTime() + finalHours * 60 * 60 * 1000)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-blue-600" />
            <span>申请延期</span>
          </DialogTitle>
          <DialogDescription>
            申请延长工单的SLA时限，需要填写延期原因并等待审批。
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 工单信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <p className="font-medium">工单ID: {ticket.id}</p>
              <Badge variant="outline" className={`${getPriorityColor(ticket.priority)} text-white border-0`}>
                {getPriorityLabel(ticket.priority)}优先级
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">{ticket.title}</p>
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span className={getSlaStatusColor(ticket.slaStatus)}>
                    SLA剩余: {ticket.slaRemaining}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3" />
                  <span>处理人: {ticket.assignee}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 延期类型 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              延期原因类型 <span className="text-red-500">*</span>
            </Label>
            <Select value={extensionType} onValueChange={onExtensionTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择延期原因类型" />
              </SelectTrigger>
              <SelectContent>
                {extensionTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="space-y-1">
                      <div className="font-medium">{type.label}</div>
                      <div className="text-xs text-muted-foreground">{type.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 延期时长 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              申请延期时长 <span className="text-red-500">*</span>
            </Label>
            <Select value={extensionDuration} onValueChange={onExtensionDurationChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择延期时长" />
              </SelectTrigger>
              <SelectContent>
                {extensionDurations.map((duration) => (
                  <SelectItem key={duration.value} value={duration.value}>
                    {duration.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 自定义时长输入 */}
          {extensionDuration === "custom" && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">自定义延期小时数</Label>
              <Input
                type="number"
                min="1"
                max="720"
                value={customHours}
                onChange={(e) => onCustomHoursChange(parseInt(e.target.value) || 0)}
                placeholder="请输入延期小时数"
              />
            </div>
          )}

          {/* 时间预览 */}
          {finalHours > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">延期后时间预览</span>
              </div>
              <div className="text-xs space-y-1">
                <div>当前截止时间: {currentDeadline.toLocaleString()}</div>
                <div>延期时长: {finalHours}小时</div>
                <div className="font-medium text-blue-600">
                  新截止时间: {newDeadline.toLocaleString()}
                </div>
              </div>
            </div>
          )}

          {/* 审批人 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              审批人 <span className="text-red-500">*</span>
            </Label>
            <Select value={extensionApprover} onValueChange={onExtensionApproverChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择审批人" />
              </SelectTrigger>
              <SelectContent>
                {extensionApprovers.map((approver) => (
                  <SelectItem key={approver} value={approver}>
                    {approver}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 详细原因 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              详细说明 <span className="text-red-500">*</span>
            </Label>
            <Textarea
              placeholder="请详细说明需要延期的具体原因..."
              value={extensionReason}
              onChange={(e) => onExtensionReasonChange(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          {/* 常用延期原因快捷选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">常用延期原因</Label>
            <div className="flex flex-wrap gap-2">
              {commonExtensionReasons.map((reason) => (
                <Button
                  key={reason}
                  variant="outline"
                  size="sm"
                  onClick={() => onExtensionReasonChange(reason)}
                  className="text-xs h-7"
                >
                  {reason}
                </Button>
              ))}
            </div>
          </div>

          {/* 注意事项 */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-xs space-y-1">
                <p className="font-medium text-yellow-800 dark:text-yellow-200">注意事项：</p>
                <ul className="text-yellow-700 dark:text-yellow-300 space-y-0.5">
                  <li>• 延期申请需要审批人确认后才能生效</li>
                  <li>• 申请期间工单状态将变更为"延期审批中"</li>
                  <li>• 建议提前申请，避免超出SLA时限</li>
                  <li>• 频繁申请延期可能影响绩效考核</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isRequesting}
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            disabled={
              !extensionType || 
              !extensionDuration || 
              (extensionDuration === "custom" && customHours <= 0) ||
              !extensionApprover || 
              !extensionReason.trim() || 
              isRequesting
            }
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isRequesting ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                申请中...
              </>
            ) : (
              "提交申请"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
