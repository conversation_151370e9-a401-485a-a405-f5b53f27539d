"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { X } from "lucide-react"

interface TicketFiltersProps {
  filters: {
    status: string[]
    priority: string[]
    dateRange: string
    tags: string[]
    template: string
  }
  onChange: (filters: any) => void
}

/**
 * 工单筛选器组件
 * 提供高级筛选功能
 */
export function TicketFilters({ filters, onChange }: TicketFiltersProps) {
  
  // 状态选项
  const statusOptions = [
    { value: "pending", label: "待处理", color: "bg-yellow-500" },
    { value: "in_progress", label: "处理中", color: "bg-blue-500" },
    { value: "waiting_customer", label: "等待客户", color: "bg-orange-500" },
    { value: "suspended", label: "已挂起", color: "bg-gray-500" },
    { value: "resolved", label: "已解决", color: "bg-green-500" },
    { value: "closed", label: "已关闭", color: "bg-gray-700" }
  ]

  // 优先级选项
  const priorityOptions = [
    { value: "urgent", label: "紧急", color: "bg-red-500" },
    { value: "high", label: "高", color: "bg-orange-500" },
    { value: "medium", label: "中", color: "bg-yellow-500" },
    { value: "low", label: "低", color: "bg-green-500" }
  ]

  // 时间范围选项
  const dateRangeOptions = [
    { value: "today", label: "今天" },
    { value: "yesterday", label: "昨天" },
    { value: "this_week", label: "本周" },
    { value: "last_week", label: "上周" },
    { value: "this_month", label: "本月" },
    { value: "last_month", label: "上月" },
    { value: "this_quarter", label: "本季度" },
    { value: "custom", label: "自定义范围" }
  ]

  // 标签选项
  const tagOptions = [
    "VIP客户", "投诉", "产品BUG", "硬件故障", "软件问题", 
    "网络问题", "权限申请", "紧急", "退款", "升级"
  ]

  // 模板选项
  const templateOptions = [
    "IT设备报修", "客户投诉", "权限申请", "采购申请", 
    "系统故障", "设备维护", "账号申请", "其他"
  ]

  const updateFilter = (key: string, value: any) => {
    onChange({
      ...filters,
      [key]: value
    })
  }

  const addToArrayFilter = (key: string, value: string) => {
    const currentArray = filters[key] as string[]
    if (!currentArray.includes(value)) {
      updateFilter(key, [...currentArray, value])
    }
  }

  const removeFromArrayFilter = (key: string, value: string) => {
    const currentArray = filters[key] as string[]
    updateFilter(key, currentArray.filter(item => item !== value))
  }

  const clearAllFilters = () => {
    onChange({
      status: [],
      priority: [],
      dateRange: "",
      tags: [],
      template: ""
    })
  }

  const hasActiveFilters = () => {
    return filters.status.length > 0 || 
           filters.priority.length > 0 || 
           filters.dateRange !== "" || 
           filters.tags.length > 0 || 
           filters.template !== ""
  }

  return (
    <div className="space-y-4">
      {/* 筛选器网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* 工单状态 */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">工单状态</Label>
          <Select onValueChange={(value) => addToArrayFilter("status", value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${status.color}`}></div>
                    <span>{status.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* 已选择的状态 */}
          {filters.status.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {filters.status.map((status) => {
                const statusInfo = statusOptions.find(s => s.value === status)
                return (
                  <Badge key={status} variant="secondary" className="text-xs">
                    {statusInfo?.label}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFromArrayFilter("status", status)}
                      className="h-auto p-0 ml-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )
              })}
            </div>
          )}
        </div>

        {/* 优先级 */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">优先级</Label>
          <Select onValueChange={(value) => addToArrayFilter("priority", value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择优先级" />
            </SelectTrigger>
            <SelectContent>
              {priorityOptions.map((priority) => (
                <SelectItem key={priority.value} value={priority.value}>
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${priority.color}`}></div>
                    <span>{priority.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* 已选择的优先级 */}
          {filters.priority.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {filters.priority.map((priority) => {
                const priorityInfo = priorityOptions.find(p => p.value === priority)
                return (
                  <Badge key={priority} variant="secondary" className="text-xs">
                    {priorityInfo?.label}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFromArrayFilter("priority", priority)}
                      className="h-auto p-0 ml-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )
              })}
            </div>
          )}
        </div>

        {/* 创建时间 */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">创建时间</Label>
          <Select value={filters.dateRange} onValueChange={(value) => updateFilter("dateRange", value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              {dateRangeOptions.map((range) => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 工单模板 */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">工单模板</Label>
          <Select value={filters.template} onValueChange={(value) => updateFilter("template", value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择模板类型" />
            </SelectTrigger>
            <SelectContent>
              {templateOptions.map((template) => (
                <SelectItem key={template} value={template}>
                  {template}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 工单标签 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">工单标签</Label>
        <div className="flex flex-wrap gap-2">
          {tagOptions.map((tag) => {
            const isSelected = filters.tags.includes(tag)
            return (
              <Button
                key={tag}
                variant={isSelected ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  if (isSelected) {
                    removeFromArrayFilter("tags", tag)
                  } else {
                    addToArrayFilter("tags", tag)
                  }
                }}
                className="h-8 text-xs"
              >
                {tag}
                {isSelected && (
                  <X className="h-3 w-3 ml-1" />
                )}
              </Button>
            )
          })}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="text-sm text-muted-foreground">
          {hasActiveFilters() ? "已应用筛选条件" : "未应用任何筛选条件"}
        </div>
        
        {hasActiveFilters() && (
          <Button variant="outline" onClick={clearAllFilters}>
            <X className="h-4 w-4 mr-2" />
            清除所有筛选
          </Button>
        )}
      </div>
    </div>
  )
}
