"use client"

import { useState } from "react"
import { toast } from "sonner"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { 
  MoreHorizontal, 
  Eye, 
  CheckCircle, 
  XCircle, 
  ArrowRight,
  MessageSquare,
  Pause,
  RotateCcw,
  UserPlus,
  ArrowUp,
  Flag,
  Link,
  Plus,
  AlertTriangle,
  FileText,
  Printer
} from "lucide-react"

interface TicketTableProps {
  tickets: any[]
  selectedTickets: string[]
  onSelectTicket: (ticketId: string, selected: boolean) => void
  onSelectAll: (selected: boolean) => void
  onPreviewTicket: (ticket: any) => void
  onTicketUpdate?: (ticketId: string, updates: any) => void
}

/**
 * 工单表格组件
 * 展示工单列表，支持选择、预览和操作
 */
export function TicketTable({
  tickets,
  selectedTickets,
  onSelectTicket,
  onSelectAll,
  onPreviewTicket,
  onTicketUpdate
}: TicketTableProps) {
  const [acceptingTickets, setAcceptingTickets] = useState<string[]>([])
  const [confirmAcceptTicket, setConfirmAcceptTicket] = useState<any>(null)
  const [rejectingTickets, setRejectingTickets] = useState<string[]>([])
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false)
  const [rejectingTicket, setRejectingTicket] = useState<any>(null)
  const [rejectReason, setRejectReason] = useState("")
  const [rejectTarget, setRejectTarget] = useState("")
  const [transferringTickets, setTransferringTickets] = useState<string[]>([])
  const [transferDialogOpen, setTransferDialogOpen] = useState(false)
  const [transferringTicket, setTransferringTicket] = useState<any>(null)
  const [transferTarget, setTransferTarget] = useState("")
  const [transferReason, setTransferReason] = useState("")
  const [transferPriority, setTransferPriority] = useState("")
  const [transferUrgent, setTransferUrgent] = useState(false)

  const [collaboratingTickets, setCollaboratingTickets] = useState<string[]>([])
  const [collaborateDialogOpen, setCollaborateDialogOpen] = useState(false)
  const [collaboratingTicket, setCollaboratingTicket] = useState<any>(null)
  const [collaborators, setCollaborators] = useState<string[]>([])
  const [collaborateMessage, setCollaborateMessage] = useState("")
  const [collaboratePermissions, setCollaboratePermissions] = useState<string[]>(["view", "comment"])

  const getPriorityColor = (priority: string) => {
    const colors = {
      urgent: "bg-red-500",
      high: "bg-orange-500", 
      medium: "bg-yellow-500",
      low: "bg-green-500"
    }
    return colors[priority] || "bg-gray-500"
  }

  const getPriorityLabel = (priority: string) => {
    const labels = {
      urgent: "紧急",
      high: "高",
      medium: "中", 
      low: "低"
    }
    return labels[priority] || priority
  }

  const getStatusColor = (status: string) => {
    const colors = {
      pending: "bg-yellow-500",
      in_progress: "bg-blue-500",
      waiting_customer: "bg-orange-500",
      suspended: "bg-gray-500",
      resolved: "bg-green-500",
      completed: "bg-green-600",
      closed: "bg-gray-700"
    }
    return colors[status] || "bg-gray-500"
  }

  const getStatusLabel = (status: string) => {
    const labels = {
      pending: "待处理",
      in_progress: "处理中",
      waiting_customer: "等待客户",
      suspended: "已挂起", 
      resolved: "已解决",
      completed: "已完成",
      closed: "已关闭"
    }
    return labels[status] || status
  }

  const getSlaStatusColor = (slaStatus: string) => {
    const colors = {
      normal: "text-green-600",
      warning: "text-yellow-600",
      danger: "text-red-600",
      completed: "text-gray-500"
    }
    return colors[slaStatus] || "text-gray-500"
  }

  const getCustomerLevelColor = (level: string) => {
    const colors = {
      VIP: "bg-purple-100 text-purple-800 border-purple-200",
      企业: "bg-blue-100 text-blue-800 border-blue-200",
      标准: "bg-gray-100 text-gray-800 border-gray-200"
    }
    return colors[level] || "bg-gray-100 text-gray-800 border-gray-200"
  }

  const handleTicketAction = (action: string, ticket: any) => {
    console.log(`执行操作: ${action}`, ticket)
  }

  const handleAcceptTicket = async (ticket: any) => {
    try {
      // 添加到正在处理的列表
      setAcceptingTickets(prev => [...prev, ticket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新工单状态
      const updates = {
        status: "in_progress",
        assignee: "我",
        updatedAt: new Date().toISOString(),
        acceptedAt: new Date().toISOString()
      }

      // 调用父组件的更新函数
      onTicketUpdate?.(ticket.id, updates)

      // 显示成功提示
      toast.success(`工单 ${ticket.id} 已成功接收`, {
        description: "工单状态已更新为处理中，您现在可以开始处理此工单。"
      })

    } catch (error) {
      console.error("接收工单失败:", error)
      toast.error("接收工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      // 从正在处理的列表中移除
      setAcceptingTickets(prev => prev.filter(id => id !== ticket.id))
      setConfirmAcceptTicket(null)
    }
  }

  const isAccepting = (ticketId: string) => {
    return acceptingTickets.includes(ticketId)
  }

  const handleRejectTicket = async () => {
    if (!rejectingTicket || !rejectReason.trim()) {
      toast.error("请填写退回原因")
      return
    }

    try {
      // 添加到正在处理的列表
      setRejectingTickets(prev => [...prev, rejectingTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新工单状态
      const updates = {
        status: "rejected",
        rejectedAt: new Date().toISOString(),
        rejectedBy: "我",
        rejectedReason: rejectReason,
        rejectedTo: rejectTarget || "派单员",
        updatedAt: new Date().toISOString()
      }

      // 调用父组件的更新函数
      onTicketUpdate?.(rejectingTicket.id, updates)

      // 显示成功提示
      toast.success(`工单 ${rejectingTicket.id} 已退回`, {
        description: `已退回给${rejectTarget || "派单员"}，退回原因：${rejectReason}`
      })

      // 重置状态
      setRejectDialogOpen(false)
      setRejectingTicket(null)
      setRejectReason("")
      setRejectTarget("")

    } catch (error) {
      console.error("退回工单失败:", error)
      toast.error("退回工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      // 从正在处理的列表中移除
      setRejectingTickets(prev => prev.filter(id => id !== rejectingTicket?.id))
    }
  }

  const isRejecting = (ticketId: string) => {
    return rejectingTickets.includes(ticketId)
  }

  const openRejectDialog = (ticket: any) => {
    setRejectingTicket(ticket)
    setRejectDialogOpen(true)
    setRejectReason("")
    setRejectTarget("")
  }

  const handleTransferTicket = async () => {
    if (!transferringTicket || !transferTarget.trim()) {
      toast.error("请选择转办目标")
      return
    }

    try {
      // 添加到正在处理的列表
      setTransferringTickets(prev => [...prev, transferringTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新工单状态
      const updates = {
        status: "transferred",
        transferredAt: new Date().toISOString(),
        transferredBy: "我",
        transferredTo: transferTarget,
        transferredReason: transferReason,
        assignee: transferTarget,
        updatedAt: new Date().toISOString(),
        // 如果设置了新优先级，则更新优先级
        ...(transferPriority && transferPriority !== "keep_current" && { priority: transferPriority }),
        // 如果标记为紧急，添加紧急标签
        ...(transferUrgent && {
          tags: [...(transferringTicket.tags || []), "紧急转办"].filter((tag, index, arr) => arr.indexOf(tag) === index)
        })
      }

      // 调用父组件的更新函数
      onTicketUpdate?.(transferringTicket.id, updates)

      // 显示成功提示
      toast.success(`工单 ${transferringTicket.id} 已转办`, {
        description: `已转办给${transferTarget}${transferReason ? `，转办原因：${transferReason}` : ""}`
      })

      // 重置状态
      setTransferDialogOpen(false)
      setTransferringTicket(null)
      setTransferTarget("")
      setTransferReason("")
      setTransferPriority("")
      setTransferUrgent(false)

    } catch (error) {
      console.error("转办工单失败:", error)
      toast.error("转办工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      // 从正在处理的列表中移除
      setTransferringTickets(prev => prev.filter(id => id !== transferringTicket?.id))
    }
  }

  const isTransferring = (ticketId: string) => {
    return transferringTickets.includes(ticketId)
  }

  const openTransferDialog = (ticket: any) => {
    setTransferringTicket(ticket)
    setTransferDialogOpen(true)
    setTransferTarget("")
    setTransferReason("")
    setTransferPriority("")
    setTransferUrgent(false)
  }

  const handleCollaborateTicket = async () => {
    if (!collaboratingTicket || collaborators.length === 0) {
      toast.error("请至少选择一个协办人员")
      return
    }

    try {
      // 添加到正在处理的列表
      setCollaboratingTickets(prev => [...prev, collaboratingTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新工单状态
      const updates = {
        collaborators: [...(collaboratingTicket.collaborators || []), ...collaborators]
          .filter((collaborator, index, arr) => arr.indexOf(collaborator) === index), // 去重
        collaborateHistory: [
          ...(collaboratingTicket.collaborateHistory || []),
          {
            invitedAt: new Date().toISOString(),
            invitedBy: "我",
            invitedUsers: collaborators,
            message: collaborateMessage,
            permissions: collaboratePermissions
          }
        ],
        updatedAt: new Date().toISOString(),
        // 添加协办标签
        tags: [...(collaboratingTicket.tags || []), "协办中"].filter((tag, index, arr) => arr.indexOf(tag) === index)
      }

      // 调用父组件的更新函数
      onTicketUpdate?.(collaboratingTicket.id, updates)

      // 显示成功提示
      toast.success(`已邀请协办人员`, {
        description: `已邀请 ${collaborators.join("、")} 协办处理工单 ${collaboratingTicket.id}`
      })

      // 重置状态
      setCollaborateDialogOpen(false)
      setCollaboratingTicket(null)
      setCollaborators([])
      setCollaborateMessage("")
      setCollaboratePermissions(["view", "comment"])

    } catch (error) {
      console.error("邀请协办失败:", error)
      toast.error("邀请协办失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      // 从正在处理的列表中移除
      setCollaboratingTickets(prev => prev.filter(id => id !== collaboratingTicket?.id))
    }
  }

  const isCollaborating = (ticketId: string) => {
    return collaboratingTickets.includes(ticketId)
  }

  const openCollaborateDialog = (ticket: any) => {
    setCollaboratingTicket(ticket)
    setCollaborateDialogOpen(true)
    setCollaborators([])
    setCollaborateMessage("")
    setCollaboratePermissions(["view", "comment"])
  }

  const addCollaborator = (collaborator: string) => {
    if (!collaborators.includes(collaborator)) {
      setCollaborators(prev => [...prev, collaborator])
    }
  }

  const removeCollaborator = (collaborator: string) => {
    setCollaborators(prev => prev.filter(c => c !== collaborator))
  }

  const togglePermission = (permission: string) => {
    setCollaboratePermissions(prev =>
      prev.includes(permission)
        ? prev.filter(p => p !== permission)
        : [...prev, permission]
    )
  }

  const renderRowActions = (ticket: any) => {
    const { status } = ticket

    // 根据状态显示不同的快捷操作
    if (status === "pending") {
      return (
        <div className="flex items-center space-x-1">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                size="sm"
                disabled={isAccepting(ticket.id)}
                className="bg-green-600 hover:bg-green-700"
              >
                {isAccepting(ticket.id) ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                    接收中...
                  </>
                ) : (
                  "接收"
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>确认接收工单</AlertDialogTitle>
                <AlertDialogDescription>
                  <div className="space-y-2">
                    <p>您确定要接收以下工单吗？</p>
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                      <p className="font-medium">工单ID: {ticket.id}</p>
                      <p className="text-sm text-muted-foreground mt-1">{ticket.title}</p>
                      <div className="flex items-center space-x-2 mt-2">
                        <div className={`w-3 h-3 rounded-full ${getPriorityColor(ticket.priority)}`}></div>
                        <span className="text-sm">{getPriorityLabel(ticket.priority)}优先级</span>
                        <span className="text-sm text-muted-foreground">
                          SLA剩余: {ticket.slaRemaining}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      接收后，工单状态将变更为"处理中"，您将成为该工单的处理人。
                    </p>
                  </div>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>取消</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => handleAcceptTicket(ticket)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  确认接收
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <Button
            variant="outline"
            size="sm"
            onClick={() => openRejectDialog(ticket)}
            disabled={isRejecting(ticket.id)}
            className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
          >
            {isRejecting(ticket.id) ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600 mr-2"></div>
                退回中...
              </>
            ) : (
              "退回"
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => openTransferDialog(ticket)}
            disabled={isTransferring(ticket.id)}
            className="border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
          >
            {isTransferring(ticket.id) ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2"></div>
                转办中...
              </>
            ) : (
              "转办"
            )}
          </Button>
        </div>
      )
    }

    if (status === "in_progress") {
      return (
        <div className="flex items-center space-x-1">
          <Button size="sm" onClick={() => handleTicketAction("complete", ticket)}>
            办结
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleTicketAction("comment", ticket)}>
            补记
          </Button>
        </div>
      )
    }

    if (status === "completed") {
      return (
        <div className="flex items-center space-x-1">
          <Button variant="outline" size="sm" onClick={() => onPreviewTicket(ticket)}>
            查看详情
          </Button>
        </div>
      )
    }

    return null
  }

  const renderMoreActions = (ticket: any) => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {/* 流程流转类操作 */}
          <DropdownMenuItem onClick={() => openTransferDialog(ticket)}>
            <ArrowRight className="h-4 w-4 mr-2" />
            转办
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => openCollaborateDialog(ticket)}>
            <UserPlus className="h-4 w-4 mr-2" />
            邀请协办
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleTicketAction("escalate", ticket)}>
            <ArrowUp className="h-4 w-4 mr-2" />
            上报/升级
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => openRejectDialog(ticket)}>
            <RotateCcw className="h-4 w-4 mr-2" />
            退回
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          {/* 状态变更类申请 */}
          <DropdownMenuItem onClick={() => handleTicketAction("request_extension", ticket)}>
            <Flag className="h-4 w-4 mr-2" />
            申请延期
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleTicketAction("request_suspension", ticket)}>
            <Pause className="h-4 w-4 mr-2" />
            申请挂起
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          {/* 信息与沟通类操作 */}
          <DropdownMenuItem onClick={() => handleTicketAction("cc", ticket)}>
            <MessageSquare className="h-4 w-4 mr-2" />
            抄送
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleTicketAction("add_tags", ticket)}>
            <Flag className="h-4 w-4 mr-2" />
            添加/修改标签
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleTicketAction("link_ticket", ticket)}>
            <Link className="h-4 w-4 mr-2" />
            关联工单
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleTicketAction("create_subtask", ticket)}>
            <Plus className="h-4 w-4 mr-2" />
            创建子工单
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          {/* 高级或特殊操作 */}
          <DropdownMenuItem onClick={() => handleTicketAction("appeal", ticket)}>
            <AlertTriangle className="h-4 w-4 mr-2" />
            工单申诉
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleTicketAction("print", ticket)}>
            <Printer className="h-4 w-4 mr-2" />
            打印工单
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  const allSelected = tickets.length > 0 && selectedTickets.length === tickets.length
  const someSelected = selectedTickets.length > 0 && selectedTickets.length < tickets.length

  return (
    <TooltipProvider>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={allSelected}
                ref={(el) => {
                  if (el) el.indeterminate = someSelected
                }}
                onCheckedChange={onSelectAll}
              />
            </TableHead>
            <TableHead>工单ID</TableHead>
            <TableHead>标题</TableHead>
            <TableHead>当前状态</TableHead>
            <TableHead>优先级</TableHead>
            <TableHead>客户信息</TableHead>
            <TableHead>创建时间</TableHead>
            <TableHead>SLA倒计时</TableHead>
            <TableHead>当前处理人</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tickets.map((ticket) => (
            <TableRow key={ticket.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
              <TableCell>
                <Checkbox
                  checked={selectedTickets.includes(ticket.id)}
                  onCheckedChange={(checked) => onSelectTicket(ticket.id, checked as boolean)}
                />
              </TableCell>
              
              <TableCell>
                <Button
                  variant="link"
                  className="p-0 h-auto font-mono text-blue-600"
                  onClick={() => onPreviewTicket(ticket)}
                >
                  {ticket.id}
                </Button>
              </TableCell>
              
              <TableCell>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="link"
                      className="p-0 h-auto text-left font-medium max-w-xs truncate"
                      onClick={() => onPreviewTicket(ticket)}
                    >
                      {ticket.title}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-sm">{ticket.title}</p>
                  </TooltipContent>
                </Tooltip>
                
                {/* 标签 */}
                <div className="flex flex-wrap gap-1 mt-1">
                  {ticket.tags.map((tag: string) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(ticket.status)}`}></div>
                  <span className="text-sm">{getStatusLabel(ticket.status)}</span>
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${getPriorityColor(ticket.priority)}`}></div>
                  <span className="text-sm font-medium">{getPriorityLabel(ticket.priority)}</span>
                </div>
              </TableCell>
              
              <TableCell>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-2 cursor-pointer">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {ticket.customer.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{ticket.customer.name}</p>
                        <Badge variant="outline" className={`text-xs ${getCustomerLevelColor(ticket.customer.level)}`}>
                          {ticket.customer.level}
                        </Badge>
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="space-y-1">
                      <p><strong>客户:</strong> {ticket.customer.name}</p>
                      <p><strong>公司:</strong> {ticket.customer.company}</p>
                      <p><strong>电话:</strong> {ticket.customer.phone}</p>
                      <p><strong>级别:</strong> {ticket.customer.level}</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TableCell>
              
              <TableCell>
                <span className="text-sm text-muted-foreground">
                  {new Date(ticket.createdAt).toLocaleDateString()}
                </span>
              </TableCell>
              
              <TableCell>
                <span className={`text-sm font-medium ${getSlaStatusColor(ticket.slaStatus)}`}>
                  {ticket.slaRemaining}
                </span>
              </TableCell>
              
              <TableCell>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-2 cursor-pointer">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {ticket.assignee === "我" ? "我" : ticket.assignee.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{ticket.assignee}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="space-y-1">
                      <p><strong>处理人:</strong> {ticket.assignee}</p>
                      <p><strong>部门:</strong> IT支持部</p>
                      <p><strong>当前负载:</strong> 8个工单</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TableCell>
              
              <TableCell className="text-right">
                <div className="flex items-center justify-end space-x-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onPreviewTicket(ticket)}
                        className="hover:bg-blue-50 hover:text-blue-600"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>快速预览</p>
                    </TooltipContent>
                  </Tooltip>

                  {renderRowActions(ticket)}

                  {renderMoreActions(ticket)}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      {tickets.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">没有找到匹配的工单</p>
        </div>
      )}

      {/* 退回工单对话框 */}
      <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>退回工单</DialogTitle>
            <DialogDescription>
              请填写退回原因，工单将被退回给指定人员重新处理。
            </DialogDescription>
          </DialogHeader>

          {rejectingTicket && (
            <div className="space-y-4">
              {/* 工单信息 */}
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <p className="font-medium">工单ID: {rejectingTicket.id}</p>
                <p className="text-sm text-muted-foreground mt-1">{rejectingTicket.title}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <div className={`w-3 h-3 rounded-full ${getPriorityColor(rejectingTicket.priority)}`}></div>
                  <span className="text-sm">{getPriorityLabel(rejectingTicket.priority)}优先级</span>
                </div>
              </div>

              {/* 退回目标 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">退回给</label>
                <Select value={rejectTarget} onValueChange={setRejectTarget}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择退回目标（默认：派单员）" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="派单员">派单员</SelectItem>
                    <SelectItem value="客服组长">客服组长</SelectItem>
                    <SelectItem value="质量管理员">质量管理员</SelectItem>
                    <SelectItem value="系统管理员">系统管理员</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 退回原因 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  退回原因 <span className="text-red-500">*</span>
                </label>
                <Textarea
                  placeholder="请详细说明退回的原因..."
                  value={rejectReason}
                  onChange={(e) => setRejectReason(e.target.value)}
                  rows={4}
                  className="resize-none"
                />
                <p className="text-xs text-muted-foreground">
                  请详细说明退回原因，以便相关人员了解问题并重新处理
                </p>
              </div>

              {/* 常用退回原因快捷选择 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">常用原因</label>
                <div className="flex flex-wrap gap-2">
                  {[
                    "信息不完整",
                    "描述不清楚",
                    "缺少必要附件",
                    "不属于我的职责范围",
                    "需要客户补充信息",
                    "重复工单"
                  ].map((reason) => (
                    <Button
                      key={reason}
                      variant="outline"
                      size="sm"
                      onClick={() => setRejectReason(reason)}
                      className="text-xs h-7"
                    >
                      {reason}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setRejectDialogOpen(false)}
              disabled={rejectingTickets.length > 0}
            >
              取消
            </Button>
            <Button
              onClick={handleRejectTicket}
              disabled={!rejectReason.trim() || rejectingTickets.length > 0}
              className="bg-red-600 hover:bg-red-700"
            >
              {rejectingTickets.length > 0 ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  退回中...
                </>
              ) : (
                "确认退回"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 转办工单对话框 */}
      <Dialog open={transferDialogOpen} onOpenChange={setTransferDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>转办工单</DialogTitle>
            <DialogDescription>
              将工单转办给其他人员处理，请选择转办目标并填写转办说明。
            </DialogDescription>
          </DialogHeader>

          {transferringTicket && (
            <div className="space-y-4">
              {/* 工单信息 */}
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <p className="font-medium">工单ID: {transferringTicket.id}</p>
                <p className="text-sm text-muted-foreground mt-1">{transferringTicket.title}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <div className={`w-3 h-3 rounded-full ${getPriorityColor(transferringTicket.priority)}`}></div>
                  <span className="text-sm">{getPriorityLabel(transferringTicket.priority)}优先级</span>
                  <span className="text-sm text-muted-foreground">
                    SLA剩余: {transferringTicket.slaRemaining}
                  </span>
                </div>
              </div>

              {/* 转办目标 */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  转办给 <span className="text-red-500">*</span>
                </Label>
                <Select value={transferTarget} onValueChange={setTransferTarget}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择转办目标" />
                  </SelectTrigger>
                  <SelectContent>
                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                      IT支持部
                    </div>
                    <SelectItem value="张工程师">张工程师</SelectItem>
                    <SelectItem value="李工程师">李工程师</SelectItem>
                    <SelectItem value="王工程师">王工程师</SelectItem>
                    <SelectItem value="IT支持组">IT支持组</SelectItem>

                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                      网络部
                    </div>
                    <SelectItem value="网络管理员">网络管理员</SelectItem>
                    <SelectItem value="网络工程师">网络工程师</SelectItem>

                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                      客服部
                    </div>
                    <SelectItem value="客服组长">客服组长</SelectItem>
                    <SelectItem value="高级客服">高级客服</SelectItem>

                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                      管理层
                    </div>
                    <SelectItem value="IT经理">IT经理</SelectItem>
                    <SelectItem value="技术总监">技术总监</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 转办原因 */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">转办说明</Label>
                <Textarea
                  placeholder="请说明转办原因或需要协助的内容..."
                  value={transferReason}
                  onChange={(e) => setTransferReason(e.target.value)}
                  rows={3}
                  className="resize-none"
                />
              </div>

              {/* 优先级调整 */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">优先级调整</Label>
                <Select value={transferPriority} onValueChange={setTransferPriority}>
                  <SelectTrigger>
                    <SelectValue placeholder="保持当前优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="keep_current">保持当前优先级</SelectItem>
                    <SelectItem value="urgent">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-red-500"></div>
                        <span>紧急</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="high">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                        <span>高</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="medium">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                        <span>中</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="low">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <span>低</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 紧急转办标记 */}
              <div className="flex items-center space-x-3">
                <Switch
                  checked={transferUrgent}
                  onCheckedChange={setTransferUrgent}
                />
                <div>
                  <Label className="text-sm font-medium">标记为紧急转办</Label>
                  <p className="text-xs text-muted-foreground">
                    将添加"紧急转办"标签，接收人会收到特别提醒
                  </p>
                </div>
              </div>

              {/* 常用转办原因快捷选择 */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">常用转办原因</Label>
                <div className="flex flex-wrap gap-2">
                  {[
                    "需要专业技术支持",
                    "超出我的权限范围",
                    "需要其他部门配合",
                    "工作负载过重",
                    "专业领域不匹配",
                    "需要上级审批"
                  ].map((reason) => (
                    <Button
                      key={reason}
                      variant="outline"
                      size="sm"
                      onClick={() => setTransferReason(reason)}
                      className="text-xs h-7"
                    >
                      {reason}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setTransferDialogOpen(false)}
              disabled={transferringTickets.length > 0}
            >
              取消
            </Button>
            <Button
              onClick={handleTransferTicket}
              disabled={!transferTarget.trim() || transferringTickets.length > 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {transferringTickets.length > 0 ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  转办中...
                </>
              ) : (
                "确认转办"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 邀请协办对话框 */}
      <Dialog open={collaborateDialogOpen} onOpenChange={setCollaborateDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>邀请协办</DialogTitle>
            <DialogDescription>
              邀请其他人员协助处理此工单，您仍然是主要处理人。
            </DialogDescription>
          </DialogHeader>

          {collaboratingTicket && (
            <div className="space-y-4">
              {/* 工单信息 */}
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <p className="font-medium">工单ID: {collaboratingTicket.id}</p>
                <p className="text-sm text-muted-foreground mt-1">{collaboratingTicket.title}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <div className={`w-3 h-3 rounded-full ${getPriorityColor(collaboratingTicket.priority)}`}></div>
                  <span className="text-sm">{getPriorityLabel(collaboratingTicket.priority)}优先级</span>
                  <span className="text-sm text-muted-foreground">
                    SLA剩余: {collaboratingTicket.slaRemaining}
                  </span>
                </div>
              </div>

              {/* 已选择的协办人员 */}
              {collaborators.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">已选择的协办人员</Label>
                  <div className="flex flex-wrap gap-2">
                    {collaborators.map((collaborator) => (
                      <Badge key={collaborator} variant="secondary" className="flex items-center space-x-1">
                        <span>{collaborator}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCollaborator(collaborator)}
                          className="h-auto p-0 ml-1"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* 选择协办人员 */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  选择协办人员 <span className="text-red-500">*</span>
                </Label>
                <Select onValueChange={addCollaborator}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择协办人员" />
                  </SelectTrigger>
                  <SelectContent>
                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                      IT支持部
                    </div>
                    {["张工程师", "李工程师", "王工程师", "IT支持组长"]
                      .filter(person => !collaborators.includes(person))
                      .map((person) => (
                        <SelectItem key={person} value={person}>
                          {person}
                        </SelectItem>
                      ))}

                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                      网络部
                    </div>
                    {["网络管理员", "网络工程师", "网络部经理"]
                      .filter(person => !collaborators.includes(person))
                      .map((person) => (
                        <SelectItem key={person} value={person}>
                          {person}
                        </SelectItem>
                      ))}

                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                      客服部
                    </div>
                    {["客服组长", "高级客服", "客服经理"]
                      .filter(person => !collaborators.includes(person))
                      .map((person) => (
                        <SelectItem key={person} value={person}>
                          {person}
                        </SelectItem>
                      ))}

                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                      管理层
                    </div>
                    {["IT经理", "技术总监", "运营总监"]
                      .filter(person => !collaborators.includes(person))
                      .map((person) => (
                        <SelectItem key={person} value={person}>
                          {person}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 协办权限设置 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">协办权限</Label>
                <div className="space-y-2">
                  {[
                    { value: "view", label: "查看工单", description: "可以查看工单详情和处理记录" },
                    { value: "comment", label: "添加评论", description: "可以添加处理记录和评论" },
                    { value: "edit", label: "编辑工单", description: "可以修改工单信息和状态" },
                    { value: "assign", label: "分配权限", description: "可以邀请其他人协办" }
                  ].map((permission) => (
                    <div key={permission.value} className="flex items-start space-x-3">
                      <Checkbox
                        checked={collaboratePermissions.includes(permission.value)}
                        onCheckedChange={() => togglePermission(permission.value)}
                        disabled={permission.value === "view"} // 查看权限必选
                      />
                      <div className="flex-1">
                        <Label className="text-sm font-medium">{permission.label}</Label>
                        <p className="text-xs text-muted-foreground">{permission.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 邀请消息 */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">邀请消息</Label>
                <Textarea
                  placeholder="请说明需要协办的内容或期望的协助..."
                  value={collaborateMessage}
                  onChange={(e) => setCollaborateMessage(e.target.value)}
                  rows={3}
                  className="resize-none"
                />
              </div>

              {/* 常用邀请原因快捷选择 */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">常用邀请原因</Label>
                <div className="flex flex-wrap gap-2">
                  {[
                    "需要技术支持",
                    "需要专业意见",
                    "复杂问题需要协助",
                    "需要跨部门配合",
                    "质量把关",
                    "经验分享"
                  ].map((reason) => (
                    <Button
                      key={reason}
                      variant="outline"
                      size="sm"
                      onClick={() => setCollaborateMessage(reason)}
                      className="text-xs h-7"
                    >
                      {reason}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setCollaborateDialogOpen(false)}
              disabled={collaboratingTickets.length > 0}
            >
              取消
            </Button>
            <Button
              onClick={handleCollaborateTicket}
              disabled={collaborators.length === 0 || collaboratingTickets.length > 0}
              className="bg-green-600 hover:bg-green-700"
            >
              {collaboratingTickets.length > 0 ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  邀请中...
                </>
              ) : (
                "发送邀请"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  )
}
