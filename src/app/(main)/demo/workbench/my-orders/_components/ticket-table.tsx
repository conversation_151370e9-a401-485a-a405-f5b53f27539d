"use client"

import { Checkbox } from "@/components/ui/checkbox"
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  TooltipProvider,
} from "@/components/ui/tooltip"
import { FileText } from "lucide-react"
import { useTicketActions } from "./use-ticket-actions"
import { TicketTableRow } from "./ticket-table-row"
import { RejectTicketDialog } from "./reject-ticket-dialog"
import { TransferTicketDialog } from "./transfer-ticket-dialog"
import { CollaborateTicketDialog } from "./collaborate-ticket-dialog"
import { EscalateTicketDialog } from "./escalate-ticket-dialog"
import { RequestExtensionDialog } from "./request-extension-dialog"
import { RequestSuspensionDialog } from "./request-suspension-dialog"
import { CcTicketDialog } from "./cc-ticket-dialog"
import { ManageTagsDialog } from "./manage-tags-dialog"

interface TicketTableProps {
  tickets: any[]
  selectedTickets: string[]
  onSelectTicket: (ticketId: string, selected: boolean) => void
  onSelectAll: (selected: boolean) => void
  onPreviewTicket: (ticket: any) => void
  onTicketUpdate?: (ticketId: string, updates: any) => void
}

/**
 * 工单表格组件
 * 展示工单列表，支持选择、预览和操作
 */
export function TicketTable({
  tickets,
  selectedTickets,
  onSelectTicket,
  onSelectAll,
  onPreviewTicket,
  onTicketUpdate
}: TicketTableProps) {
  // 使用自定义Hook管理所有状态和操作
  const ticketActions = useTicketActions(onTicketUpdate)

  const handleTicketAction = (action: string, ticket: any) => {
    console.log(`执行操作: ${action}`, ticket)
  }

  const allSelected = tickets.length > 0 && selectedTickets.length === tickets.length
  const someSelected = selectedTickets.length > 0 && selectedTickets.length < tickets.length

  return (
    <TooltipProvider>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={allSelected}
                ref={(el) => {
                  if (el) (el as any).indeterminate = someSelected
                }}
                onCheckedChange={onSelectAll}
              />
            </TableHead>
            <TableHead>工单ID</TableHead>
            <TableHead>标题</TableHead>
            <TableHead>当前状态</TableHead>
            <TableHead>优先级</TableHead>
            <TableHead>客户信息</TableHead>
            <TableHead>创建时间</TableHead>
            <TableHead>SLA倒计时</TableHead>
            <TableHead>当前处理人</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tickets.map((ticket) => (
            <TicketTableRow
              key={ticket.id}
              ticket={ticket}
              isSelected={selectedTickets.includes(ticket.id)}
              isAccepting={ticketActions.isAccepting(ticket.id)}
              isRejecting={ticketActions.isRejecting(ticket.id)}
              isTransferring={ticketActions.isTransferring(ticket.id)}
              isEscalating={ticketActions.isEscalating(ticket.id)}
              isRequestingExtension={ticketActions.isRequestingExtension(ticket.id)}
              isRequestingSuspension={ticketActions.isRequestingSuspension(ticket.id)}
              isSendingCc={ticketActions.isSendingCc(ticket.id)}
              isUpdatingTags={ticketActions.isUpdatingTags(ticket.id)}
              onSelectTicket={onSelectTicket}
              onPreviewTicket={onPreviewTicket}
              onAcceptTicket={ticketActions.handleAcceptTicket}
              onOpenRejectDialog={ticketActions.openRejectDialog}
              onOpenTransferDialog={ticketActions.openTransferDialog}
              onOpenCollaborateDialog={ticketActions.openCollaborateDialog}
              onOpenEscalateDialog={ticketActions.openEscalateDialog}
              onOpenExtensionDialog={ticketActions.openExtensionDialog}
              onOpenSuspensionDialog={ticketActions.openSuspensionDialog}
              onOpenCcDialog={ticketActions.openCcDialog}
              onOpenTagsDialog={ticketActions.openTagsDialog}
              onTicketAction={handleTicketAction}
            />
          ))}
        </TableBody>
      </Table>

      {tickets.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">没有找到匹配的工单</p>
        </div>
      )}

      {/* 对话框组件 */}
      <RejectTicketDialog
        open={ticketActions.rejectDialogOpen}
        onOpenChange={ticketActions.setRejectDialogOpen}
        ticket={ticketActions.rejectingTicket}
        rejectReason={ticketActions.rejectReason}
        rejectTarget={ticketActions.rejectTarget}
        isRejecting={ticketActions.isRejecting(ticketActions.rejectingTicket?.id || "")}
        onRejectReasonChange={ticketActions.setRejectReason}
        onRejectTargetChange={ticketActions.setRejectTarget}
        onConfirm={ticketActions.handleRejectTicket}
      />

      <TransferTicketDialog
        open={ticketActions.transferDialogOpen}
        onOpenChange={ticketActions.setTransferDialogOpen}
        ticket={ticketActions.transferringTicket}
        transferTarget={ticketActions.transferTarget}
        transferReason={ticketActions.transferReason}
        transferPriority={ticketActions.transferPriority}
        transferUrgent={ticketActions.transferUrgent}
        isTransferring={ticketActions.isTransferring(ticketActions.transferringTicket?.id || "")}
        onTransferTargetChange={ticketActions.setTransferTarget}
        onTransferReasonChange={ticketActions.setTransferReason}
        onTransferPriorityChange={ticketActions.setTransferPriority}
        onTransferUrgentChange={ticketActions.setTransferUrgent}
        onConfirm={ticketActions.handleTransferTicket}
      />

      <CollaborateTicketDialog
        open={ticketActions.collaborateDialogOpen}
        onOpenChange={ticketActions.setCollaborateDialogOpen}
        ticket={ticketActions.collaboratingTicket}
        collaborators={ticketActions.collaborators}
        collaborateMessage={ticketActions.collaborateMessage}
        collaboratePermissions={ticketActions.collaboratePermissions}
        isCollaborating={ticketActions.isCollaborating(ticketActions.collaboratingTicket?.id || "")}
        onCollaborateMessageChange={ticketActions.setCollaborateMessage}
        onAddCollaborator={ticketActions.addCollaborator}
        onRemoveCollaborator={ticketActions.removeCollaborator}
        onTogglePermission={ticketActions.togglePermission}
        onConfirm={ticketActions.handleCollaborateTicket}
      />

      <EscalateTicketDialog
        open={ticketActions.escalateDialogOpen}
        onOpenChange={ticketActions.setEscalateDialogOpen}
        ticket={ticketActions.escalatingTicket}
        escalationType={ticketActions.escalationType}
        escalationTarget={ticketActions.escalationTarget}
        escalationReason={ticketActions.escalationReason}
        escalationPriority={ticketActions.escalationPriority}
        escalationUrgent={ticketActions.escalationUrgent}
        isEscalating={ticketActions.isEscalating(ticketActions.escalatingTicket?.id || "")}
        onEscalationTypeChange={ticketActions.setEscalationType}
        onEscalationTargetChange={ticketActions.setEscalationTarget}
        onEscalationReasonChange={ticketActions.setEscalationReason}
        onEscalationPriorityChange={ticketActions.setEscalationPriority}
        onEscalationUrgentChange={ticketActions.setEscalationUrgent}
        onConfirm={ticketActions.handleEscalateTicket}
      />

      <RequestExtensionDialog
        open={ticketActions.extensionDialogOpen}
        onOpenChange={ticketActions.setExtensionDialogOpen}
        ticket={ticketActions.extensionTicket}
        extensionType={ticketActions.extensionType}
        extensionDuration={ticketActions.extensionDuration}
        customHours={ticketActions.customHours}
        extensionReason={ticketActions.extensionReason}
        extensionApprover={ticketActions.extensionApprover}
        isRequesting={ticketActions.isRequestingExtension(ticketActions.extensionTicket?.id || "")}
        onExtensionTypeChange={ticketActions.setExtensionType}
        onExtensionDurationChange={ticketActions.setExtensionDuration}
        onCustomHoursChange={ticketActions.setCustomHours}
        onExtensionReasonChange={ticketActions.setExtensionReason}
        onExtensionApproverChange={ticketActions.setExtensionApprover}
        onConfirm={ticketActions.handleRequestExtension}
      />

      {/* 申请挂起对话框 */}
      <RequestSuspensionDialog
        open={ticketActions.suspensionDialogOpen}
        onOpenChange={ticketActions.setSuspensionDialogOpen}
        ticket={ticketActions.suspensionTicket}
        suspensionType={ticketActions.suspensionType}
        suspensionDuration={ticketActions.suspensionDuration}
        customDays={ticketActions.customDays}
        suspensionReason={ticketActions.suspensionReason}
        suspensionApprover={ticketActions.suspensionApprover}
        isRequesting={ticketActions.isRequestingSuspension(ticketActions.suspensionTicket?.id || "")}
        onSuspensionTypeChange={ticketActions.setSuspensionType}
        onSuspensionDurationChange={ticketActions.setSuspensionDuration}
        onCustomDaysChange={ticketActions.setCustomDays}
        onSuspensionReasonChange={ticketActions.setSuspensionReason}
        onSuspensionApproverChange={ticketActions.setSuspensionApprover}
        onConfirm={ticketActions.handleRequestSuspension}
      />

      {/* 抄送工单对话框 */}
      <CcTicketDialog
        open={ticketActions.ccDialogOpen}
        onOpenChange={ticketActions.setCcDialogOpen}
        ticket={ticketActions.ccTicket}
        ccType={ticketActions.ccType}
        ccRecipients={ticketActions.ccRecipients}
        ccMessage={ticketActions.ccMessage}
        ccPermissions={ticketActions.ccPermissions}
        isSending={ticketActions.isSendingCc(ticketActions.ccTicket?.id || "")}
        onCcTypeChange={ticketActions.setCcType}
        onCcRecipientsChange={ticketActions.setCcRecipients}
        onCcMessageChange={ticketActions.setCcMessage}
        onCcPermissionsChange={ticketActions.setCcPermissions}
        onConfirm={ticketActions.handleCcTicket}
      />

      {/* 标签管理对话框 */}
      <ManageTagsDialog
        open={ticketActions.tagsDialogOpen}
        onOpenChange={ticketActions.setTagsDialogOpen}
        ticket={ticketActions.tagsTicket}
        currentTags={ticketActions.currentTags}
        newTagName={ticketActions.newTagName}
        newTagColor={ticketActions.newTagColor}
        isUpdating={ticketActions.isUpdatingTags(ticketActions.tagsTicket?.id || "")}
        onCurrentTagsChange={ticketActions.setCurrentTags}
        onNewTagNameChange={ticketActions.setNewTagName}
        onNewTagColorChange={ticketActions.setNewTagColor}
        onConfirm={ticketActions.handleManageTags}
      />
    </TooltipProvider>
  )
}