"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { 
  getPriorityColor, 
  getPriorityLabel, 
  personnelOptions, 
  commonTransferReasons,
  priorityOptions 
} from "./ticket-utils"

interface TransferTicketDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ticket: any
  transferTarget: string
  transferReason: string
  transferPriority: string
  transferUrgent: boolean
  isTransferring: boolean
  onTransferTargetChange: (target: string) => void
  onTransferReasonChange: (reason: string) => void
  onTransferPriorityChange: (priority: string) => void
  onTransferUrgentChange: (urgent: boolean) => void
  onConfirm: () => void
}

/**
 * 转办工单对话框组件
 */
export function TransferTicketDialog({
  open,
  onOpenChange,
  ticket,
  transferTarget,
  transferReason,
  transferPriority,
  transferUrgent,
  isTransferring,
  onTransferTargetChange,
  onTransferReasonChange,
  onTransferPriorityChange,
  onTransferUrgentChange,
  onConfirm
}: TransferTicketDialogProps) {
  if (!ticket) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>转办工单</DialogTitle>
          <DialogDescription>
            将工单转办给其他人员处理，请选择转办目标并填写转办说明。
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 工单信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <p className="font-medium">工单ID: {ticket.id}</p>
            <p className="text-sm text-muted-foreground mt-1">{ticket.title}</p>
            <div className="flex items-center space-x-2 mt-2">
              <div className={`w-3 h-3 rounded-full ${getPriorityColor(ticket.priority)}`}></div>
              <span className="text-sm">{getPriorityLabel(ticket.priority)}优先级</span>
              <span className="text-sm text-muted-foreground">
                SLA剩余: {ticket.slaRemaining}
              </span>
            </div>
          </div>

          {/* 转办目标 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              转办给 <span className="text-red-500">*</span>
            </Label>
            <Select value={transferTarget} onValueChange={onTransferTargetChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择转办目标" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(personnelOptions).map(([department, people]) => (
                  <div key={department}>
                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                      {department}
                    </div>
                    {people.map((person) => (
                      <SelectItem key={person} value={person}>
                        {person}
                      </SelectItem>
                    ))}
                  </div>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 转办原因 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">转办说明</Label>
            <Textarea
              placeholder="请说明转办原因或需要协助的内容..."
              value={transferReason}
              onChange={(e) => onTransferReasonChange(e.target.value)}
              rows={3}
              className="resize-none"
            />
          </div>

          {/* 优先级调整 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">优先级调整</Label>
            <Select value={transferPriority} onValueChange={onTransferPriorityChange}>
              <SelectTrigger>
                <SelectValue placeholder="保持当前优先级" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="keep_current">保持当前优先级</SelectItem>
                {priorityOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${option.color}`}></div>
                      <span>{option.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 紧急转办标记 */}
          <div className="flex items-center space-x-3">
            <Switch
              checked={transferUrgent}
              onCheckedChange={onTransferUrgentChange}
            />
            <div>
              <Label className="text-sm font-medium">标记为紧急转办</Label>
              <p className="text-xs text-muted-foreground">
                将添加"紧急转办"标签，接收人会收到特别提醒
              </p>
            </div>
          </div>

          {/* 常用转办原因快捷选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">常用转办原因</Label>
            <div className="flex flex-wrap gap-2">
              {commonTransferReasons.map((reason) => (
                <Button
                  key={reason}
                  variant="outline"
                  size="sm"
                  onClick={() => onTransferReasonChange(reason)}
                  className="text-xs h-7"
                >
                  {reason}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isTransferring}
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            disabled={!transferTarget.trim() || isTransferring}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isTransferring ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                转办中...
              </>
            ) : (
              "确认转办"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
