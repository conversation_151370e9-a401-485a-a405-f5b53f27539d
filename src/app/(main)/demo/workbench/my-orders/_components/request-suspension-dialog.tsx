"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Pause, Calendar, AlertCircle, User, Clock } from "lucide-react"
import { 
  getPriorityColor, 
  getPriorityLabel,
  getSlaStatusColor,
  suspensionTypes,
  suspensionDurations,
  suspensionApprovers,
  commonSuspensionReasons
} from "./ticket-utils"

interface RequestSuspensionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ticket: any
  suspensionType: string
  suspensionDuration: string
  customDays: number
  suspensionReason: string
  suspensionApprover: string
  isRequesting: boolean
  onSuspensionTypeChange: (type: string) => void
  onSuspensionDurationChange: (duration: string) => void
  onCustomDaysChange: (days: number) => void
  onSuspensionReasonChange: (reason: string) => void
  onSuspensionApproverChange: (approver: string) => void
  onConfirm: () => void
}

/**
 * 申请挂起对话框组件
 */
export function RequestSuspensionDialog({
  open,
  onOpenChange,
  ticket,
  suspensionType,
  suspensionDuration,
  customDays,
  suspensionReason,
  suspensionApprover,
  isRequesting,
  onSuspensionTypeChange,
  onSuspensionDurationChange,
  onCustomDaysChange,
  onSuspensionReasonChange,
  onSuspensionApproverChange,
  onConfirm
}: RequestSuspensionDialogProps) {
  if (!ticket) return null

  const selectedDuration = suspensionDurations.find(d => d.value === suspensionDuration)
  const finalDays = suspensionDuration === "custom" ? customDays : (selectedDuration?.days || 0)
  
  // 计算预计恢复时间
  const currentDate = new Date()
  const resumeDate = suspensionDuration === "indefinite" 
    ? null 
    : new Date(currentDate.getTime() + finalDays * 24 * 60 * 60 * 1000)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <Pause className="h-5 w-5 text-yellow-600" />
            <span>申请挂起</span>
          </DialogTitle>
          <DialogDescription>
            申请暂时挂起工单处理，需要填写挂起原因并等待审批。
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 pr-2">
          {/* 工单信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <p className="font-medium">工单ID: {ticket.id}</p>
              <Badge variant="outline" className={`${getPriorityColor(ticket.priority)} text-white border-0`}>
                {getPriorityLabel(ticket.priority)}优先级
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">{ticket.title}</p>
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span className={getSlaStatusColor(ticket.slaStatus)}>
                    SLA剩余: {ticket.slaRemaining}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3" />
                  <span>处理人: {ticket.assignee}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 挂起类型 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              挂起原因类型 <span className="text-red-500">*</span>
            </Label>
            <Select value={suspensionType} onValueChange={onSuspensionTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择挂起原因类型" />
              </SelectTrigger>
              <SelectContent>
                {suspensionTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="space-y-1">
                      <div className="font-medium">{type.label}</div>
                      <div className="text-xs text-muted-foreground">{type.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 挂起时长 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              预计挂起时长 <span className="text-red-500">*</span>
            </Label>
            <Select value={suspensionDuration} onValueChange={onSuspensionDurationChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择挂起时长" />
              </SelectTrigger>
              <SelectContent>
                {suspensionDurations.map((duration) => (
                  <SelectItem key={duration.value} value={duration.value}>
                    {duration.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 自定义天数输入 */}
          {suspensionDuration === "custom" && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">自定义挂起天数</Label>
              <Input
                type="number"
                min="1"
                max="365"
                value={customDays}
                onChange={(e) => onCustomDaysChange(parseInt(e.target.value) || 0)}
                placeholder="请输入挂起天数"
              />
            </div>
          )}

          {/* 时间预览 */}
          {suspensionDuration && suspensionDuration !== "indefinite" && finalDays > 0 && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium">挂起时间预览</span>
              </div>
              <div className="text-xs space-y-1">
                <div>挂起开始时间: {currentDate.toLocaleString()}</div>
                <div>预计挂起时长: {finalDays}天</div>
                {resumeDate && (
                  <div className="font-medium text-yellow-600">
                    预计恢复时间: {resumeDate.toLocaleString()}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 无限期挂起提示 */}
          {suspensionDuration === "indefinite" && (
            <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-800 dark:text-orange-200">
                  无限期挂起
                </span>
              </div>
              <p className="text-xs text-orange-700 dark:text-orange-300 mt-1">
                工单将被无限期挂起，需要手动恢复处理
              </p>
            </div>
          )}

          {/* 审批人 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              审批人 <span className="text-red-500">*</span>
            </Label>
            <Select value={suspensionApprover} onValueChange={onSuspensionApproverChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择审批人" />
              </SelectTrigger>
              <SelectContent>
                {suspensionApprovers.map((approver) => (
                  <SelectItem key={approver} value={approver}>
                    {approver}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 详细原因 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              详细说明 <span className="text-red-500">*</span>
            </Label>
            <Textarea
              placeholder="请详细说明需要挂起的具体原因和预期解决方案..."
              value={suspensionReason}
              onChange={(e) => onSuspensionReasonChange(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          {/* 常用挂起原因快捷选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">常用挂起原因</Label>
            <div className="flex flex-wrap gap-2">
              {commonSuspensionReasons.map((reason) => (
                <Button
                  key={reason}
                  variant="outline"
                  size="sm"
                  onClick={() => onSuspensionReasonChange(reason)}
                  className="text-xs h-7"
                >
                  {reason}
                </Button>
              ))}
            </div>
          </div>

          {/* 注意事项 */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-xs space-y-1">
                <p className="font-medium text-blue-800 dark:text-blue-200">注意事项：</p>
                <ul className="text-blue-700 dark:text-blue-300 space-y-0.5">
                  <li>• 挂起申请需要审批人确认后才能生效</li>
                  <li>• 挂起期间工单状态将变更为"已挂起"</li>
                  <li>• SLA计时将在挂起期间暂停</li>
                  <li>• 挂起期满后需要手动恢复处理</li>
                  <li>• 频繁申请挂起可能影响绩效考核</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex-shrink-0 flex-col sm:flex-row gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isRequesting}
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            disabled={
              !suspensionType || 
              !suspensionDuration || 
              (suspensionDuration === "custom" && customDays <= 0) ||
              !suspensionApprover || 
              !suspensionReason.trim() || 
              isRequesting
            }
            className="bg-yellow-600 hover:bg-yellow-700"
          >
            {isRequesting ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                申请中...
              </>
            ) : (
              "提交申请"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
