"use client"

import { useState } from "react"
import { toast } from "sonner"

/**
 * 工单操作相关的自定义Hook
 * 管理工单的接收、退回、转办、协办等操作的状态和逻辑
 */
export function useTicketActions(onTicketUpdate?: (ticketId: string, updates: any) => void) {
  // 接收工单相关状态
  const [acceptingTickets, setAcceptingTickets] = useState<string[]>([])
  const [confirmAcceptTicket, setConfirmAcceptTicket] = useState<any>(null)

  // 退回工单相关状态
  const [rejectingTickets, setRejectingTickets] = useState<string[]>([])
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false)
  const [rejectingTicket, setRejectingTicket] = useState<any>(null)
  const [rejectReason, setRejectReason] = useState("")
  const [rejectTarget, setRejectTarget] = useState("")

  // 转办工单相关状态
  const [transferringTickets, setTransferringTickets] = useState<string[]>([])
  const [transferDialogOpen, setTransferDialogOpen] = useState(false)
  const [transferringTicket, setTransferringTicket] = useState<any>(null)
  const [transferTarget, setTransferTarget] = useState("")
  const [transferReason, setTransferReason] = useState("")
  const [transferPriority, setTransferPriority] = useState("")
  const [transferUrgent, setTransferUrgent] = useState(false)

  // 协办工单相关状态
  const [collaboratingTickets, setCollaboratingTickets] = useState<string[]>([])
  const [collaborateDialogOpen, setCollaborateDialogOpen] = useState(false)
  const [collaboratingTicket, setCollaboratingTicket] = useState<any>(null)
  const [collaborators, setCollaborators] = useState<string[]>([])
  const [collaborateMessage, setCollaborateMessage] = useState("")
  const [collaboratePermissions, setCollaboratePermissions] = useState<string[]>(["view", "comment"])

  // 上报/升级工单相关状态
  const [escalatingTickets, setEscalatingTickets] = useState<string[]>([])
  const [escalateDialogOpen, setEscalateDialogOpen] = useState(false)
  const [escalatingTicket, setEscalatingTicket] = useState<any>(null)
  const [escalationType, setEscalationType] = useState("")
  const [escalationTarget, setEscalationTarget] = useState("")
  const [escalationReason, setEscalationReason] = useState("")
  const [escalationPriority, setEscalationPriority] = useState("")
  const [escalationUrgent, setEscalationUrgent] = useState(false)

  // 申请延期相关状态
  const [requestingExtensionTickets, setRequestingExtensionTickets] = useState<string[]>([])
  const [extensionDialogOpen, setExtensionDialogOpen] = useState(false)
  const [extensionTicket, setExtensionTicket] = useState<any>(null)
  const [extensionType, setExtensionType] = useState("")
  const [extensionDuration, setExtensionDuration] = useState("")
  const [customHours, setCustomHours] = useState(0)
  const [extensionReason, setExtensionReason] = useState("")
  const [extensionApprover, setExtensionApprover] = useState("")

  // 申请挂起相关状态
  const [requestingSuspensionTickets, setRequestingSuspensionTickets] = useState<string[]>([])
  const [suspensionDialogOpen, setSuspensionDialogOpen] = useState(false)
  const [suspensionTicket, setSuspensionTicket] = useState<any>(null)
  const [suspensionType, setSuspensionType] = useState("")
  const [suspensionDuration, setSuspensionDuration] = useState("")
  const [customDays, setCustomDays] = useState(0)
  const [suspensionReason, setSuspensionReason] = useState("")
  const [suspensionApprover, setSuspensionApprover] = useState("")

  // 抄送相关状态
  const [sendingCcTickets, setSendingCcTickets] = useState<string[]>([])
  const [ccDialogOpen, setCcDialogOpen] = useState(false)
  const [ccTicket, setCcTicket] = useState<any>(null)
  const [ccType, setCcType] = useState("")
  const [ccRecipients, setCcRecipients] = useState<string[]>([])
  const [ccMessage, setCcMessage] = useState("")
  const [ccPermissions, setCcPermissions] = useState<string[]>(["view_basic"])

  // 标签管理相关状态
  const [updatingTagsTickets, setUpdatingTagsTickets] = useState<string[]>([])
  const [tagsDialogOpen, setTagsDialogOpen] = useState(false)
  const [tagsTicket, setTagsTicket] = useState<any>(null)
  const [currentTags, setCurrentTags] = useState<Array<{ name: string; color: string }>>([])
  const [newTagName, setNewTagName] = useState("")
  const [newTagColor, setNewTagColor] = useState("bg-blue-100 text-blue-800 border-blue-200")

  // 关联工单相关状态
  const [linkingTickets, setLinkingTickets] = useState<string[]>([])
  const [linkDialogOpen, setLinkDialogOpen] = useState(false)
  const [linkTicket, setLinkTicket] = useState<any>(null)
  const [linkType, setLinkType] = useState("")
  const [selectedTicket, setSelectedTicket] = useState<any>(null)
  const [linkDescription, setLinkDescription] = useState("")
  const [bidirectional, setBidirectional] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  /**
   * 接收工单
   */
  const handleAcceptTicket = async (ticket: any) => {
    try {
      setAcceptingTickets(prev => [...prev, ticket.id])
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        status: "in_progress",
        assignee: "我",
        updatedAt: new Date().toISOString(),
        acceptedAt: new Date().toISOString()
      }

      onTicketUpdate?.(ticket.id, updates)

      toast.success(`工单 ${ticket.id} 已成功接收`, {
        description: "工单状态已更新为处理中，您现在可以开始处理此工单。"
      })

    } catch (error) {
      console.error("接收工单失败:", error)
      toast.error("接收工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setAcceptingTickets(prev => prev.filter(id => id !== ticket.id))
      setConfirmAcceptTicket(null)
    }
  }

  /**
   * 退回工单
   */
  const handleRejectTicket = async () => {
    if (!rejectingTicket || !rejectReason.trim()) {
      toast.error("请填写退回原因")
      return
    }

    try {
      setRejectingTickets(prev => [...prev, rejectingTicket.id])
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        status: "rejected",
        rejectedAt: new Date().toISOString(),
        rejectedBy: "我",
        rejectedReason: rejectReason,
        rejectedTo: rejectTarget || "派单员",
        updatedAt: new Date().toISOString()
      }

      onTicketUpdate?.(rejectingTicket.id, updates)

      toast.success(`工单 ${rejectingTicket.id} 已退回`, {
        description: `已退回给${rejectTarget || "派单员"}，退回原因：${rejectReason}`
      })

      // 重置状态
      setRejectDialogOpen(false)
      setRejectingTicket(null)
      setRejectReason("")
      setRejectTarget("")

    } catch (error) {
      console.error("退回工单失败:", error)
      toast.error("退回工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setRejectingTickets(prev => prev.filter(id => id !== rejectingTicket?.id))
    }
  }

  /**
   * 转办工单
   */
  const handleTransferTicket = async () => {
    if (!transferringTicket || !transferTarget.trim()) {
      toast.error("请选择转办目标")
      return
    }

    try {
      setTransferringTickets(prev => [...prev, transferringTicket.id])
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        status: "transferred",
        transferredAt: new Date().toISOString(),
        transferredBy: "我",
        transferredTo: transferTarget,
        transferredReason: transferReason,
        assignee: transferTarget,
        updatedAt: new Date().toISOString(),
        ...(transferPriority && transferPriority !== "keep_current" && { priority: transferPriority }),
        ...(transferUrgent && {
          tags: [...(transferringTicket.tags || []), "紧急转办"].filter((tag, index, arr) => arr.indexOf(tag) === index)
        })
      }

      onTicketUpdate?.(transferringTicket.id, updates)

      toast.success(`工单 ${transferringTicket.id} 已转办`, {
        description: `已转办给${transferTarget}${transferReason ? `，转办原因：${transferReason}` : ""}`
      })

      // 重置状态
      setTransferDialogOpen(false)
      setTransferringTicket(null)
      setTransferTarget("")
      setTransferReason("")
      setTransferPriority("")
      setTransferUrgent(false)

    } catch (error) {
      console.error("转办工单失败:", error)
      toast.error("转办工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setTransferringTickets(prev => prev.filter(id => id !== transferringTicket?.id))
    }
  }

  /**
   * 邀请协办
   */
  const handleCollaborateTicket = async () => {
    if (!collaboratingTicket || collaborators.length === 0) {
      toast.error("请至少选择一个协办人员")
      return
    }

    try {
      setCollaboratingTickets(prev => [...prev, collaboratingTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        collaborators: [...(collaboratingTicket.collaborators || []), ...collaborators]
          .filter((collaborator, index, arr) => arr.indexOf(collaborator) === index),
        collaborateHistory: [
          ...(collaboratingTicket.collaborateHistory || []),
          {
            invitedAt: new Date().toISOString(),
            invitedBy: "我",
            invitedUsers: collaborators,
            message: collaborateMessage,
            permissions: collaboratePermissions
          }
        ],
        updatedAt: new Date().toISOString(),
        tags: [...(collaboratingTicket.tags || []), "协办中"].filter((tag, index, arr) => arr.indexOf(tag) === index)
      }

      onTicketUpdate?.(collaboratingTicket.id, updates)

      toast.success(`已邀请协办人员`, {
        description: `已邀请 ${collaborators.join("、")} 协办处理工单 ${collaboratingTicket.id}`
      })

      // 重置状态
      setCollaborateDialogOpen(false)
      setCollaboratingTicket(null)
      setCollaborators([])
      setCollaborateMessage("")
      setCollaboratePermissions(["view", "comment"])

    } catch (error) {
      console.error("邀请协办失败:", error)
      toast.error("邀请协办失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setCollaboratingTickets(prev => prev.filter(id => id !== collaboratingTicket?.id))
    }
  }

  /**
   * 上报/升级工单
   */
  const handleEscalateTicket = async () => {
    if (!escalatingTicket || !escalationType || !escalationTarget || !escalationReason.trim()) {
      toast.error("请填写完整的上报信息")
      return
    }

    try {
      setEscalatingTickets(prev => [...prev, escalatingTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        status: "escalated",
        escalatedAt: new Date().toISOString(),
        escalatedBy: "我",
        escalationType: escalationType,
        escalatedTo: escalationTarget,
        escalationReason: escalationReason,
        escalationHistory: [
          ...(escalatingTicket.escalationHistory || []),
          {
            escalatedAt: new Date().toISOString(),
            escalatedBy: "我",
            escalationType: escalationType,
            escalatedTo: escalationTarget,
            reason: escalationReason,
            previousAssignee: escalatingTicket.assignee
          }
        ],
        assignee: escalationTarget,
        updatedAt: new Date().toISOString(),
        // 如果设置了新优先级，则更新优先级
        ...(escalationPriority && escalationPriority !== "keep_current" && { priority: escalationPriority }),
        // 如果标记为紧急，添加紧急标签
        ...(escalationUrgent && {
          tags: [...(escalatingTicket.tags || []), "紧急上报"].filter((tag, index, arr) => arr.indexOf(tag) === index)
        })
      }

      onTicketUpdate?.(escalatingTicket.id, updates)

      toast.success(`工单 ${escalatingTicket.id} 已成功上报`, {
        description: `已上报给${escalationTarget}，类型：${escalationType}`
      })

      // 重置状态
      setEscalateDialogOpen(false)
      setEscalatingTicket(null)
      setEscalationType("")
      setEscalationTarget("")
      setEscalationReason("")
      setEscalationPriority("")
      setEscalationUrgent(false)

    } catch (error) {
      console.error("上报工单失败:", error)
      toast.error("上报工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setEscalatingTickets(prev => prev.filter(id => id !== escalatingTicket?.id))
    }
  }

  /**
   * 申请延期
   */
  const handleRequestExtension = async () => {
    if (!extensionTicket || !extensionType || !extensionDuration || !extensionApprover || !extensionReason.trim()) {
      toast.error("请填写完整的延期申请信息")
      return
    }

    // 验证自定义时长
    if (extensionDuration === "custom" && customHours <= 0) {
      toast.error("请输入有效的延期小时数")
      return
    }

    try {
      setRequestingExtensionTickets(prev => [...prev, extensionTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 计算延期时长
      const extensionDurations = [
        { value: "2h", hours: 2 },
        { value: "4h", hours: 4 },
        { value: "8h", hours: 8 },
        { value: "1d", hours: 24 },
        { value: "2d", hours: 48 },
        { value: "3d", hours: 72 },
        { value: "1w", hours: 168 }
      ]

      const selectedDuration = extensionDurations.find(d => d.value === extensionDuration)
      const finalHours = extensionDuration === "custom" ? customHours : (selectedDuration?.hours || 0)

      // 计算新的截止时间
      const currentDeadline = new Date(extensionTicket.deadline || Date.now() + 24 * 60 * 60 * 1000)
      const newDeadline = new Date(currentDeadline.getTime() + finalHours * 60 * 60 * 1000)

      const updates = {
        status: "extension_pending",
        extensionRequestedAt: new Date().toISOString(),
        extensionRequestedBy: "我",
        extensionType: extensionType,
        extensionDuration: finalHours,
        extensionReason: extensionReason,
        extensionApprover: extensionApprover,
        proposedNewDeadline: newDeadline.toISOString(),
        extensionHistory: [
          ...(extensionTicket.extensionHistory || []),
          {
            requestedAt: new Date().toISOString(),
            requestedBy: "我",
            extensionType: extensionType,
            requestedHours: finalHours,
            reason: extensionReason,
            approver: extensionApprover,
            status: "pending",
            originalDeadline: currentDeadline.toISOString(),
            proposedDeadline: newDeadline.toISOString()
          }
        ],
        updatedAt: new Date().toISOString(),
        tags: [...(extensionTicket.tags || []), "延期申请中"].filter((tag, index, arr) => arr.indexOf(tag) === index)
      }

      onTicketUpdate?.(extensionTicket.id, updates)

      toast.success(`延期申请已提交`, {
        description: `工单 ${extensionTicket.id} 的延期申请已发送给${extensionApprover}审批`
      })

      // 重置状态
      setExtensionDialogOpen(false)
      setExtensionTicket(null)
      setExtensionType("")
      setExtensionDuration("")
      setCustomHours(0)
      setExtensionReason("")
      setExtensionApprover("")

    } catch (error) {
      console.error("申请延期失败:", error)
      toast.error("申请延期失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setRequestingExtensionTickets(prev => prev.filter(id => id !== extensionTicket?.id))
    }
  }

  /**
   * 申请挂起
   */
  const handleRequestSuspension = async () => {
    if (!suspensionTicket || !suspensionType || !suspensionDuration || !suspensionApprover || !suspensionReason.trim()) {
      toast.error("请填写完整的挂起申请信息")
      return
    }

    // 验证自定义天数
    if (suspensionDuration === "custom" && customDays <= 0) {
      toast.error("请输入有效的挂起天数")
      return
    }

    try {
      setRequestingSuspensionTickets(prev => [...prev, suspensionTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 计算挂起时长
      const suspensionDurations = [
        { value: "1d", days: 1 },
        { value: "3d", days: 3 },
        { value: "1w", days: 7 },
        { value: "2w", days: 14 },
        { value: "1m", days: 30 }
      ]

      const selectedDuration = suspensionDurations.find(d => d.value === suspensionDuration)
      const finalDays = suspensionDuration === "custom" ? customDays : (selectedDuration?.days || 0)

      // 计算预计恢复时间
      const currentDate = new Date()
      const resumeDate = suspensionDuration === "indefinite"
        ? null
        : new Date(currentDate.getTime() + finalDays * 24 * 60 * 60 * 1000)

      const updates = {
        status: "suspension_pending",
        suspensionRequestedAt: new Date().toISOString(),
        suspensionRequestedBy: "我",
        suspensionType: suspensionType,
        suspensionDuration: suspensionDuration === "indefinite" ? "indefinite" : finalDays.toString(),
        suspensionReason: suspensionReason,
        suspensionApprover: suspensionApprover,
        proposedResumeDate: resumeDate?.toISOString() || null,
        suspensionHistory: [
          ...(suspensionTicket.suspensionHistory || []),
          {
            requestedAt: new Date().toISOString(),
            requestedBy: "我",
            suspensionType: suspensionType,
            requestedDuration: suspensionDuration === "indefinite" ? "indefinite" : finalDays.toString(),
            reason: suspensionReason,
            approver: suspensionApprover,
            status: "pending",
            proposedResumeDate: resumeDate?.toISOString() || null
          }
        ],
        updatedAt: new Date().toISOString(),
        tags: [...(suspensionTicket.tags || []), "挂起申请中"].filter((tag, index, arr) => arr.indexOf(tag) === index)
      }

      onTicketUpdate?.(suspensionTicket.id, updates)

      toast.success(`挂起申请已提交`, {
        description: `工单 ${suspensionTicket.id} 的挂起申请已发送给${suspensionApprover}审批`
      })

      // 重置状态
      setSuspensionDialogOpen(false)
      setSuspensionTicket(null)
      setSuspensionType("")
      setSuspensionDuration("")
      setCustomDays(0)
      setSuspensionReason("")
      setSuspensionApprover("")

    } catch (error) {
      console.error("申请挂起失败:", error)
      toast.error("申请挂起失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setRequestingSuspensionTickets(prev => prev.filter(id => id !== suspensionTicket?.id))
    }
  }

  /**
   * 抄送工单
   */
  const handleCcTicket = async () => {
    if (!ccTicket || !ccType || ccRecipients.length === 0 || !ccMessage.trim()) {
      toast.error("请填写完整的抄送信息")
      return
    }

    try {
      setSendingCcTickets(prev => [...prev, ccTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 获取抄送人员信息
      const ccRecipientsData = ccRecipients.map(id => {
        const recipient = [
          { id: "user1", name: "张三", role: "技术经理", department: "技术部" },
          { id: "user2", name: "李四", role: "产品经理", department: "产品部" },
          { id: "user3", name: "王五", role: "运维工程师", department: "运维部" },
          { id: "user4", name: "赵六", role: "测试工程师", department: "测试部" },
          { id: "user5", name: "钱七", role: "项目经理", department: "项目部" },
          { id: "user6", name: "孙八", role: "客户经理", department: "客服部" },
          { id: "user7", name: "周九", role: "质量管理员", department: "质量部" },
          { id: "user8", name: "吴十", role: "安全专员", department: "安全部" },
          { id: "user9", name: "郑一", role: "架构师", department: "技术部" },
          { id: "user10", name: "王二", role: "业务分析师", department: "业务部" }
        ].find(r => r.id === id)
        return recipient || { id, name: "未知用户", role: "未知", department: "未知" }
      })

      const updates = {
        ccHistory: [
          ...(ccTicket.ccHistory || []),
          {
            sentAt: new Date().toISOString(),
            sentBy: "我",
            ccType: ccType,
            recipients: ccRecipientsData,
            message: ccMessage,
            permissions: ccPermissions,
            recipientIds: ccRecipients
          }
        ],
        updatedAt: new Date().toISOString(),
        tags: [...(ccTicket.tags || []), "已抄送"].filter((tag, index, arr) => arr.indexOf(tag) === index)
      }

      onTicketUpdate?.(ccTicket.id, updates)

      const recipientNames = ccRecipientsData.map(r => r.name).join("、")
      toast.success(`抄送成功`, {
        description: `工单 ${ccTicket.id} 已抄送给 ${recipientNames}`
      })

      // 重置状态
      setCcDialogOpen(false)
      setCcTicket(null)
      setCcType("")
      setCcRecipients([])
      setCcMessage("")
      setCcPermissions(["view_basic"])

    } catch (error) {
      console.error("抄送失败:", error)
      toast.error("抄送失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setSendingCcTickets(prev => prev.filter(id => id !== ccTicket?.id))
    }
  }

  /**
   * 管理标签
   */
  const handleManageTags = async () => {
    if (!tagsTicket) {
      toast.error("工单信息不完整")
      return
    }

    try {
      setUpdatingTagsTickets(prev => [...prev, tagsTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        tags: currentTags.map(tag => tag.name),
        tagDetails: currentTags,
        updatedAt: new Date().toISOString(),
        tagHistory: [
          ...(tagsTicket.tagHistory || []),
          {
            updatedAt: new Date().toISOString(),
            updatedBy: "我",
            previousTags: tagsTicket.tags || [],
            newTags: currentTags.map(tag => tag.name),
            action: "update_tags"
          }
        ]
      }

      onTicketUpdate?.(tagsTicket.id, updates)

      toast.success(`标签更新成功`, {
        description: `工单 ${tagsTicket.id} 的标签已更新`
      })

      // 重置状态
      setTagsDialogOpen(false)
      setTagsTicket(null)
      setCurrentTags([])
      setNewTagName("")
      setNewTagColor("bg-blue-100 text-blue-800 border-blue-200")

    } catch (error) {
      console.error("标签更新失败:", error)
      toast.error("标签更新失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setUpdatingTagsTickets(prev => prev.filter(id => id !== tagsTicket?.id))
    }
  }

  /**
   * 关联工单
   */
  const handleLinkTicket = async () => {
    if (!linkTicket || !linkType || !selectedTicket || !linkDescription.trim()) {
      toast.error("请填写完整的关联信息")
      return
    }

    try {
      setLinkingTickets(prev => [...prev, linkTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 获取关联类型信息
      const linkTypeInfo = [
        {
          value: "depends_on",
          label: "依赖于",
          description: "当前工单依赖于目标工单完成",
          color: "bg-red-100 text-red-800 border-red-200",
          bidirectional: false
        },
        {
          value: "blocks",
          label: "阻塞",
          description: "当前工单阻塞目标工单进行",
          color: "bg-orange-100 text-orange-800 border-orange-200",
          bidirectional: false
        },
        {
          value: "related_to",
          label: "相关",
          description: "与目标工单相关",
          color: "bg-blue-100 text-blue-800 border-blue-200",
          bidirectional: true
        },
        {
          value: "duplicate_of",
          label: "重复",
          description: "与目标工单重复",
          color: "bg-purple-100 text-purple-800 border-purple-200",
          bidirectional: false
        },
        {
          value: "parent_of",
          label: "父任务",
          description: "当前工单是目标工单的父任务",
          color: "bg-green-100 text-green-800 border-green-200",
          bidirectional: false
        },
        {
          value: "child_of",
          label: "子任务",
          description: "当前工单是目标工单的子任务",
          color: "bg-yellow-100 text-yellow-800 border-yellow-200",
          bidirectional: false
        },
        {
          value: "follows",
          label: "后续",
          description: "当前工单在目标工单之后处理",
          color: "bg-indigo-100 text-indigo-800 border-indigo-200",
          bidirectional: false
        },
        {
          value: "precedes",
          label: "前置",
          description: "当前工单在目标工单之前处理",
          color: "bg-pink-100 text-pink-800 border-pink-200",
          bidirectional: false
        }
      ].find(t => t.value === linkType)

      const linkInfo = {
        id: Date.now().toString(),
        type: linkType,
        typeLabel: linkTypeInfo?.label || linkType,
        typeColor: linkTypeInfo?.color || "bg-gray-100 text-gray-800 border-gray-200",
        targetTicket: selectedTicket,
        description: linkDescription,
        bidirectional: bidirectional && linkTypeInfo?.bidirectional,
        createdAt: new Date().toISOString(),
        createdBy: "我"
      }

      const updates = {
        linkedTickets: [
          ...(linkTicket.linkedTickets || []),
          linkInfo
        ],
        updatedAt: new Date().toISOString(),
        linkHistory: [
          ...(linkTicket.linkHistory || []),
          {
            action: "link_created",
            createdAt: new Date().toISOString(),
            createdBy: "我",
            linkType: linkType,
            targetTicketId: selectedTicket.id,
            targetTicketTitle: selectedTicket.title,
            description: linkDescription,
            bidirectional: bidirectional && linkTypeInfo?.bidirectional
          }
        ]
      }

      onTicketUpdate?.(linkTicket.id, updates)

      toast.success(`关联建立成功`, {
        description: `工单 ${linkTicket.id} 已与 ${selectedTicket.id} 建立${linkTypeInfo?.label}关联`
      })

      // 重置状态
      setLinkDialogOpen(false)
      setLinkTicket(null)
      setLinkType("")
      setSelectedTicket(null)
      setLinkDescription("")
      setBidirectional(false)
      setSearchQuery("")

    } catch (error) {
      console.error("关联工单失败:", error)
      toast.error("关联工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setLinkingTickets(prev => prev.filter(id => id !== linkTicket?.id))
    }
  }

  // 工具函数
  const isAccepting = (ticketId: string) => acceptingTickets.includes(ticketId)
  const isRejecting = (ticketId: string) => rejectingTickets.includes(ticketId)
  const isTransferring = (ticketId: string) => transferringTickets.includes(ticketId)
  const isCollaborating = (ticketId: string) => collaboratingTickets.includes(ticketId)
  const isEscalating = (ticketId: string) => escalatingTickets.includes(ticketId)
  const isRequestingExtension = (ticketId: string) => requestingExtensionTickets.includes(ticketId)
  const isRequestingSuspension = (ticketId: string) => requestingSuspensionTickets.includes(ticketId)
  const isSendingCc = (ticketId: string) => sendingCcTickets.includes(ticketId)
  const isUpdatingTags = (ticketId: string) => updatingTagsTickets.includes(ticketId)
  const isLinking = (ticketId: string) => linkingTickets.includes(ticketId)

  const openRejectDialog = (ticket: any) => {
    setRejectingTicket(ticket)
    setRejectDialogOpen(true)
    setRejectReason("")
    setRejectTarget("")
  }

  const openTransferDialog = (ticket: any) => {
    setTransferringTicket(ticket)
    setTransferDialogOpen(true)
    setTransferTarget("")
    setTransferReason("")
    setTransferPriority("")
    setTransferUrgent(false)
  }

  const openCollaborateDialog = (ticket: any) => {
    setCollaboratingTicket(ticket)
    setCollaborateDialogOpen(true)
    setCollaborators([])
    setCollaborateMessage("")
    setCollaboratePermissions(["view", "comment"])
  }

  const openEscalateDialog = (ticket: any) => {
    setEscalatingTicket(ticket)
    setEscalateDialogOpen(true)
    setEscalationType("")
    setEscalationTarget("")
    setEscalationReason("")
    setEscalationPriority("")
    setEscalationUrgent(false)
  }

  const openExtensionDialog = (ticket: any) => {
    setExtensionTicket(ticket)
    setExtensionDialogOpen(true)
    setExtensionType("")
    setExtensionDuration("")
    setCustomHours(0)
    setExtensionReason("")
    setExtensionApprover("")
  }

  const openSuspensionDialog = (ticket: any) => {
    setSuspensionTicket(ticket)
    setSuspensionDialogOpen(true)
    setSuspensionType("")
    setSuspensionDuration("")
    setCustomDays(0)
    setSuspensionReason("")
    setSuspensionApprover("")
  }

  const openCcDialog = (ticket: any) => {
    setCcTicket(ticket)
    setCcDialogOpen(true)
    setCcType("")
    setCcRecipients([])
    setCcMessage("")
    setCcPermissions(["view_basic"])
  }

  const openTagsDialog = (ticket: any) => {
    setTagsTicket(ticket)
    setTagsDialogOpen(true)

    // 初始化当前标签
    const existingTags = ticket.tagDetails || ticket.tags?.map((tag: string) => ({
      name: tag,
      color: "bg-blue-100 text-blue-800 border-blue-200"
    })) || []

    setCurrentTags(existingTags)
    setNewTagName("")
    setNewTagColor("bg-blue-100 text-blue-800 border-blue-200")
  }

  const openLinkDialog = (ticket: any) => {
    setLinkTicket(ticket)
    setLinkDialogOpen(true)
    setLinkType("")
    setSelectedTicket(null)
    setLinkDescription("")
    setBidirectional(false)
    setSearchQuery("")
  }

  const addCollaborator = (collaborator: string) => {
    if (!collaborators.includes(collaborator)) {
      setCollaborators(prev => [...prev, collaborator])
    }
  }

  const removeCollaborator = (collaborator: string) => {
    setCollaborators(prev => prev.filter(c => c !== collaborator))
  }

  const togglePermission = (permission: string) => {
    setCollaboratePermissions(prev =>
      prev.includes(permission)
        ? prev.filter(p => p !== permission)
        : [...prev, permission]
    )
  }

  return {
    // 状态
    acceptingTickets,
    rejectDialogOpen,
    rejectingTicket,
    rejectReason,
    rejectTarget,
    transferDialogOpen,
    transferringTicket,
    transferTarget,
    transferReason,
    transferPriority,
    transferUrgent,
    collaborateDialogOpen,
    collaboratingTicket,
    collaborators,
    collaborateMessage,
    collaboratePermissions,
    escalateDialogOpen,
    escalatingTicket,
    escalationType,
    escalationTarget,
    escalationReason,
    escalationPriority,
    escalationUrgent,
    extensionDialogOpen,
    extensionTicket,
    extensionType,
    extensionDuration,
    customHours,
    extensionReason,
    extensionApprover,
    suspensionDialogOpen,
    suspensionTicket,
    suspensionType,
    suspensionDuration,
    customDays,
    suspensionReason,
    suspensionApprover,
    ccDialogOpen,
    ccTicket,
    ccType,
    ccRecipients,
    ccMessage,
    ccPermissions,
    tagsDialogOpen,
    tagsTicket,
    currentTags,
    newTagName,
    newTagColor,
    linkDialogOpen,
    linkTicket,
    linkType,
    selectedTicket,
    linkDescription,
    bidirectional,
    searchQuery,

    // 状态设置函数
    setRejectDialogOpen,
    setRejectReason,
    setRejectTarget,
    setTransferDialogOpen,
    setTransferTarget,
    setTransferReason,
    setTransferPriority,
    setTransferUrgent,
    setCollaborateDialogOpen,
    setCollaborateMessage,
    setCollaboratePermissions,
    setEscalateDialogOpen,
    setEscalationType,
    setEscalationTarget,
    setEscalationReason,
    setEscalationPriority,
    setEscalationUrgent,
    setExtensionDialogOpen,
    setExtensionType,
    setExtensionDuration,
    setCustomHours,
    setExtensionReason,
    setExtensionApprover,
    setSuspensionDialogOpen,
    setSuspensionType,
    setSuspensionDuration,
    setCustomDays,
    setSuspensionReason,
    setSuspensionApprover,
    setCcDialogOpen,
    setCcType,
    setCcRecipients,
    setCcMessage,
    setCcPermissions,
    setTagsDialogOpen,
    setCurrentTags,
    setNewTagName,
    setNewTagColor,
    setLinkDialogOpen,
    setLinkType,
    setSelectedTicket,
    setLinkDescription,
    setBidirectional,
    setSearchQuery,

    // 事件处理函数
    handleAcceptTicket,
    handleRejectTicket,
    handleTransferTicket,
    handleCollaborateTicket,
    handleEscalateTicket,
    handleRequestExtension,
    handleRequestSuspension,
    handleCcTicket,
    handleManageTags,
    handleLinkTicket,

    // 工具函数
    isAccepting,
    isRejecting,
    isTransferring,
    isCollaborating,
    isEscalating,
    isRequestingExtension,
    isRequestingSuspension,
    isSendingCc,
    isUpdatingTags,
    isLinking,
    openRejectDialog,
    openTransferDialog,
    openCollaborateDialog,
    openEscalateDialog,
    openExtensionDialog,
    openSuspensionDialog,
    openCcDialog,
    openTagsDialog,
    openLinkDialog,
    addCollaborator,
    removeCollaborator,
    togglePermission
  }
}
