"use client"

import { useState } from "react"
import { toast } from "sonner"

/**
 * 工单操作相关的自定义Hook
 * 管理工单的接收、退回、转办、协办等操作的状态和逻辑
 */
export function useTicketActions(onTicketUpdate?: (ticketId: string, updates: any) => void) {
  // 接收工单相关状态
  const [acceptingTickets, setAcceptingTickets] = useState<string[]>([])
  const [confirmAcceptTicket, setConfirmAcceptTicket] = useState<any>(null)

  // 退回工单相关状态
  const [rejectingTickets, setRejectingTickets] = useState<string[]>([])
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false)
  const [rejectingTicket, setRejectingTicket] = useState<any>(null)
  const [rejectReason, setRejectReason] = useState("")
  const [rejectTarget, setRejectTarget] = useState("")

  // 转办工单相关状态
  const [transferringTickets, setTransferringTickets] = useState<string[]>([])
  const [transferDialogOpen, setTransferDialogOpen] = useState(false)
  const [transferringTicket, setTransferringTicket] = useState<any>(null)
  const [transferTarget, setTransferTarget] = useState("")
  const [transferReason, setTransferReason] = useState("")
  const [transferPriority, setTransferPriority] = useState("")
  const [transferUrgent, setTransferUrgent] = useState(false)

  // 协办工单相关状态
  const [collaboratingTickets, setCollaboratingTickets] = useState<string[]>([])
  const [collaborateDialogOpen, setCollaborateDialogOpen] = useState(false)
  const [collaboratingTicket, setCollaboratingTicket] = useState<any>(null)
  const [collaborators, setCollaborators] = useState<string[]>([])
  const [collaborateMessage, setCollaborateMessage] = useState("")
  const [collaboratePermissions, setCollaboratePermissions] = useState<string[]>(["view", "comment"])

  // 上报/升级工单相关状态
  const [escalatingTickets, setEscalatingTickets] = useState<string[]>([])
  const [escalateDialogOpen, setEscalateDialogOpen] = useState(false)
  const [escalatingTicket, setEscalatingTicket] = useState<any>(null)
  const [escalationType, setEscalationType] = useState("")
  const [escalationTarget, setEscalationTarget] = useState("")
  const [escalationReason, setEscalationReason] = useState("")
  const [escalationPriority, setEscalationPriority] = useState("")
  const [escalationUrgent, setEscalationUrgent] = useState(false)

  /**
   * 接收工单
   */
  const handleAcceptTicket = async (ticket: any) => {
    try {
      setAcceptingTickets(prev => [...prev, ticket.id])
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        status: "in_progress",
        assignee: "我",
        updatedAt: new Date().toISOString(),
        acceptedAt: new Date().toISOString()
      }

      onTicketUpdate?.(ticket.id, updates)

      toast.success(`工单 ${ticket.id} 已成功接收`, {
        description: "工单状态已更新为处理中，您现在可以开始处理此工单。"
      })

    } catch (error) {
      console.error("接收工单失败:", error)
      toast.error("接收工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setAcceptingTickets(prev => prev.filter(id => id !== ticket.id))
      setConfirmAcceptTicket(null)
    }
  }

  /**
   * 退回工单
   */
  const handleRejectTicket = async () => {
    if (!rejectingTicket || !rejectReason.trim()) {
      toast.error("请填写退回原因")
      return
    }

    try {
      setRejectingTickets(prev => [...prev, rejectingTicket.id])
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        status: "rejected",
        rejectedAt: new Date().toISOString(),
        rejectedBy: "我",
        rejectedReason: rejectReason,
        rejectedTo: rejectTarget || "派单员",
        updatedAt: new Date().toISOString()
      }

      onTicketUpdate?.(rejectingTicket.id, updates)

      toast.success(`工单 ${rejectingTicket.id} 已退回`, {
        description: `已退回给${rejectTarget || "派单员"}，退回原因：${rejectReason}`
      })

      // 重置状态
      setRejectDialogOpen(false)
      setRejectingTicket(null)
      setRejectReason("")
      setRejectTarget("")

    } catch (error) {
      console.error("退回工单失败:", error)
      toast.error("退回工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setRejectingTickets(prev => prev.filter(id => id !== rejectingTicket?.id))
    }
  }

  /**
   * 转办工单
   */
  const handleTransferTicket = async () => {
    if (!transferringTicket || !transferTarget.trim()) {
      toast.error("请选择转办目标")
      return
    }

    try {
      setTransferringTickets(prev => [...prev, transferringTicket.id])
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        status: "transferred",
        transferredAt: new Date().toISOString(),
        transferredBy: "我",
        transferredTo: transferTarget,
        transferredReason: transferReason,
        assignee: transferTarget,
        updatedAt: new Date().toISOString(),
        ...(transferPriority && transferPriority !== "keep_current" && { priority: transferPriority }),
        ...(transferUrgent && {
          tags: [...(transferringTicket.tags || []), "紧急转办"].filter((tag, index, arr) => arr.indexOf(tag) === index)
        })
      }

      onTicketUpdate?.(transferringTicket.id, updates)

      toast.success(`工单 ${transferringTicket.id} 已转办`, {
        description: `已转办给${transferTarget}${transferReason ? `，转办原因：${transferReason}` : ""}`
      })

      // 重置状态
      setTransferDialogOpen(false)
      setTransferringTicket(null)
      setTransferTarget("")
      setTransferReason("")
      setTransferPriority("")
      setTransferUrgent(false)

    } catch (error) {
      console.error("转办工单失败:", error)
      toast.error("转办工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setTransferringTickets(prev => prev.filter(id => id !== transferringTicket?.id))
    }
  }

  /**
   * 邀请协办
   */
  const handleCollaborateTicket = async () => {
    if (!collaboratingTicket || collaborators.length === 0) {
      toast.error("请至少选择一个协办人员")
      return
    }

    try {
      setCollaboratingTickets(prev => [...prev, collaboratingTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        collaborators: [...(collaboratingTicket.collaborators || []), ...collaborators]
          .filter((collaborator, index, arr) => arr.indexOf(collaborator) === index),
        collaborateHistory: [
          ...(collaboratingTicket.collaborateHistory || []),
          {
            invitedAt: new Date().toISOString(),
            invitedBy: "我",
            invitedUsers: collaborators,
            message: collaborateMessage,
            permissions: collaboratePermissions
          }
        ],
        updatedAt: new Date().toISOString(),
        tags: [...(collaboratingTicket.tags || []), "协办中"].filter((tag, index, arr) => arr.indexOf(tag) === index)
      }

      onTicketUpdate?.(collaboratingTicket.id, updates)

      toast.success(`已邀请协办人员`, {
        description: `已邀请 ${collaborators.join("、")} 协办处理工单 ${collaboratingTicket.id}`
      })

      // 重置状态
      setCollaborateDialogOpen(false)
      setCollaboratingTicket(null)
      setCollaborators([])
      setCollaborateMessage("")
      setCollaboratePermissions(["view", "comment"])

    } catch (error) {
      console.error("邀请协办失败:", error)
      toast.error("邀请协办失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setCollaboratingTickets(prev => prev.filter(id => id !== collaboratingTicket?.id))
    }
  }

  /**
   * 上报/升级工单
   */
  const handleEscalateTicket = async () => {
    if (!escalatingTicket || !escalationType || !escalationTarget || !escalationReason.trim()) {
      toast.error("请填写完整的上报信息")
      return
    }

    try {
      setEscalatingTickets(prev => [...prev, escalatingTicket.id])

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updates = {
        status: "escalated",
        escalatedAt: new Date().toISOString(),
        escalatedBy: "我",
        escalationType: escalationType,
        escalatedTo: escalationTarget,
        escalationReason: escalationReason,
        escalationHistory: [
          ...(escalatingTicket.escalationHistory || []),
          {
            escalatedAt: new Date().toISOString(),
            escalatedBy: "我",
            escalationType: escalationType,
            escalatedTo: escalationTarget,
            reason: escalationReason,
            previousAssignee: escalatingTicket.assignee
          }
        ],
        assignee: escalationTarget,
        updatedAt: new Date().toISOString(),
        // 如果设置了新优先级，则更新优先级
        ...(escalationPriority && escalationPriority !== "keep_current" && { priority: escalationPriority }),
        // 如果标记为紧急，添加紧急标签
        ...(escalationUrgent && {
          tags: [...(escalatingTicket.tags || []), "紧急上报"].filter((tag, index, arr) => arr.indexOf(tag) === index)
        })
      }

      onTicketUpdate?.(escalatingTicket.id, updates)

      toast.success(`工单 ${escalatingTicket.id} 已成功上报`, {
        description: `已上报给${escalationTarget}，类型：${escalationType}`
      })

      // 重置状态
      setEscalateDialogOpen(false)
      setEscalatingTicket(null)
      setEscalationType("")
      setEscalationTarget("")
      setEscalationReason("")
      setEscalationPriority("")
      setEscalationUrgent(false)

    } catch (error) {
      console.error("上报工单失败:", error)
      toast.error("上报工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setEscalatingTickets(prev => prev.filter(id => id !== escalatingTicket?.id))
    }
  }

  // 工具函数
  const isAccepting = (ticketId: string) => acceptingTickets.includes(ticketId)
  const isRejecting = (ticketId: string) => rejectingTickets.includes(ticketId)
  const isTransferring = (ticketId: string) => transferringTickets.includes(ticketId)
  const isCollaborating = (ticketId: string) => collaboratingTickets.includes(ticketId)
  const isEscalating = (ticketId: string) => escalatingTickets.includes(ticketId)

  const openRejectDialog = (ticket: any) => {
    setRejectingTicket(ticket)
    setRejectDialogOpen(true)
    setRejectReason("")
    setRejectTarget("")
  }

  const openTransferDialog = (ticket: any) => {
    setTransferringTicket(ticket)
    setTransferDialogOpen(true)
    setTransferTarget("")
    setTransferReason("")
    setTransferPriority("")
    setTransferUrgent(false)
  }

  const openCollaborateDialog = (ticket: any) => {
    setCollaboratingTicket(ticket)
    setCollaborateDialogOpen(true)
    setCollaborators([])
    setCollaborateMessage("")
    setCollaboratePermissions(["view", "comment"])
  }

  const openEscalateDialog = (ticket: any) => {
    setEscalatingTicket(ticket)
    setEscalateDialogOpen(true)
    setEscalationType("")
    setEscalationTarget("")
    setEscalationReason("")
    setEscalationPriority("")
    setEscalationUrgent(false)
  }

  const addCollaborator = (collaborator: string) => {
    if (!collaborators.includes(collaborator)) {
      setCollaborators(prev => [...prev, collaborator])
    }
  }

  const removeCollaborator = (collaborator: string) => {
    setCollaborators(prev => prev.filter(c => c !== collaborator))
  }

  const togglePermission = (permission: string) => {
    setCollaboratePermissions(prev =>
      prev.includes(permission)
        ? prev.filter(p => p !== permission)
        : [...prev, permission]
    )
  }

  return {
    // 状态
    acceptingTickets,
    rejectDialogOpen,
    rejectingTicket,
    rejectReason,
    rejectTarget,
    transferDialogOpen,
    transferringTicket,
    transferTarget,
    transferReason,
    transferPriority,
    transferUrgent,
    collaborateDialogOpen,
    collaboratingTicket,
    collaborators,
    collaborateMessage,
    collaboratePermissions,
    escalateDialogOpen,
    escalatingTicket,
    escalationType,
    escalationTarget,
    escalationReason,
    escalationPriority,
    escalationUrgent,

    // 状态设置函数
    setRejectDialogOpen,
    setRejectReason,
    setRejectTarget,
    setTransferDialogOpen,
    setTransferTarget,
    setTransferReason,
    setTransferPriority,
    setTransferUrgent,
    setCollaborateDialogOpen,
    setCollaborateMessage,
    setCollaboratePermissions,
    setEscalateDialogOpen,
    setEscalationType,
    setEscalationTarget,
    setEscalationReason,
    setEscalationPriority,
    setEscalationUrgent,

    // 事件处理函数
    handleAcceptTicket,
    handleRejectTicket,
    handleTransferTicket,
    handleCollaborateTicket,
    handleEscalateTicket,

    // 工具函数
    isAccepting,
    isRejecting,
    isTransferring,
    isCollaborating,
    isEscalating,
    openRejectDialog,
    openTransferDialog,
    openCollaborateDialog,
    openEscalateDialog,
    addCollaborator,
    removeCollaborator,
    togglePermission
  }
}
