"use client"

import { useEffect, useState } from "react"
import { toast } from "sonner"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  X, 
  User, 
  Building, 
  Phone, 
  Clock, 
  Flag, 
  MessageSquare,
  Send,
  Eye,
  ExternalLink
} from "lucide-react"

interface QuickPreviewPanelProps {
  ticket: any
  onClose: () => void
  onTicketUpdate?: (ticketId: string, updates: any) => void
}

/**
 * 快速预览面板组件
 * 在侧边栏显示工单详细信息，支持快速操作
 */
export function QuickPreviewPanel({ ticket, onClose, onTicketUpdate }: QuickPreviewPanelProps) {
  const [isAccepting, setIsAccepting] = useState(false)
  const [isRejecting, setIsRejecting] = useState(false)
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false)
  const [rejectReason, setRejectReason] = useState("")
  const [rejectTarget, setRejectTarget] = useState("")
  const [isTransferring, setIsTransferring] = useState(false)
  const [transferDialogOpen, setTransferDialogOpen] = useState(false)
  const [transferTarget, setTransferTarget] = useState("")
  const [transferReason, setTransferReason] = useState("")
  const [transferPriority, setTransferPriority] = useState("")
  const [transferUrgent, setTransferUrgent] = useState(false)

  const [isCollaborating, setIsCollaborating] = useState(false)
  const [collaborateDialogOpen, setCollaborateDialogOpen] = useState(false)
  const [collaborators, setCollaborators] = useState<string[]>([])
  const [collaborateMessage, setCollaborateMessage] = useState("")
  const [collaboratePermissions, setCollaboratePermissions] = useState<string[]>(["view", "comment"])

  // 添加ESC键关闭功能
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [onClose])

  // 防止页面滚动
  useEffect(() => {
    document.body.style.overflow = 'hidden'
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [])

  const getPriorityColor = (priority: string) => {
    const colors = {
      urgent: "bg-red-500",
      high: "bg-orange-500",
      medium: "bg-yellow-500", 
      low: "bg-green-500"
    }
    return colors[priority] || "bg-gray-500"
  }

  const getPriorityLabel = (priority: string) => {
    const labels = {
      urgent: "紧急",
      high: "高",
      medium: "中",
      low: "低"
    }
    return labels[priority] || priority
  }

  const getStatusColor = (status: string) => {
    const colors = {
      pending: "bg-yellow-500",
      in_progress: "bg-blue-500",
      waiting_customer: "bg-orange-500",
      suspended: "bg-gray-500",
      resolved: "bg-green-500",
      completed: "bg-green-600",
      closed: "bg-gray-700"
    }
    return colors[status] || "bg-gray-500"
  }

  const getStatusLabel = (status: string) => {
    const labels = {
      pending: "待处理",
      in_progress: "处理中",
      waiting_customer: "等待客户",
      suspended: "已挂起",
      resolved: "已解决", 
      completed: "已完成",
      closed: "已关闭"
    }
    return labels[status] || status
  }

  const getSlaStatusColor = (slaStatus: string) => {
    const colors = {
      normal: "text-green-600",
      warning: "text-yellow-600",
      danger: "text-red-600",
      completed: "text-gray-500"
    }
    return colors[slaStatus] || "text-gray-500"
  }

  // 模拟评论数据
  const comments = [
    {
      id: 1,
      author: "张三",
      content: "电脑突然蓝屏，显示错误代码0x0000007B，无法正常启动。",
      timestamp: "2024-01-20 09:30:00",
      type: "customer"
    },
    {
      id: 2,
      author: "李工程师",
      content: "已接收工单，正在远程诊断问题。初步判断可能是硬盘驱动问题。",
      timestamp: "2024-01-20 10:15:00",
      type: "agent"
    },
    {
      id: 3,
      author: "李工程师",
      content: "确认是硬盘驱动器故障，需要更换硬盘。已联系采购部门。",
      timestamp: "2024-01-20 14:15:00",
      type: "agent"
    }
  ]

  const handleQuickAction = async (action: string) => {
    if (action === "accept") {
      try {
        setIsAccepting(true)

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 更新工单状态
        const updates = {
          status: "in_progress",
          assignee: "我",
          updatedAt: new Date().toISOString(),
          acceptedAt: new Date().toISOString()
        }

        onTicketUpdate?.(ticket.id, updates)

        toast.success(`工单 ${ticket.id} 已成功接收`, {
          description: "工单状态已更新为处理中。"
        })

        // 关闭预览面板
        onClose()

      } catch (error) {
        console.error("接收工单失败:", error)
        toast.error("接收工单失败", {
          description: "请稍后重试或联系系统管理员。"
        })
      } finally {
        setIsAccepting(false)
      }
    } else if (action === "reject") {
      setRejectDialogOpen(true)
    } else if (action === "transfer") {
      setTransferDialogOpen(true)
    } else if (action === "collaborate") {
      setCollaborateDialogOpen(true)
    } else {
      console.log(`快速操作: ${action}`, ticket)
    }
  }

  const handleTransferTicket = async () => {
    if (!transferTarget.trim()) {
      toast.error("请选择转办目标")
      return
    }

    try {
      setIsTransferring(true)

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新工单状态
      const updates = {
        status: "transferred",
        transferredAt: new Date().toISOString(),
        transferredBy: "我",
        transferredTo: transferTarget,
        transferredReason: transferReason,
        assignee: transferTarget,
        updatedAt: new Date().toISOString(),
        // 如果设置了新优先级，则更新优先级
        ...(transferPriority && transferPriority !== "keep_current" && { priority: transferPriority }),
        // 如果标记为紧急，添加紧急标签
        ...(transferUrgent && {
          tags: [...(ticket.tags || []), "紧急转办"].filter((tag, index, arr) => arr.indexOf(tag) === index)
        })
      }

      onTicketUpdate?.(ticket.id, updates)

      toast.success(`工单 ${ticket.id} 已转办`, {
        description: `已转办给${transferTarget}${transferReason ? `，转办原因：${transferReason}` : ""}`
      })

      // 重置状态并关闭预览
      setTransferDialogOpen(false)
      setTransferTarget("")
      setTransferReason("")
      setTransferPriority("")
      setTransferUrgent(false)
      onClose()

    } catch (error) {
      console.error("转办工单失败:", error)
      toast.error("转办工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setIsTransferring(false)
    }
  }

  const handleCollaborateTicket = async () => {
    if (collaborators.length === 0) {
      toast.error("请至少选择一个协办人员")
      return
    }

    try {
      setIsCollaborating(true)

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新工单状态
      const updates = {
        collaborators: [...(ticket.collaborators || []), ...collaborators]
          .filter((collaborator, index, arr) => arr.indexOf(collaborator) === index), // 去重
        collaborateHistory: [
          ...(ticket.collaborateHistory || []),
          {
            invitedAt: new Date().toISOString(),
            invitedBy: "我",
            invitedUsers: collaborators,
            message: collaborateMessage,
            permissions: collaboratePermissions
          }
        ],
        updatedAt: new Date().toISOString(),
        // 添加协办标签
        tags: [...(ticket.tags || []), "协办中"].filter((tag, index, arr) => arr.indexOf(tag) === index)
      }

      onTicketUpdate?.(ticket.id, updates)

      toast.success(`已邀请协办人员`, {
        description: `已邀请 ${collaborators.join("、")} 协办处理工单 ${ticket.id}`
      })

      // 重置状态并关闭预览
      setCollaborateDialogOpen(false)
      setCollaborators([])
      setCollaborateMessage("")
      setCollaboratePermissions(["view", "comment"])

    } catch (error) {
      console.error("邀请协办失败:", error)
      toast.error("邀请协办失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setIsCollaborating(false)
    }
  }

  const addCollaborator = (collaborator: string) => {
    if (!collaborators.includes(collaborator)) {
      setCollaborators(prev => [...prev, collaborator])
    }
  }

  const removeCollaborator = (collaborator: string) => {
    setCollaborators(prev => prev.filter(c => c !== collaborator))
  }

  const togglePermission = (permission: string) => {
    setCollaboratePermissions(prev =>
      prev.includes(permission)
        ? prev.filter(p => p !== permission)
        : [...prev, permission]
    )
  }

  const handleRejectTicket = async () => {
    if (!rejectReason.trim()) {
      toast.error("请填写退回原因")
      return
    }

    try {
      setIsRejecting(true)

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新工单状态
      const updates = {
        status: "rejected",
        rejectedAt: new Date().toISOString(),
        rejectedBy: "我",
        rejectedReason: rejectReason,
        rejectedTo: rejectTarget || "派单员",
        updatedAt: new Date().toISOString()
      }

      onTicketUpdate?.(ticket.id, updates)

      toast.success(`工单 ${ticket.id} 已退回`, {
        description: `已退回给${rejectTarget || "派单员"}，退回原因：${rejectReason}`
      })

      // 重置状态并关闭预览
      setRejectDialogOpen(false)
      setRejectReason("")
      setRejectTarget("")
      onClose()

    } catch (error) {
      console.error("退回工单失败:", error)
      toast.error("退回工单失败", {
        description: "请稍后重试或联系系统管理员。"
      })
    } finally {
      setIsRejecting(false)
    }
  }

  const handleAddComment = () => {
    console.log("添加评论")
  }

  return (
    <div className="fixed right-0 top-0 h-screen w-96 bg-white dark:bg-gray-900
                    shadow-xl border-l border-gray-200 dark:border-gray-700
                    flex flex-col z-50
                    transform transition-transform duration-300 ease-in-out
                    animate-in slide-in-from-right
                    sm:w-96 w-full sm:max-w-none max-w-full">
      {/* 头部 */}
      <div className="p-4 border-b bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-semibold">工单详情</h3>
            <Badge variant="outline" className="text-xs">
              快速预览
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" title="查看完整详情">
              <ExternalLink className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose} title="关闭预览 (ESC)">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="outline" className="font-mono text-xs">
                  {ticket.id}
                </Badge>
                <Button variant="link" size="sm" className="p-0 h-auto">
                  <ExternalLink className="h-3 w-3 mr-1" />
                  详情页
                </Button>
              </div>
              <h4 className="font-medium text-gray-900 dark:text-white leading-relaxed">
                {ticket.title}
              </h4>
            </div>

            {/* 状态和优先级 */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${getStatusColor(ticket.status)}`}></div>
                <span className="text-sm">{getStatusLabel(ticket.status)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${getPriorityColor(ticket.priority)}`}></div>
                <span className="text-sm font-medium">{getPriorityLabel(ticket.priority)}</span>
              </div>
            </div>

            {/* 标签 */}
            <div className="flex flex-wrap gap-1">
              {ticket.tags.map((tag: string) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>

          <Separator />

          {/* 客户信息 */}
          <div className="space-y-3">
            <h5 className="font-medium text-gray-900 dark:text-white flex items-center">
              <User className="h-4 w-4 mr-2" />
              客户信息
            </h5>
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="text-sm">
                    {ticket.customer.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{ticket.customer.name}</p>
                  <Badge variant="outline" className="text-xs">
                    {ticket.customer.level}
                  </Badge>
                </div>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Building className="h-3 w-3" />
                <span>{ticket.customer.company}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Phone className="h-3 w-3" />
                <span>{ticket.customer.phone}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* 时间信息 */}
          <div className="space-y-3">
            <h5 className="font-medium text-gray-900 dark:text-white flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              时间信息
            </h5>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">创建时间:</span>
                <span>{new Date(ticket.createdAt).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">更新时间:</span>
                <span>{new Date(ticket.updatedAt).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">SLA剩余:</span>
                <span className={`font-medium ${getSlaStatusColor(ticket.slaStatus)}`}>
                  {ticket.slaRemaining}
                </span>
              </div>
            </div>
          </div>

          <Separator />

          {/* 处理记录 */}
          <div className="space-y-3">
            <h5 className="font-medium text-gray-900 dark:text-white flex items-center">
              <MessageSquare className="h-4 w-4 mr-2" />
              处理记录
            </h5>
            <div className="space-y-3">
              {comments.map((comment) => (
                <div key={comment.id} className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback className="text-xs">
                        {comment.author.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm font-medium">{comment.author}</span>
                    <Badge variant={comment.type === "customer" ? "outline" : "secondary"} className="text-xs">
                      {comment.type === "customer" ? "客户" : "处理人"}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300 ml-8">
                    {comment.content}
                  </p>
                  <p className="text-xs text-muted-foreground ml-8">
                    {new Date(comment.timestamp).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 底部操作区 */}
      <div className="p-4 border-t bg-gray-50 dark:bg-gray-800 space-y-3">
        {/* 快速评论 */}
        <div className="space-y-2">
          <Textarea
            placeholder="添加处理记录..."
            rows={2}
            className="resize-none"
          />
          <Button size="sm" onClick={handleAddComment} className="w-full">
            <Send className="h-3 w-3 mr-2" />
            添加记录
          </Button>
        </div>

        {/* 快速操作按钮 */}
        <div className="grid grid-cols-2 gap-2">
          {ticket.status === "pending" && (
            <>
              <Button
                size="sm"
                onClick={() => handleQuickAction("accept")}
                disabled={isAccepting}
                className="bg-green-600 hover:bg-green-700"
              >
                {isAccepting ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                    接收中...
                  </>
                ) : (
                  "接收"
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction("reject")}
                disabled={isRejecting}
                className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
              >
                {isRejecting ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600 mr-2"></div>
                    退回中...
                  </>
                ) : (
                  "退回"
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction("transfer")}
                disabled={isTransferring}
                className="border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
              >
                {isTransferring ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2"></div>
                    转办中...
                  </>
                ) : (
                  "转办"
                )}
              </Button>
            </>
          )}
          
          {ticket.status === "in_progress" && (
            <>
              <Button size="sm" onClick={() => handleQuickAction("complete")}>
                办结
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction("collaborate")}
                disabled={isCollaborating}
                className="border-green-200 text-green-600 hover:bg-green-50 hover:text-green-700"
              >
                {isCollaborating ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-green-600 mr-2"></div>
                    邀请中...
                  </>
                ) : (
                  "邀请协办"
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={() => handleQuickAction("suspend")}>
                挂起
              </Button>
            </>
          )}
          
          {ticket.status === "completed" && (
            <>
              <Button variant="outline" size="sm" onClick={() => handleQuickAction("reopen")}>
                重新打开
              </Button>
              <Button variant="outline" size="sm" onClick={() => handleQuickAction("archive")}>
                归档
              </Button>
            </>
          )}
        </div>
      </div>

      {/* 退回工单对话框 */}
      <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>退回工单</DialogTitle>
            <DialogDescription>
              请填写退回原因，工单将被退回给指定人员重新处理。
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* 工单信息 */}
            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <p className="font-medium">工单ID: {ticket.id}</p>
              <p className="text-sm text-muted-foreground mt-1">{ticket.title}</p>
            </div>

            {/* 退回目标 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">退回给</label>
              <Select value={rejectTarget} onValueChange={setRejectTarget}>
                <SelectTrigger>
                  <SelectValue placeholder="选择退回目标（默认：派单员）" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="派单员">派单员</SelectItem>
                  <SelectItem value="客服组长">客服组长</SelectItem>
                  <SelectItem value="质量管理员">质量管理员</SelectItem>
                  <SelectItem value="系统管理员">系统管理员</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 退回原因 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                退回原因 <span className="text-red-500">*</span>
              </label>
              <Textarea
                placeholder="请详细说明退回的原因..."
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                rows={3}
                className="resize-none"
              />
            </div>

            {/* 常用退回原因快捷选择 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">常用原因</label>
              <div className="flex flex-wrap gap-2">
                {[
                  "信息不完整",
                  "描述不清楚",
                  "缺少必要附件",
                  "不属于我的职责范围"
                ].map((reason) => (
                  <Button
                    key={reason}
                    variant="outline"
                    size="sm"
                    onClick={() => setRejectReason(reason)}
                    className="text-xs h-7"
                  >
                    {reason}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setRejectDialogOpen(false)}
              disabled={isRejecting}
            >
              取消
            </Button>
            <Button
              onClick={handleRejectTicket}
              disabled={!rejectReason.trim() || isRejecting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isRejecting ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  退回中...
                </>
              ) : (
                "确认退回"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 转办工单对话框 */}
      <Dialog open={transferDialogOpen} onOpenChange={setTransferDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>转办工单</DialogTitle>
            <DialogDescription>
              将工单转办给其他人员处理，请选择转办目标并填写转办说明。
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* 工单信息 */}
            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <p className="font-medium">工单ID: {ticket.id}</p>
              <p className="text-sm text-muted-foreground mt-1">{ticket.title}</p>
            </div>

            {/* 转办目标 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                转办给 <span className="text-red-500">*</span>
              </Label>
              <Select value={transferTarget} onValueChange={setTransferTarget}>
                <SelectTrigger>
                  <SelectValue placeholder="选择转办目标" />
                </SelectTrigger>
                <SelectContent>
                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                    IT支持部
                  </div>
                  <SelectItem value="张工程师">张工程师</SelectItem>
                  <SelectItem value="李工程师">李工程师</SelectItem>
                  <SelectItem value="王工程师">王工程师</SelectItem>

                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                    网络部
                  </div>
                  <SelectItem value="网络管理员">网络管理员</SelectItem>
                  <SelectItem value="网络工程师">网络工程师</SelectItem>

                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                    客服部
                  </div>
                  <SelectItem value="客服组长">客服组长</SelectItem>
                  <SelectItem value="高级客服">高级客服</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 转办原因 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">转办说明</Label>
              <Textarea
                placeholder="请说明转办原因或需要协助的内容..."
                value={transferReason}
                onChange={(e) => setTransferReason(e.target.value)}
                rows={3}
                className="resize-none"
              />
            </div>

            {/* 紧急转办标记 */}
            <div className="flex items-center space-x-3">
              <Switch
                checked={transferUrgent}
                onCheckedChange={setTransferUrgent}
              />
              <div>
                <Label className="text-sm font-medium">标记为紧急转办</Label>
                <p className="text-xs text-muted-foreground">
                  将添加"紧急转办"标签
                </p>
              </div>
            </div>

            {/* 常用转办原因快捷选择 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">常用转办原因</Label>
              <div className="flex flex-wrap gap-2">
                {[
                  "需要专业技术支持",
                  "超出我的权限范围",
                  "需要其他部门配合",
                  "专业领域不匹配"
                ].map((reason) => (
                  <Button
                    key={reason}
                    variant="outline"
                    size="sm"
                    onClick={() => setTransferReason(reason)}
                    className="text-xs h-7"
                  >
                    {reason}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setTransferDialogOpen(false)}
              disabled={isTransferring}
            >
              取消
            </Button>
            <Button
              onClick={handleTransferTicket}
              disabled={!transferTarget.trim() || isTransferring}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isTransferring ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  转办中...
                </>
              ) : (
                "确认转办"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 邀请协办对话框 */}
      <Dialog open={collaborateDialogOpen} onOpenChange={setCollaborateDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>邀请协办</DialogTitle>
            <DialogDescription>
              邀请其他人员协助处理此工单，您仍然是主要处理人。
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* 工单信息 */}
            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <p className="font-medium">工单ID: {ticket.id}</p>
              <p className="text-sm text-muted-foreground mt-1">{ticket.title}</p>
            </div>

            {/* 已选择的协办人员 */}
            {collaborators.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">已选择的协办人员</Label>
                <div className="flex flex-wrap gap-2">
                  {collaborators.map((collaborator) => (
                    <Badge key={collaborator} variant="secondary" className="flex items-center space-x-1">
                      <span>{collaborator}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCollaborator(collaborator)}
                        className="h-auto p-0 ml-1"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* 选择协办人员 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                选择协办人员 <span className="text-red-500">*</span>
              </Label>
              <Select onValueChange={addCollaborator}>
                <SelectTrigger>
                  <SelectValue placeholder="选择协办人员" />
                </SelectTrigger>
                <SelectContent>
                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                    IT支持部
                  </div>
                  {["张工程师", "李工程师", "王工程师"]
                    .filter(person => !collaborators.includes(person))
                    .map((person) => (
                      <SelectItem key={person} value={person}>
                        {person}
                      </SelectItem>
                    ))}

                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                    网络部
                  </div>
                  {["网络管理员", "网络工程师"]
                    .filter(person => !collaborators.includes(person))
                    .map((person) => (
                      <SelectItem key={person} value={person}>
                        {person}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            {/* 协办权限设置 */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">协办权限</Label>
              <div className="space-y-2">
                {[
                  { value: "view", label: "查看工单", description: "可以查看工单详情" },
                  { value: "comment", label: "添加评论", description: "可以添加处理记录" },
                  { value: "edit", label: "编辑工单", description: "可以修改工单信息" }
                ].map((permission) => (
                  <div key={permission.value} className="flex items-start space-x-3">
                    <Checkbox
                      checked={collaboratePermissions.includes(permission.value)}
                      onCheckedChange={() => togglePermission(permission.value)}
                      disabled={permission.value === "view"} // 查看权限必选
                    />
                    <div className="flex-1">
                      <Label className="text-sm font-medium">{permission.label}</Label>
                      <p className="text-xs text-muted-foreground">{permission.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 邀请消息 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">邀请消息</Label>
              <Textarea
                placeholder="请说明需要协办的内容..."
                value={collaborateMessage}
                onChange={(e) => setCollaborateMessage(e.target.value)}
                rows={2}
                className="resize-none"
              />
            </div>

            {/* 常用邀请原因快捷选择 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">常用原因</Label>
              <div className="flex flex-wrap gap-2">
                {[
                  "需要技术支持",
                  "需要专业意见",
                  "复杂问题需要协助",
                  "质量把关"
                ].map((reason) => (
                  <Button
                    key={reason}
                    variant="outline"
                    size="sm"
                    onClick={() => setCollaborateMessage(reason)}
                    className="text-xs h-7"
                  >
                    {reason}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setCollaborateDialogOpen(false)}
              disabled={isCollaborating}
            >
              取消
            </Button>
            <Button
              onClick={handleCollaborateTicket}
              disabled={collaborators.length === 0 || isCollaborating}
              className="bg-green-600 hover:bg-green-700"
            >
              {isCollaborating ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  邀请中...
                </>
              ) : (
                "发送邀请"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
