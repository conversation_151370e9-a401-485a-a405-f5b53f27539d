"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { X } from "lucide-react"
import { 
  getPriorityColor, 
  getPriorityLabel, 
  personnelOptions, 
  commonCollaborateReasons,
  collaboratePermissions as permissionOptions
} from "./ticket-utils"

interface CollaborateTicketDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ticket: any
  collaborators: string[]
  collaborateMessage: string
  collaboratePermissions: string[]
  isCollaborating: boolean
  onCollaborateMessageChange: (message: string) => void
  onAddCollaborator: (collaborator: string) => void
  onRemoveCollaborator: (collaborator: string) => void
  onTogglePermission: (permission: string) => void
  onConfirm: () => void
}

/**
 * 邀请协办对话框组件
 */
export function CollaborateTicketDialog({
  open,
  onOpenChange,
  ticket,
  collaborators,
  collaborateMessage,
  collaboratePermissions,
  isCollaborating,
  onCollaborateMessageChange,
  onAddCollaborator,
  onRemoveCollaborator,
  onTogglePermission,
  onConfirm
}: CollaborateTicketDialogProps) {
  if (!ticket) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>邀请协办</DialogTitle>
          <DialogDescription>
            邀请其他人员协助处理此工单，您仍然是主要处理人。
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 pr-2">
          {/* 工单信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <p className="font-medium">工单ID: {ticket.id}</p>
            <p className="text-sm text-muted-foreground mt-1">{ticket.title}</p>
            <div className="flex items-center space-x-2 mt-2">
              <div className={`w-3 h-3 rounded-full ${getPriorityColor(ticket.priority)}`}></div>
              <span className="text-sm">{getPriorityLabel(ticket.priority)}优先级</span>
              <span className="text-sm text-muted-foreground">
                SLA剩余: {ticket.slaRemaining}
              </span>
            </div>
          </div>

          {/* 已选择的协办人员 */}
          {collaborators.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">已选择的协办人员</Label>
              <div className="flex flex-wrap gap-2">
                {collaborators.map((collaborator) => (
                  <Badge key={collaborator} variant="secondary" className="flex items-center space-x-1">
                    <span>{collaborator}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onRemoveCollaborator(collaborator)}
                      className="h-auto p-0 ml-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* 选择协办人员 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              选择协办人员 <span className="text-red-500">*</span>
            </Label>
            <Select onValueChange={onAddCollaborator}>
              <SelectTrigger>
                <SelectValue placeholder="选择协办人员" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(personnelOptions).map(([department, people]) => (
                  <div key={department}>
                    <div className="px-2 py-1 text-xs font-medium text-muted-foreground bg-gray-100 dark:bg-gray-800">
                      {department}
                    </div>
                    {people
                      .filter(person => !collaborators.includes(person))
                      .map((person) => (
                        <SelectItem key={person} value={person}>
                          {person}
                        </SelectItem>
                      ))}
                  </div>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 协办权限设置 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">协办权限</Label>
            <div className="space-y-2">
              {permissionOptions.map((permission) => (
                <div key={permission.value} className="flex items-start space-x-3">
                  <Checkbox
                    checked={collaboratePermissions.includes(permission.value)}
                    onCheckedChange={() => onTogglePermission(permission.value)}
                    disabled={permission.value === "view"} // 查看权限必选
                  />
                  <div className="flex-1">
                    <Label className="text-sm font-medium">{permission.label}</Label>
                    <p className="text-xs text-muted-foreground">{permission.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 邀请消息 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">邀请消息</Label>
            <Textarea
              placeholder="请说明需要协办的内容或期望的协助..."
              value={collaborateMessage}
              onChange={(e) => onCollaborateMessageChange(e.target.value)}
              rows={3}
              className="resize-none"
            />
          </div>

          {/* 常用邀请原因快捷选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">常用邀请原因</Label>
            <div className="flex flex-wrap gap-2">
              {commonCollaborateReasons.map((reason) => (
                <Button
                  key={reason}
                  variant="outline"
                  size="sm"
                  onClick={() => onCollaborateMessageChange(reason)}
                  className="text-xs h-7"
                >
                  {reason}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter className="flex-shrink-0 flex-col sm:flex-row gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isCollaborating}
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            disabled={collaborators.length === 0 || isCollaborating}
            className="bg-green-600 hover:bg-green-700"
          >
            {isCollaborating ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                邀请中...
              </>
            ) : (
              "发送邀请"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
