"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getPriorityColor, getPriorityLabel, commonRejectReasons } from "./ticket-utils"

interface RejectTicketDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ticket: any
  rejectReason: string
  rejectTarget: string
  isRejecting: boolean
  onRejectReasonChange: (reason: string) => void
  onRejectTargetChange: (target: string) => void
  onConfirm: () => void
}

/**
 * 退回工单对话框组件
 */
export function RejectTicketDialog({
  open,
  onOpenChange,
  ticket,
  rejectReason,
  rejectTarget,
  isRejecting,
  onRejectReasonChange,
  onRejectTargetChange,
  onConfirm
}: RejectTicketDialogProps) {
  if (!ticket) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>退回工单</DialogTitle>
          <DialogDescription>
            请填写退回原因，工单将被退回给指定人员重新处理。
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 工单信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <p className="font-medium">工单ID: {ticket.id}</p>
            <p className="text-sm text-muted-foreground mt-1">{ticket.title}</p>
            <div className="flex items-center space-x-2 mt-2">
              <div className={`w-3 h-3 rounded-full ${getPriorityColor(ticket.priority)}`}></div>
              <span className="text-sm">{getPriorityLabel(ticket.priority)}优先级</span>
            </div>
          </div>

          {/* 退回目标 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">退回给</label>
            <Select value={rejectTarget} onValueChange={onRejectTargetChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择退回目标（默认：派单员）" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="派单员">派单员</SelectItem>
                <SelectItem value="客服组长">客服组长</SelectItem>
                <SelectItem value="质量管理员">质量管理员</SelectItem>
                <SelectItem value="系统管理员">系统管理员</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 退回原因 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              退回原因 <span className="text-red-500">*</span>
            </label>
            <Textarea
              placeholder="请详细说明退回的原因..."
              value={rejectReason}
              onChange={(e) => onRejectReasonChange(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <p className="text-xs text-muted-foreground">
              请详细说明退回原因，以便相关人员了解问题并重新处理
            </p>
          </div>

          {/* 常用退回原因快捷选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">常用原因</label>
            <div className="flex flex-wrap gap-2">
              {commonRejectReasons.map((reason) => (
                <Button
                  key={reason}
                  variant="outline"
                  size="sm"
                  onClick={() => onRejectReasonChange(reason)}
                  className="text-xs h-7"
                >
                  {reason}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isRejecting}
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            disabled={!rejectReason.trim() || isRejecting}
            className="bg-red-600 hover:bg-red-700"
          >
            {isRejecting ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                退回中...
              </>
            ) : (
              "确认退回"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
