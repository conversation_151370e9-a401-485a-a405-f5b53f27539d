"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Mail, User, Clock, AlertCircle, Shield, Users } from "lucide-react"
import { 
  getPriorityColor, 
  getPriorityLabel,
  getSlaStatusColor,
  ccTypes,
  ccPermissions,
  ccRecipients,
  commonCcReasons
} from "./ticket-utils"

interface CcTicketDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ticket: any
  ccType: string
  ccRecipients: string[]
  ccMessage: string
  ccPermissions: string[]
  isSending: boolean
  onCcTypeChange: (type: string) => void
  onCcRecipientsChange: (recipients: string[]) => void
  onCcMessageChange: (message: string) => void
  onCcPermissionsChange: (permissions: string[]) => void
  onConfirm: () => void
}

/**
 * 抄送工单对话框组件
 */
export function CcTicketDialog({
  open,
  onOpenChange,
  ticket,
  ccType,
  ccRecipients: selectedRecipients,
  ccMessage,
  ccPermissions: selectedPermissions,
  isSending,
  onCcTypeChange,
  onCcRecipientsChange,
  onCcMessageChange,
  onCcPermissionsChange,
  onConfirm
}: CcTicketDialogProps) {
  if (!ticket) return null

  const handleRecipientToggle = (recipientId: string) => {
    const newRecipients = selectedRecipients.includes(recipientId)
      ? selectedRecipients.filter(id => id !== recipientId)
      : [...selectedRecipients, recipientId]
    onCcRecipientsChange(newRecipients)
  }

  const handlePermissionToggle = (permission: string) => {
    const newPermissions = selectedPermissions.includes(permission)
      ? selectedPermissions.filter(p => p !== permission)
      : [...selectedPermissions, permission]
    onCcPermissionsChange(newPermissions)
  }

  const selectedRecipientsData = ccRecipients.filter(r => selectedRecipients.includes(r.id))

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <Mail className="h-5 w-5 text-blue-600" />
            <span>抄送工单</span>
          </DialogTitle>
          <DialogDescription>
            将工单信息抄送给相关人员，让他们了解工单进展情况。
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 pr-2">
          {/* 工单信息 */}
          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <p className="font-medium">工单ID: {ticket.id}</p>
              <Badge variant="outline" className={`${getPriorityColor(ticket.priority)} text-white border-0`}>
                {getPriorityLabel(ticket.priority)}优先级
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">{ticket.title}</p>
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span className={getSlaStatusColor(ticket.slaStatus)}>
                    SLA剩余: {ticket.slaRemaining}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3" />
                  <span>处理人: {ticket.assignee}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 抄送类型 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              抄送类型 <span className="text-red-500">*</span>
            </Label>
            <Select value={ccType} onValueChange={onCcTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择抄送类型" />
              </SelectTrigger>
              <SelectContent>
                {ccTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="space-y-1">
                      <div className="font-medium">{type.label}</div>
                      <div className="text-xs text-muted-foreground">{type.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 抄送人员选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              抄送人员 <span className="text-red-500">*</span>
            </Label>
            <div className="border rounded-lg p-3">
              <ScrollArea className="h-48">
                <div className="space-y-2">
                  {ccRecipients.map((recipient) => (
                    <div key={recipient.id} className="flex items-center space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded">
                      <Checkbox
                        id={recipient.id}
                        checked={selectedRecipients.includes(recipient.id)}
                        onCheckedChange={() => handleRecipientToggle(recipient.id)}
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{recipient.name}</span>
                          <Badge variant="secondary" className="text-xs">
                            {recipient.role}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">{recipient.department}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
            
            {/* 已选择的人员预览 */}
            {selectedRecipientsData.length > 0 && (
              <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Users className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">已选择 {selectedRecipientsData.length} 人</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {selectedRecipientsData.map((recipient) => (
                    <Badge key={recipient.id} variant="outline" className="text-xs">
                      {recipient.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 权限设置 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4" />
                <span>抄送权限</span>
              </div>
            </Label>
            <div className="border rounded-lg p-3">
              <div className="grid grid-cols-1 gap-2">
                {ccPermissions.map((permission) => (
                  <div key={permission.value} className="flex items-start space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded">
                    <Checkbox
                      id={permission.value}
                      checked={selectedPermissions.includes(permission.value)}
                      onCheckedChange={() => handlePermissionToggle(permission.value)}
                    />
                    <div className="flex-1">
                      <div className="font-medium text-sm">{permission.label}</div>
                      <p className="text-xs text-muted-foreground">{permission.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 抄送消息 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              抄送消息 <span className="text-red-500">*</span>
            </Label>
            <Textarea
              placeholder="请说明抄送原因和需要关注的重点..."
              value={ccMessage}
              onChange={(e) => onCcMessageChange(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          {/* 常用抄送原因快捷选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">常用抄送原因</Label>
            <div className="flex flex-wrap gap-2">
              {commonCcReasons.map((reason) => (
                <Button
                  key={reason}
                  variant="outline"
                  size="sm"
                  onClick={() => onCcMessageChange(reason)}
                  className="text-xs h-7"
                >
                  {reason}
                </Button>
              ))}
            </div>
          </div>

          {/* 注意事项 */}
          <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
              <div className="text-xs space-y-1">
                <p className="font-medium text-amber-800 dark:text-amber-200">注意事项：</p>
                <ul className="text-amber-700 dark:text-amber-300 space-y-0.5">
                  <li>• 抄送人员将收到工单信息通知</li>
                  <li>• 根据权限设置，抄送人员可以查看相应信息</li>
                  <li>• 抄送记录将保存在工单历史中</li>
                  <li>• 请谨慎选择抄送人员，避免信息泄露</li>
                  <li>• 抄送人员可能会收到后续工单更新通知</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex-shrink-0 flex-col sm:flex-row gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSending}
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            disabled={
              !ccType || 
              selectedRecipients.length === 0 || 
              !ccMessage.trim() || 
              isSending
            }
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSending ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                发送中...
              </>
            ) : (
              "发送抄送"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
