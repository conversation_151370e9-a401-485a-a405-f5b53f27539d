"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { TicketFilters } from "./_components/ticket-filters"
import { TicketTable } from "./_components/ticket-table"
import { BatchOperations } from "./_components/batch-operations"
import { QuickPreviewPanel } from "./_components/quick-preview-panel"
import { 
  Search, 
  Filter, 
  RefreshCw,
  Settings,
  Save,
  Eye
} from "lucide-react"

/**
 * 我的工单页面
 * 提供工单列表查看、筛选、批量操作等功能
 */
export default function MyTicketsPage() {
  const [activeTab, setActiveTab] = useState("pending")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTickets, setSelectedTickets] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const [previewTicket, setPreviewTicket] = useState<any>(null)
  const [filters, setFilters] = useState({
    status: [],
    priority: [],
    dateRange: "",
    tags: [],
    template: ""
  })

  // 模拟工单数据
  const [tickets, setTickets] = useState([
    {
      id: "TK-2024-001",
      title: "办公电脑无法启动，显示蓝屏错误",
      status: "pending",
      priority: "urgent",
      customer: {
        name: "张三",
        company: "阿里巴巴",
        level: "VIP",
        phone: "138****1234"
      },
      assignee: "李工程师",
      createdAt: "2024-01-20 09:30:00",
      updatedAt: "2024-01-20 14:15:00",
      slaRemaining: "2小时30分钟",
      slaStatus: "warning",
      tags: ["硬件故障", "紧急"],
      template: "IT设备报修"
    },
    {
      id: "TK-2024-002", 
      title: "新员工入职需要开通系统账号权限",
      status: "in_progress",
      priority: "medium",
      customer: {
        name: "王五",
        company: "腾讯科技",
        level: "企业",
        phone: "139****5678"
      },
      assignee: "我",
      createdAt: "2024-01-19 16:20:00",
      updatedAt: "2024-01-20 10:45:00",
      slaRemaining: "1天6小时",
      slaStatus: "normal",
      tags: ["权限申请"],
      template: "权限申请"
    },
    {
      id: "TK-2024-003",
      title: "客户投诉产品质量问题要求退款",
      status: "completed",
      priority: "high",
      customer: {
        name: "赵六",
        company: "百度",
        level: "标准",
        phone: "137****9012"
      },
      assignee: "我",
      createdAt: "2024-01-18 11:15:00",
      updatedAt: "2024-01-19 17:30:00",
      slaRemaining: "已完成",
      slaStatus: "completed",
      tags: ["客户投诉", "退款"],
      template: "客户投诉"
    }
  ])

  // 根据标签页筛选工单
  const getFilteredTickets = () => {
    let filtered = tickets

    // 按标签页筛选
    switch (activeTab) {
      case "pending":
        filtered = filtered.filter(t => t.status === "pending")
        break
      case "in_progress":
        filtered = filtered.filter(t => t.status === "in_progress")
        break
      case "completed":
        filtered = filtered.filter(t => t.status === "completed")
        break
      case "all":
      default:
        // 显示所有工单
        break
    }

    // 搜索筛选
    if (searchQuery) {
      filtered = filtered.filter(ticket =>
        ticket.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.customer.phone.includes(searchQuery)
      )
    }

    // 高级筛选
    if (filters.priority.length > 0) {
      filtered = filtered.filter(t => filters.priority.includes(t.priority))
    }

    if (filters.tags.length > 0) {
      filtered = filtered.filter(t => 
        t.tags.some(tag => filters.tags.includes(tag))
      )
    }

    return filtered
  }

  const filteredTickets = getFilteredTickets()

  // 获取各标签页的数量
  const getTabCounts = () => {
    return {
      pending: tickets.filter(t => t.status === "pending").length,
      in_progress: tickets.filter(t => t.status === "in_progress").length,
      completed: tickets.filter(t => t.status === "completed").length,
      all: tickets.length
    }
  }

  const tabCounts = getTabCounts()

  const handleSelectTicket = (ticketId: string, selected: boolean) => {
    if (selected) {
      setSelectedTickets(prev => [...prev, ticketId])
    } else {
      setSelectedTickets(prev => prev.filter(id => id !== ticketId))
    }
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedTickets(filteredTickets.map(t => t.id))
    } else {
      setSelectedTickets([])
    }
  }

  const handlePreviewTicket = (ticket: any) => {
    setPreviewTicket(ticket)
  }

  const handleRefresh = () => {
    console.log("刷新工单列表")
  }

  const handleTicketUpdate = (ticketId: string, updates: any) => {
    setTickets(prevTickets =>
      prevTickets.map(ticket =>
        ticket.id === ticketId
          ? { ...ticket, ...updates }
          : ticket
      )
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* 主要内容区域 - 现在占满全宽 */}
      <div className="space-y-6">
          {/* 页面头部 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
                我的工单
              </h1>
              <p className="text-muted-foreground mt-2">
                管理和处理分配给我的工单任务
              </p>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                视图设置
              </Button>
            </div>
          </div>

          {/* 筛选与搜索区 */}
          <Card>
            <CardContent className="p-6">
              {/* 标签页 */}
              <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="pending" className="relative">
                    待我处理
                    {tabCounts.pending > 0 && (
                      <Badge variant="destructive" className="ml-2 text-xs">
                        {tabCounts.pending}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="in_progress" className="relative">
                    处理中
                    {tabCounts.in_progress > 0 && (
                      <Badge variant="default" className="ml-2 text-xs">
                        {tabCounts.in_progress}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="completed" className="relative">
                    已完成
                    {tabCounts.completed > 0 && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {tabCounts.completed}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="all" className="relative">
                    全部
                    <Badge variant="outline" className="ml-2 text-xs">
                      {tabCounts.all}
                    </Badge>
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              {/* 搜索和筛选 */}
              <div className="flex items-center space-x-4">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="搜索工单ID、标题、客户姓名或电话..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Button
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                  className={showFilters ? "bg-blue-50 border-blue-200" : ""}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  高级筛选
                </Button>

                <Button variant="outline">
                  <Save className="h-4 w-4 mr-2" />
                  保存视图
                </Button>
              </div>

              {/* 高级筛选器 */}
              {showFilters && (
                <div className="mt-4 pt-4 border-t">
                  <TicketFilters
                    filters={filters}
                    onChange={setFilters}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* 批量操作区 */}
          {selectedTickets.length > 0 && (
            <BatchOperations
              selectedCount={selectedTickets.length}
              onClear={() => setSelectedTickets([])}
            />
          )}

          {/* 工单列表 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>工单列表</span>
                <Badge variant="outline">
                  {filteredTickets.length} 个工单
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TicketTable
                tickets={filteredTickets}
                selectedTickets={selectedTickets}
                onSelectTicket={handleSelectTicket}
                onSelectAll={handleSelectAll}
                onPreviewTicket={handlePreviewTicket}
                onTicketUpdate={handleTicketUpdate}
              />
            </CardContent>
          </Card>
      </div>

      {/* 弹出式预览面板 */}
      {previewTicket && (
        <>
          {/* 半透明遮罩层 */}
          <div
            className="fixed inset-0 bg-black/20 z-40"
            onClick={() => setPreviewTicket(null)}
          />
          {/* 预览面板 */}
          <QuickPreviewPanel
            ticket={previewTicket}
            onClose={() => setPreviewTicket(null)}
            onTicketUpdate={handleTicketUpdate}
          />
        </>
      )}
    </div>
  )
}
